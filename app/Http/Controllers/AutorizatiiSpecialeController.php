<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AutorizatiiSpeciale; 
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class AutorizatiiSpecialeController extends Controller
{
    public function autorizatiiSpeciale() {
        return view('autorizatiiSpeciale.create');
    }


    public function index()
    {
        $columns = [
            'id' => 'ID',
            'tip_autorizatie' => 'TIP Autorizatie',
            'nr_serie' => 'NR. SERIE',
            'nume' => 'NUME',
            'prenume' => 'PRENUME',
            'functie' => 'FUNCȚIE',
            'unitate' => 'UNITATE',
            'ministru' => 'MINISTRU',
            'data_emitere' => 'DATĂ EMITERE',
            'data_valabilitate' => 'DATĂ VALABILITATE',
            'poza' => 'POZĂ'
        ];

        $selectColumns = array_keys($columns); 

        $query = AutorizatiiSpeciale::select(array_keys($columns))->orderBy('id', 'DESC');


        $dateTabel = $query->paginate(10);

        $menus = config('customVariables.menus');

        return view('autorizatiiSpeciale.vizualizare', compact('menus', 'dateTabel', 'columns'));
    }


    public function store(Request $request){
        // dd($request->all());
        Log::info('pozaaa' . $request);
        $request->validate([
            'tip_autorizatie' => 'required|string|max:255',
            'nr_serie' => 'required|string|max:255',
            'nume' => 'required|string|max:255',
            'prenume' => 'required|string|max:255',
            'functie' => 'required|string|max:255',
            'unitate' => 'required|string|max:255',
            'ministru' => 'required|string|max:255',
            'data_emitere' => 'required',
            'data_valabilitate' => 'required',
            'poza' => 'nullable',
        ]);
    
        try {
            Log::info('poza' . $request->poza);
            if ($request->has('poza')) {
                $imageData = $request->poza;

                if (strpos($imageData, 'data:image') === 0) {
                    $imageData = explode(',', $imageData)[1];
                }

                $image = base64_decode($imageData);

                if ($image === false) {
                    throw new \Exception('Imagine invalidă (nu s-a putut decoda)');
                }

                $imageName = uniqid() . '.png';
                $path = public_path('uploads/images/' . $imageName);

                file_put_contents($path, $image);
        
                $autorizatieSpeciala = AutorizatiiSpeciale::create([
                    'tip_autorizatie' => $request->tip_autorizatie,
                    'nr_serie' => $request->nr_serie,
                    'nume' => $request->nume,
                    'prenume' => $request->prenume,
                    'functie' => $request->functie,
                    'unitate' => $request->unitate,
                    'ministru' => $request->ministru,
                    'data_emitere' => $request->data_emitere,
                    'data_valabilitate' => $request->data_valabilitate,
                    'poza' => 'uploads/images/' . $imageName,
                ]);
            } else {
                $autorizatieSpeciala = AutorizatiiSpeciale::create([
                    'tip_autorizatie' => $request->tip_autorizatie,
                    'nr_serie' => $request->nr_serie,
                    'nume' => $request->nume,
                    'prenume' => $request->prenume,
                    'functie' => $request->functie,
                    'unitate' => $request->unitate,
                    'ministru' => $request->ministru,
                    'data_emitere' => $request->data_emitere,
                    'data_valabilitate' => $request->data_valabilitate,
                    'poza' => null,
                ]);
            }
        
            return response()->json(['message' => 'Autorizatie salvată']);
        } catch (\Exception $e) {
            Log::info('msj' . $e);
            return response()->json(['message' => 'Eroare server: ' . $e->getMessage()], 500);
        }
        
        
    }


    public function genereazaPdf($id)
    {
        $autorizatie = AutorizatiiSpeciale::findOrFail($id);
        $nume = $autorizatie->nume;
        $prenume = $autorizatie->prenume;

        $pdf = Pdf::loadView('autorizatiiSpeciale', compact('autorizatie'));

        return $pdf->download('autorizatieSpeciala_'.$nume.'_'.$prenume.'.pdf');
    }
}
