<?php

namespace App\Http\Controllers;

use App\Models\Cenafer;
use App\Models\Comisie;
use App\Models\isfuri;
use App\Models\Tarife;
use App\Models\UnitatiFeroviare;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Solicitari;
use Illuminate\Support\Facades\Log;
use App\Models\art8;
use Mockery\Undefined;

class ComunicareController extends Controller
{



    public function generatePersFizicaPDF($oameni)
    {
        Solicitari::where('oameni', $oameni)->update(['status' => 'COMUNICARE TIPARITA']);
        $solicitare = Solicitari::where('oameni', $oameni)->first();
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie)->first();
        $isf = Isfuri::where('contor', $solicitare->id_isf)->first();
        $art8 = art8::where('literaArticol', $solicitare->motiv_suspendare)->first();
        $textArticol = null;
        if ($art8) {
            $textArticol = $art8->textArticol;
        }

        $incetare_suspendare_nr_certificat = $solicitare->incetare_suspendare_nr_certificat;
        $incetare_suspendare_data_certificat = $solicitare->incetare_suspendare_data_certificat;
        $incetare_suspendare_eliberat_certificat = $solicitare->incetare_suspendare_eliberat_certificat;

        $incetare_suspendare_nr_aviz = $solicitare->incetare_suspendare_nr_aviz;
        $incetare_suspendare_data_aviz = $solicitare->incetare_suspendare_data_aviz;
        $incetare_suspendare_eliberat_aviz = $solicitare->incetare_suspendare_eliberat_aviz;
        $perioada_suspendare = null;
        if ($art8) {
            $perioada_suspendare = $art8->perioada_suspendare ?? $solicitare->perioada_suspendare;
        }



        $solicitariAnterioare = null;
        $tipuriCuSolicitariAnterioare = ['duplicate', 'incetaresuspendare', 'retragereautorizatie', 'schimbarenume', 'vizeperiodice', 'suspendare', 'reautorizare'];
        $idsAutorizatiiAnterioare = explode(',', $solicitare->ids_aut_anterioare);

        // solicitari anterioare pt type3
        if (in_array(strtolower($solicitare->tip_comisie), $tipuriCuSolicitariAnterioare) && $idsAutorizatiiAnterioare) {
            $solicitariAnterioare = [];

            foreach ($idsAutorizatiiAnterioare as $idSolicitareAnterioara) {
                $solicitareGasita = Solicitari::where('oameni', intval($idSolicitareAnterioara))->first();
                Log::info('solicitare gasita ' . $solicitareGasita->oameni);
                array_push($solicitariAnterioare, $solicitareGasita);
            }

        }
        // de gasit ce solicitari anterioare trebuie luate pt vize periodice
        $tipuriTaxe = ['examinare', 'reautorizare', 'reexaminare', 'vizeperiodice', 'duplicate', 'schimbarenume'];
        $tarifCalculat = 0;
        if (in_array(strtolower($solicitare->tip_comisie), $tipuriTaxe)) {
            if (in_array(strtolower($solicitare->tip_comisie), $tipuriCuSolicitariAnterioare) && $idsAutorizatiiAnterioare) {
                foreach ($idsAutorizatiiAnterioare as $idSolicitareAnterioara) {
                    $tarif = Tarife::where('tip_solicitare', $solicitare->tip_comisie)->first();
                    if ($comisie->urgent === 'Da') {
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore * $tarif->multiplier_tarif_urgenta;
                    } else {
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore;
                    }
                }
            } else {
                $tarif = Tarife::where('tip_solicitare', $solicitare->tip_comisie)->first();
                if ($comisie->urgent === 'Da') {
                    $tarifCalculat = $tarif->tarif_orar * $tarif->nr_ore * $tarif->multiplier_tarif_urgenta;
                } else {
                    $tarifCalculat = $tarif->tarif_orar * $tarif->nr_ore;
                }
            }

        }
        ;


        Log::info('solicitare' . $solicitare->tip_comisie);
        Log::info('comisie ' . $comisie->data_ISF);
        Log::info('comisie 1 ' . $comisie->cerere_uf_data);
        Log::info('perioada suspendare ' . $perioada_suspendare);

        // Log::info('comisie 2 ' . $solicitariAnterioare[0]->data_elib);
        // Log::info('comisie 3 ' . $solicitariAnterioare[0]->valabilitate);



        $pdf = Pdf::loadView('ComunicarePersFizica.' . $solicitare->tip_comisie, compact(
            'solicitare',
            'comisie',
            'isf',
            'solicitariAnterioare',
            'tarifCalculat',
            'art8',
            'textArticol',
            'incetare_suspendare_nr_certificat',
            'incetare_suspendare_data_certificat',
            'incetare_suspendare_eliberat_certificat',
            'incetare_suspendare_nr_aviz',
            'incetare_suspendare_data_aviz',
            'incetare_suspendare_eliberat_aviz',
            'perioada_suspendare'
        ));
        $pdfContent = $pdf->output();

        return response()->json([
            'status' => $solicitare->status,
            'pdf' => base64_encode($pdfContent)
        ]);
    }

    public function generatePersJuridicaPDF($oameni)
    {   // de adunat toate solicitarile cu acelasi id de grup si puse toate datele de la acestea in tabel
        $solicitare = Solicitari::where('oameni', $oameni)->first(); //tine oameni
        $id_solicitare_pj = $solicitare->id_solicitare_pj; // tine id-ul solicitarii PJ

        Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->update(['status' => 'COMUNICARE TIPARITA']);
        Log::info('id solicitare pj' . $id_solicitare_pj);

        $solicitari = Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->get(); // tine un array cu toate datele unde este acelasi id_solicitare_pj
        $comisie = Comisie::where('nr_comisie', $solicitari[0]->nr_comisie)->first(); // 
        $isf = Isfuri::where('contor', $solicitari[0]->id_isf)->first();
        //pentru fiecare $solicitari->oameni, gasim toate solicitarile cu where('tip_comisie', 'Examinare'), dam push in array ul solicitariAnterioare
        $unitate = UnitatiFeroviare::where('unitate', $solicitari[0]->unitate)->first();
        Log::info('unitate' . $unitate);
        $solicitariAnterioare = null;
        $tipuriCuSolicitariAnterioare = ['duplicate', 'incetaresuspendare', 'retragereautorizatie', 'schimbarenume', 'vizeperiodice', 'suspendare', 'reautorizare'];
        $idsAutorizatiiAnterioare = explode(',', $solicitare->ids_aut_anterioare);

        // solicitari anterioare pt type3
        $sus = ['suspendare'];
        $incetareSuspendare = ['incetaresuspendare'];

        if (in_array(strtolower($solicitare->tip_comisie), $tipuriCuSolicitariAnterioare) && $idsAutorizatiiAnterioare) {
            $solicitariAnterioare = [];
            foreach ($solicitari as $solicitareItem) {
                $solicitareGasita = Solicitari::where('oameni', intval($solicitareItem->id_aut_anterioara))->first();
                Log::info('solicitareGasita' . $solicitareGasita);

                if (in_array(strtolower($solicitare->tip_comisie), $sus)) {
                    Log::info('solicitareItem' . $solicitareItem);
                    $articol = art8::where('literaArticol', $solicitareItem->motiv_suspendare)->first();
                    Log::info('articol' . $articol);
                    $solicitareGasita->art8 = $articol->literaArticol;
                    $solicitareGasita->textArticol = $articol?->textArticol ?? null;

                    $solicitareGasita->perioada_suspendare = $articol->perioada_suspendare ?? $solicitareItem->perioada_suspendare;
                }
                if (in_array(strtolower($solicitare->tip_comisie), $incetareSuspendare)) {

                    $solicitareGasita->incetare_suspendare_nr_certificat = $solicitare->incetare_suspendare_nr_certificat;
                    $solicitareGasita->incetare_suspendare_data_certificat = $solicitare->incetare_suspendare_data_certificat;
                    $solicitareGasita->incetare_suspendare_eliberat_certificat = $solicitare->incetare_suspendare_eliberat_certificat;


                    $solicitareGasita->incetare_suspendare_nr_aviz = $solicitare->incetare_suspendare_nr_aviz;
                    $solicitareGasita->incetare_suspendare_data_aviz = $solicitare->incetare_suspendare_data_aviz;
                    $solicitareGasita->incetare_suspendare_eliberat_aviz = $solicitare->incetare_suspendare_eliberat_aviz;
                }

                array_push($solicitariAnterioare, $solicitareGasita);
            }
        }

        // if (in_array(strtolower($solicitare->tip_comisie), $tipuriCuSolicitariAnterioare) && $idsAutorizatiiAnterioare) {
        //     $solicitariAnterioare = [];
        //     foreach ($idsAutorizatiiAnterioare as $idSolicitareAnterioara) {
        //         $solicitareGasita = Solicitari::where('oameni', intval($idSolicitareAnterioara))->first();
        //         array_push($solicitariAnterioare, $solicitareGasita);
        //     }
        // }
        $tipuriTaxe = ['examinare', 'reautorizare', 'reexaminare', 'vizeperiodice', 'duplicate', 'schimbarenume'];
        $tarifCalculat = 0;

        if (in_array(strtolower($solicitari[0]->tip_comisie), $tipuriTaxe)) {
            $tarif = Tarife::where('tip_solicitare', $solicitari[0]->tip_comisie)->first();
            if (in_array(strtolower($solicitare->tip_comisie), $tipuriCuSolicitariAnterioare) && $idsAutorizatiiAnterioare) {
                foreach ($idsAutorizatiiAnterioare as $idSolicitareAnterioara) {
                    if ($comisie->urgent === 'Da') {
                        // Log::info('tarif inainte ids' . $tarifCalculat);
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore * $tarif->multiplier_tarif_urgenta;
                        // Log::info('tarif dupa ids' . $tarifCalculat);
                    } else {
                        Log::info('tarif inainte ids' . $tarifCalculat);
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore;
                        Log::info('tarif dupa ids' . $tarifCalculat);
                    }
                }
            } else {
                foreach ($solicitari as $solicitare) {
                    if ($comisie->urgent === 'Da') {
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore * $tarif->multiplier_tarif_urgenta;
                    } else {
                        Log::info('tarif inainte solicitari' . $tarifCalculat);
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore;
                        Log::info('tarif dupa solicitari' . $tarifCalculat);
                    }
                }
            }
        }
        ;


        $pdf = Pdf::loadView('ComunicarePersJuridica.' . $solicitari[0]->tip_comisie, compact('solicitari', 'solicitariAnterioare', 'comisie', 'isf', 'unitate', 'tarifCalculat'));
        $pdfContent = $pdf->output();

        return response()->json([
            'status' => 'COMUNICARE TIPARITA',
            'pdf' => base64_encode($pdfContent)
        ]);

        //de trecut prin toate solicitarile si păus tabel la fiecare in parte
    }

    public function validareComunicarePf($oameni)
    {
        // examinare, reexaminare
        $solicitare = Solicitari::where('oameni', $oameni);
        $solicitareData = $solicitare->first();

        $updatedStatus = 'COMUNICARE VALIDATA';

        switch (strtolower($solicitareData->tip_comisie)) {
            case 'duplicate':
            case 'schimbarenume':
            case 'vizeperiodice':
                $updatedStatus = 'NR AUTORIZARE INTRODUS';
                break;
            case 'suspendare':
            case 'incetaresuspendare':
            case 'retragereautorizatie':
                $updatedStatus = 'AVIZE GENERATE';
                break;
            default:
                $updatedStatus = 'COMUNICARE VALIDATA';

        }

        $solicitare->update(['status' => $updatedStatus]);

        return response()->json([
            'requestStatus' => 200,
            'status' => $updatedStatus,
        ]);
    }

    public function validareComunicarePj($id_solicitare_pj)
    {
        // examinare, reexaminare
        $solicitari = Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->get();

        $updatedStatus = 'COMUNICARE VALIDATA';

        switch (strtolower($solicitari[0]->tip_comisie)) {
            case 'duplicate':
            case 'schimbarenume':
            case 'vizeperiodice':
                $updatedStatus = 'NR AUTORIZARE INTRODUS';
                break;
            case 'suspendare':
            case 'incetaresuspendare':
            case 'retragereautorizatie':
                $updatedStatus = 'AVIZE GENERATE';
                break;
            default:
                $updatedStatus = 'COMUNICARE VALIDATA';

        }
        foreach ($solicitari as $solicitare) {
            Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->update(['status' => $updatedStatus]);


        }

        return response()->json([
            'requestStatus' => 200,
            'status' => $updatedStatus,
        ]);
    }
}


