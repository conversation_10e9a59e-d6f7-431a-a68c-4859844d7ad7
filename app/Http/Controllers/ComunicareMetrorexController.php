<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Cenafer;
use App\Models\Comisie;
use App\Models\isfuri;
use App\Models\Tarife;
use App\Models\UnitatiFeroviare;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Solicitari;
use Illuminate\Support\Facades\Log;
use Mockery\Undefined;

class ComunicareMetrorexController extends Controller
{

    public function generatePersFizicaPDF($oameni)
    {
        Solicitari::where('oameni', $oameni)->update(['status' => 'COMUNICARE TIPARITA']);
        $solicitare = Solicitari::where('oameni', $oameni)->first();
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie)->first();
        $isf = Isfuri::where('contor', $solicitare->id_isf)->first();

        $solicitariAnterioare = null;
        $tipuriCuSolicitariAnterioare = ['duplicate', 'incetaresuspendare', 'retragereautorizatie', 'schimbarenume', 'vizeperiodice', 'suspendare', 'reautorizare'];
        $idsAutorizatiiAnterioare = explode(',', $solicitare->ids_aut_anterioare);

        // solicitari anterioare pt type3
        if (in_array(strtolower($solicitare->tip_comisie), $tipuriCuSolicitariAnterioare) && $idsAutorizatiiAnterioare) {
            $solicitariAnterioare = [];

            foreach ($idsAutorizatiiAnterioare as $idSolicitareAnterioara) {
                $solicitareGasita = Solicitari::where('oameni', intval($idSolicitareAnterioara))->first();
                array_push($solicitariAnterioare, $solicitareGasita);
            }

        }
        // de gasit ce solicitari anterioare trebuie luate pt vize periodice
        $tipuriTaxe = ['examinare', 'reautorizare', 'reexaminare', 'vizeperiodice', 'duplicate', 'schimbarenume'];
        $tarifCalculat = 0;
        if (in_array(strtolower($solicitare->tip_comisie), $tipuriTaxe)) {
            if (in_array(strtolower($solicitare->tip_comisie), $tipuriCuSolicitariAnterioare) && $idsAutorizatiiAnterioare) {
                foreach ($idsAutorizatiiAnterioare as $idSolicitareAnterioara) {
                    $tarif = Tarife::where('tip_solicitare', $solicitare->tip_comisie)->first();
                    if ($comisie->urgent === 'Da') {
                        $tarifCalculat = $tarif->tarif_orar * $tarif->nr_ore * $tarif->multiplier_tarif_urgenta;
                    } else {
                        $tarifCalculat = $tarif->tarif_orar * $tarif->nr_ore;
                    }
                }
            } else {
                $tarif = Tarife::where('tip_solicitare', $solicitare->tip_comisie)->first();
                if ($comisie->urgent === 'Da') {
                    $tarifCalculat = $tarif->tarif_orar * $tarif->nr_ore * $tarif->multiplier_tarif_urgenta;
                } else {
                    $tarifCalculat = $tarif->tarif_orar * $tarif->nr_ore;
                }
            }

        }
        ;



        $pdf = Pdf::loadView('ComunicarePersFizicaMetrorex.' . $solicitare->tip_comisie, compact('solicitare', 'comisie', 'isf', 'solicitariAnterioare', 'tarifCalculat'));
        $pdfContent = $pdf->output();

        return response()->json([
            'status' => $solicitare->status,
            'pdf' => base64_encode($pdfContent)
        ]);
    }
    public function generatePersJuridicaPDF($oameni)
    {   // de adunat toate solicitarile cu acelasi id de grup si puse toate datele de la acestea in tabel
        $solicitare = Solicitari::where('oameni', $oameni)->first();
        $id_solicitare_pj = $solicitare->id_solicitare_pj;

        Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->update(['status' => 'COMUNICARE TIPARITA']);
        Log::info('id solicitare pj' . $id_solicitare_pj);

        $solicitari = Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->get();
        $comisie = Comisie::where('nr_comisie', $solicitari[0]->nr_comisie)->first();
        $isf = Isfuri::where('contor', $solicitari[0]->id_isf)->first();
        //pentru fiecare $solicitari->oameni, gasim toate solicitarile cu where('tip_comisie', 'Examinare'), dam push in array ul solicitariAnterioare
        $unitate = UnitatiFeroviare::where('unitate', $solicitari[0]->unitate)->first();

        $solicitariAnterioare = null;
        $tipuriCuSolicitariAnterioare = ['duplicate', 'incetaresuspendare', 'retragereautorizatie', 'schimbarenume', 'vizeperiodice', 'suspendare', 'reautorizare'];
        $idsAutorizatiiAnterioare = explode(',', $solicitare->ids_aut_anterioare);

        // solicitari anterioare pt type3
        if (in_array(strtolower($solicitare->tip_comisie), $tipuriCuSolicitariAnterioare) && $idsAutorizatiiAnterioare) {
            $solicitariAnterioare = [];
            foreach ($idsAutorizatiiAnterioare as $idSolicitareAnterioara) {
                $solicitareGasita = Solicitari::where('oameni', intval($idSolicitareAnterioara))->first();
                array_push($solicitariAnterioare, $solicitareGasita);
            }
        }



        $tipuriTaxe = ['examinare', 'reautorizare', 'reexaminare', 'vizeperiodice', 'duplicate', 'schimbarenume'];
        $tarifCalculat = 0;

        if (in_array(strtolower($solicitari[0]->tip_comisie), $tipuriTaxe)) {
            $tarif = Tarife::where('tip_solicitare', $solicitari[0]->tip_comisie)->first();
            if (in_array(strtolower($solicitare->tip_comisie), $tipuriCuSolicitariAnterioare) && $idsAutorizatiiAnterioare) {
                foreach ($idsAutorizatiiAnterioare as $idSolicitareAnterioara) {
                    if ($comisie->urgent === 'Da') {
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore * $tarif->multiplier_tarif_urgenta;
                    } else {
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore;
                    }
                }
            } else {
                foreach ($solicitari as $solicitare) {
                    if ($comisie->urgent === 'Da') {
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore * $tarif->multiplier_tarif_urgenta;
                    } else {
                        $tarifCalculat += $tarif->tarif_orar * $tarif->nr_ore;
                    }
                }
            }
        };


        $pdf = Pdf::loadView('ComunicarePersJuridicaMetrorex.' . $solicitari[0]->tip_comisie, compact('solicitari', 'solicitariAnterioare', 'comisie', 'isf', 'unitate', 'tarifCalculat'));
        $pdfContent = $pdf->output();

        return response()->json([
            'status' => $solicitare->status,
            'pdf' => base64_encode($pdfContent)
        ]);

        //de trecut prin toate solicitarile si păus tabel la fiecare in parte
    }
}
