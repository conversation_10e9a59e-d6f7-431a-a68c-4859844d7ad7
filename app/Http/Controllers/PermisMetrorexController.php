<?php

namespace App\Http\Controllers;

use App\Models\Cenafer;
use App\Models\Comisie;
use App\Models\PersonalMetrorex;
use App\Models\Tarife;
use App\Models\TipComisie;
use Illuminate\Http\Request;
use App\Models\Solicitari;
use App\Models\isfuri;
use App\Models\UnitatiFeroviare;
use App\Models\FunctiiMetrorex;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Opcodes\LogViewer\Logs\Log as LogsLog;

function valid_CNP($input)
{
    // Verifică dacă CNP-ul are exact 13 caractere și este compus doar din cifre
    if (strlen($input) != 13 || !ctype_digit($input)) {
        return false;
    }

    // Împarte fiecare cifră a CNP-ului într-un vector
    for ($i = 0; $i <= 12; $i++) {
        $cnp[] = intval($input[$i]);
    }

    // Verificare prima cifră (sex și secol)
    if ($cnp[0] < 1 || $cnp[0] > 9) {
        return false; // Prima cifră trebuie să fie între 1 și 9
    }

    // Verificare anul nașterii
    $an = ($cnp[0] == 1 || $cnp[0] == 2) ? 1900 : (($cnp[0] == 3 || $cnp[0] == 4) ? 1800 : 2000);
    $an += $cnp[1] * 10 + $cnp[2];

    // Verificare luna nașterii (01-12)
    $luna = $cnp[3] * 10 + $cnp[4];
    if ($luna < 1 || $luna > 12) {
        return false; // Lună invalidă
    }

    // Verificare ziua nașterii (01-31), fără a valida zilele exacte ale fiecărei luni (pentru simplitate)
    $zi = $cnp[5] * 10 + $cnp[6];
    if ($zi < 1 || $zi > 31) {
        return false; // Zi invalidă
    }

    // Verificare județ (codul trebuie să fie între 01 și 52)
    $judet = $cnp[7] * 10 + $cnp[8];
    if ($judet < 1 || $judet > 52) {
        return false; // Județ invalid
    }

    // Calculează cifra de control
    $coef = [2, 7, 9, 1, 4, 6, 3, 5, 8, 2, 7, 9]; // coeficienții specifici algoritmului
    $suma = 0;
    for ($i = 0; $i <= 11; $i++) {
        $suma += $cnp[$i] * $coef[$i];
    }

    // Restul împărțirii sumei la 11
    $rest = $suma % 11;

    // Dacă restul este 10, cifra de control trebuie să fie 1, altfel trebuie să fie egală cu restul
    if (($rest < 10 && $rest == $cnp[12]) || ($rest == 10 && $cnp[12] == 1)) {
        return true; // CNP valid
    } else {
        return false; // CNP invalid
    }
}

class PermisMetrorexController extends Controller
{
    public function index()
    {
        $columns = [
            'id'=> 'ID',
            'nr_permis'=> 'NR. PERMIS',
            'nume'=> 'NUME',
            'prenume'=> 'PRENUME',
            'cnp'=> 'CNP',
            'data_pv'=> 'DATĂ PV',
            'numar_pv' => 'NUMĂR PV'
        ];


        $selectColumns = array_keys($columns); 

        $query = PersonalMetrorex::select(array_keys($columns))->orderBy('id', 'DESC');

        $dateTabel = $query->paginate(10);

        $menus = config('customVariables.menus');

        return view('permisMetrorex.vizualizare', compact('menus', 'dateTabel', 'columns'));
    }


    

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        function getLastId()
        {
            $last = DB::table('personal_metrorex')->orderByDesc('nr_permis')->first();
            
            return $last ? ($last->nr_permis + 1) : 1;
            
            
        }
        $last = getLastId();
        Log::info('numar permis next: ' . $last);


        $functions = FunctiiMetrorex::get();

        $tipSolicitare = TipComisie::all();

        $unitatiColumns = [
            'unitate',
            'cod_fiscal',
        ];
        Log::info('functions' . $functions[0]);
        $unitatiFeroviare = UnitatiFeroviare::orderBy('unitate')->get();

        $cenaferuri = Cenafer::all();
        $isfuri = isfuri::all();
        return view('permisMetrorex.create', compact('unitatiFeroviare', 'unitatiColumns', 'tipSolicitare', 'functions', 'cenaferuri', 'isfuri', 'last'));
        // la introducere serie si numar CI, introducem in personal petru CNP ul respectiv
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $fieldsToValidate = [
            'numeMetrorex' => 'required|string|max:255',
            'prenumeMetrorex' => 'required|string|max:255',
            'cnpMetrorex' => 'required|string|size:13|regex:/^[0-9]+$/',
            'dataPvMetrorex' => 'required|date',
            'numarPvMetrorex' => 'required|string|max:50',
            'fotografieAutorizatieMetrorex' => 'required|image|mimes:jpg,jpeg,png|max:2048',
            'functieMetrorex' => 'required|string|max:200',
            'numarPermisMetrorex' => 'required|string|max:50'
        ];

        $validator = Validator::make($request->all(), $fieldsToValidate); // fac validarea in sine
        if ($validator->fails()) {
            // Return validation errors
            return response()->json([
                'message' => 'Eroare de validare',
                'errors' => $validator->errors()->keys(),
            ], 422);
        }

        if (!valid_CNP($request->input('cnpMetrorex'))) //iau input dupa id, nu dupa variabila
            return response()->json(['isValid' => false], 400);

        DB::beginTransaction();
        try {

            // Get the base64 image string from the request
            $base64Image = $request->input('fotografieAutorizatieMetrorex');

            // Remove the `data:image/png;base64,` part if it exists
            $image = preg_replace('/^data:image\/[a-zA-Z]+;base64,/', '', $base64Image);


            Log::info('image ' . $image);

            // Decode the base64 string
            $decodedImage = base64_decode($image);

            // Generate a unique name for the image file
            $imageName = $request->input('cnpMetrorex') . $request->input('numarPvMetrorex') . '.png';

            // Specify the folder where you want to save the image
            $path = 'uploads/images/' . $imageName;

            Log::info('path' . $path);

            // Save the decoded image in the specified path
            Storage::disk('public')->put($path, $decodedImage);


            $personalMetrorex = new PersonalMetrorex(); //creez un model pentru personalMetrorex(o intrare noua)
            $personalMetrorex->nume = $request->input('numeMetrorex'); // adaug datele in coloanele necesare
            $personalMetrorex->prenume = $request->input('prenumeMetrorex');
            $personalMetrorex->cnp = $request->input('cnpMetrorex');
            $personalMetrorex->data_pv = $request->input('dataPvMetrorex');
            $personalMetrorex->numar_pv = $request->input('numarPvMetrorex');
            $personalMetrorex->fotografie = $imageName;
            $personalMetrorex->functie = $request->input('functieMetrorex');
            $personalMetrorex->nr_permis = $request->input('numarPermisMetrorex');

            $personalMetrorex->save();
            Log::info('saved successfully');
            DB::commit(); // commit neaparat dupa save sa salveze operatia
            return response()->json(['message' => 'Data saved successfully'], 200); //trebuie neaoparat sa returnez ceva pt ca pe catch returnez deci trb si aici
        } catch (\Exception $e) { //habar n-am ce-i aici
            DB::rollBack();
            Log::error('Error saving data', ['error' => $e->getMessage()]);
            return response()->json(['message' => 'Error saving data', 'error' => $e->getMessage()], 500);
        }

        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $unitatiColumns = [
            'unitate',
            'cod_fiscal',
        ];
        
        $permisMetrorex = PersonalMetrorex::findOrFail($id);
        $functions = FunctiiMetrorex::get();
        $cenaferuri = Cenafer::all();
        $isfuri = isfuri::all();
        $unitatiFeroviare = UnitatiFeroviare::orderBy('unitate')->get();
        // dd($permisMetrorex);

        return view('permisMetrorex.edit', compact('permisMetrorex', 'functions', 'unitatiFeroviare', 'cenaferuri', 'isfuri', 'unitatiColumns'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'numeMetrorex' => 'required|string',
            'prenumeMetrorex' => 'required|string',
            'cnpMetrorex' => 'required|string',
            'dataPvMetrorex' => 'required|date',
            'numarPvMetrorex' => 'required|string',
            'functieMetrorex' => 'nullable|string',
            'numarPermisMetrorex' => 'nullable|string',
            'fotografieAutorizatieMetrorex' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $personal = PersonalMetrorex::findOrFail($id); // Caută în baza de date

        if ($request->hasFile('fotografieAutorizatieMetrorex')) {
            $file = $request->file('fotografieAutorizatieMetrorex');
            $filename = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('uploads'), $filename);
            $personal->fotografie = $filename; // Salvează numele imaginii
        }

        $personal->update($request->except(['fotografieAutorizatieMetrorex'])); // Actualizează restul datelor

        return response()->json(['message' => 'Solicitarea a fost actualizata cu succes']);
    }



    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
