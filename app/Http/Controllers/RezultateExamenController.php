<?php

namespace App\Http\Controllers;

use App\Models\Comisie;
use Illuminate\Http\Request;
use App\Models\Solicitari;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class RezultateExamenController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function createPf($id_solicitare)
    {
        $solicitarePf = Solicitari::select()->where('oameni', $id_solicitare)->first();
        return view('rezultate_examen.create', compact('solicitarePf'));
    }

    public function createPj($id_solicitare_pj)
    {   //array ul cu toate solicitarile/persoanele care apartin de solicitarea id_solicitare_pj
        $solicitariPj = Solicitari::select()->where('id_solicitare_pj', $id_solicitare_pj)->get();

        return view('rezultate_examen.create', compact('solicitariPj'));
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $note = $request->input('note');

        $pv_data = [
            'pv_nr_iesire' => $request->input('pv_nr_iesire'),
            'pv_data_iesire' => $request->input('pv_data_iesire'),
            'pv_nr_intrare' => $request->input('pv_nr_intrare'),
            'pv_data_intrare' => $request->input('pv_data_intrare')
        ];

        $fieldsToValidateGeneral = [
            'pv_nr_iesire' => 'required',
            'pv_data_iesire' => 'required',
            'pv_nr_intrare' => 'required',
            'pv_data_intrare' => 'required'
        ];

        $validatorGeneral = Validator::make($pv_data, $fieldsToValidateGeneral);

        if ($validatorGeneral->fails()) {
            // Return validation errors
            return response()->json([
                'message' => 'Eroare de validare',
                'errors' => $validatorGeneral->errors()->keys(),
            ], 422);
        }
        Log::info('validated general fields');



        $keys = array_keys($note[0]);

        $solicitare = Solicitari::where('oameni', $keys[0])->first();
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie);

        $comisie->update([
            'pv_nr_iesire' => $pv_data['pv_nr_iesire'],
            'pv_data_iesire' => $pv_data['pv_data_iesire'],
            'pv_nr_intrare' => $pv_data['pv_nr_intrare'],
            'pv_data_intrare' => $pv_data['pv_data_intrare'],

        ]);

        

        foreach ($note as $noteItem) {
            foreach ($noteItem as $id_solicitare => $data) {

                $fieldsToValidate = [
                    'mat1_nota1' => 'required|numeric|between:0,10',
                    'mat1_nota2' => 'required|numeric|between:0,10',
                    'mat1_nota3' => 'required|numeric|between:0,10',
                    'mat1_nota4' => 'required|numeric|between:0,10',
                    'mat1_nota5' => 'required|numeric|between:0,10',
                    'mat2_nota1' => 'required|numeric|between:0,10',
                    'mat2_nota2' => 'required|numeric|between:0,10',
                    'mat2_nota3' => 'required|numeric|between:0,10',
                    'mat2_nota4' => 'required|numeric|between:0,10',
                    'mat2_nota5' => 'required|numeric|between:0,10',
                    'mat3_nota1' => 'required|numeric|between:0,10',
                    'mat3_nota2' => 'required|numeric|between:0,10',
                    'mat3_nota3' => 'required|numeric|between:0,10',
                    'mat3_nota4' => 'required|numeric|between:0,10',
                    'mat3_nota5' => 'required|numeric|between:0,10',
                    'mat1_medie' => 'required|numeric|between:0,10',
                    'mat2_medie' => 'required|numeric|between:0,10',
                    'mat3_medie' => 'required|numeric|between:0,10',
                    'medie_t' => 'required|numeric|between:0,10',
                    'calificativ_t' => 'required',
                    'calificativ_p' => 'required',
                    'calificativ' => 'required',
                ];
                Log::info($data);
                if (strtolower($data['calificativ']) === 'absent') {
                    $fieldsToValidate['mat1_medie'] = 'nullable';
                    $fieldsToValidate['mat2_medie'] = 'nullable';
                    $fieldsToValidate['mat3_medie'] = 'nullable';
                    $fieldsToValidate['medie_t'] = 'nullable';
                }
                $validator = Validator::make($data, $fieldsToValidate);
                if ($validator->fails()) {
                    // Return validation errors
                    return response()->json([
                        'message' => 'Eroare de validare',
                        'errors' => $validator->errors()->keys(),
                    ], 422);
                }
                Log::info('validated note fields');

                Log::info($id_solicitare);
                // Find the record based on the 'id_solicitare'
                $solicitare = Solicitari::where('oameni', $id_solicitare);

                // If the record is found, update it
                if ($solicitare) {
           
                        $solicitare->update([
                            'mat1_nota1' => $data['mat1_nota1'],
                            'mat1_nota2' => $data['mat1_nota2'],
                            'mat1_nota3' => $data['mat1_nota3'],
                            'mat1_nota4' => $data['mat1_nota4'],
                            'mat1_nota5' => $data['mat1_nota5'],
                            'mat2_nota1' => $data['mat2_nota1'],
                            'mat2_nota2' => $data['mat2_nota2'],
                            'mat2_nota3' => $data['mat2_nota3'],
                            'mat2_nota4' => $data['mat2_nota4'],
                            'mat2_nota5' => $data['mat2_nota5'],
                            'mat3_nota1' => $data['mat3_nota1'],
                            'mat3_nota2' => $data['mat3_nota2'],
                            'mat3_nota3' => $data['mat3_nota3'],
                            'mat3_nota4' => $data['mat3_nota4'],
                            'mat3_nota5' => $data['mat3_nota5'],
                            'mat1_medie' => $data['mat1_medie'],
                            'mat2_medie' => $data['mat2_medie'],
                            'mat3_medie' => $data['mat3_medie'],
                            'medie_t' => $data['medie_t'],
                            'calificativ_t' => $data['calificativ_t'],
                            'calificativ_p' => $data['calificativ_p'],
                            'calificativ' => $data['calificativ'],
                            'status' => 'REZULTATE INTRODUSE'
                        ]);
                    
                 
                }
            }
        }



        return response()->json(['message' => 'Note adaugate cu succes'], 200);


    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function editPf(string $oameni)
    {

        $solicitarePf = Solicitari::select()->where('oameni', $oameni)->first();
        $comisie = Comisie::where('nr_comisie', $solicitarePf->nr_comisie)->first();// aici
        Log::info('comisie->'. $comisie->pv_data_iesire);
        Log::info('comisie->'. $solicitarePf->nr_comisie);
        return view('rezultate_examen.edit', compact('solicitarePf', 'comisie'));
    }
    public function editPj(string $oameni)
    {
        $solicitare=Solicitari::select()->where('oameni', $oameni)->first();
        
        $solicitariPj = Solicitari::select()->where('id_solicitare_pj', $solicitare->id_solicitare_pj)->get();
        $comisie = Comisie::where('nr_comisie', $solicitariPj[0]->nr_comisie)->first();

        Log::info('comisie->'. $comisie->pv_data_iesire);
        Log::info('comisie->'. $solicitariPj[0]->nr_comisie);
        return view('rezultate_examen.edit', compact('solicitariPj', 'comisie'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        $note = $request->input('note');

        $pv_data = [
            'pv_nr_iesire' => $request->input('pv_nr_iesire'),
            'pv_data_iesire' => $request->input('pv_data_iesire'),
            'pv_nr_intrare' => $request->input('pv_nr_intrare'),
            'pv_data_intrare' => $request->input('pv_data_intrare')
        ];

        $fieldsToValidateGeneral = [
            'pv_nr_iesire' => 'required',
            'pv_data_iesire' => 'required',
            'pv_nr_intrare' => 'required',
            'pv_data_intrare' => 'required'
        ];

        $validatorGeneral = Validator::make($pv_data, $fieldsToValidateGeneral);

        if ($validatorGeneral->fails()) {
            // Return validation errors
            return response()->json([
                'message' => 'Eroare de validare',
                'errors' => $validatorGeneral->errors()->keys(),
            ], 422);
        }
        Log::info('validated general fields');



        $keys = array_keys($note[0]);

        $solicitare = Solicitari::where('oameni', $keys[0])->first();
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie);

        $comisie->update([
            'pv_nr_iesire' => $pv_data['pv_nr_iesire'],
            'pv_data_iesire' => $pv_data['pv_data_iesire'],
            'pv_nr_intrare' => $pv_data['pv_nr_intrare'],
            'pv_data_intrare' => $pv_data['pv_data_intrare'],

        ]);

        

        foreach ($note as $noteItem) {
            foreach ($noteItem as $id_solicitare => $data) {

                $fieldsToValidate = [
                    'mat1_nota1' => 'required|numeric|between:0,10',
                    'mat1_nota2' => 'required|numeric|between:0,10',
                    'mat1_nota3' => 'required|numeric|between:0,10',
                    'mat1_nota4' => 'required|numeric|between:0,10',
                    'mat1_nota5' => 'required|numeric|between:0,10',
                    'mat2_nota1' => 'required|numeric|between:0,10',
                    'mat2_nota2' => 'required|numeric|between:0,10',
                    'mat2_nota3' => 'required|numeric|between:0,10',
                    'mat2_nota4' => 'required|numeric|between:0,10',
                    'mat2_nota5' => 'required|numeric|between:0,10',
                    'mat3_nota1' => 'required|numeric|between:0,10',
                    'mat3_nota2' => 'required|numeric|between:0,10',
                    'mat3_nota3' => 'required|numeric|between:0,10',
                    'mat3_nota4' => 'required|numeric|between:0,10',
                    'mat3_nota5' => 'required|numeric|between:0,10',
                    'mat1_medie' => 'required|numeric|between:0,10',
                    'mat2_medie' => 'required|numeric|between:0,10',
                    'mat3_medie' => 'required|numeric|between:0,10',
                    'medie_t' => 'required|numeric|between:0,10',
                    'calificativ_t' => 'required',
                    'calificativ_p' => 'required',
                    'calificativ' => 'required',
                ];
                Log::info($data);
                if (strtolower($data['calificativ']) === 'absent') {
                    $fieldsToValidate['mat1_medie'] = 'nullable';
                    $fieldsToValidate['mat2_medie'] = 'nullable';
                    $fieldsToValidate['mat3_medie'] = 'nullable';
                    $fieldsToValidate['medie_t'] = 'nullable';
                }
                $validator = Validator::make($data, $fieldsToValidate);
                if ($validator->fails()) {
                    // Return validation errors
                    return response()->json([
                        'message' => 'Eroare de validare',
                        'errors' => $validator->errors()->keys(),
                    ], 422);
                }
                Log::info('validated note fields');

                Log::info($id_solicitare);
                // Find the record based on the 'id_solicitare'
                $solicitare = Solicitari::where('oameni', $id_solicitare);

                // If the record is found, update it
                if ($solicitare) {
           
                    $solicitare->update([
                        'mat1_nota1' => $data['mat1_nota1'],
                        'mat1_nota2' => $data['mat1_nota2'],
                        'mat1_nota3' => $data['mat1_nota3'],
                        'mat1_nota4' => $data['mat1_nota4'],
                        'mat1_nota5' => $data['mat1_nota5'],
                        'mat2_nota1' => $data['mat2_nota1'],
                        'mat2_nota2' => $data['mat2_nota2'],
                        'mat2_nota3' => $data['mat2_nota3'],
                        'mat2_nota4' => $data['mat2_nota4'],
                        'mat2_nota5' => $data['mat2_nota5'],
                        'mat3_nota1' => $data['mat3_nota1'],
                        'mat3_nota2' => $data['mat3_nota2'],
                        'mat3_nota3' => $data['mat3_nota3'],
                        'mat3_nota4' => $data['mat3_nota4'],
                        'mat3_nota5' => $data['mat3_nota5'],
                        'mat1_medie' => $data['mat1_medie'],
                        'mat2_medie' => $data['mat2_medie'],
                        'mat3_medie' => $data['mat3_medie'],
                        'medie_t' => $data['medie_t'],
                        'calificativ_t' => $data['calificativ_t'],
                        'calificativ_p' => $data['calificativ_p'],
                        'calificativ' => $data['calificativ'],
                        'status' => 'REZULTATE INTRODUSE'
                    ]);
                
             
            }
            }
        }



        return response()->json(['message' => 'Note adaugate cu succes'], 200);



    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
