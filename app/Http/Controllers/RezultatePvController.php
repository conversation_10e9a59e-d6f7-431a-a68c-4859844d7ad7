<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Comisie;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Solicitari; // Assuming the model is named Solicitari
use App\Models\isfuri;
use Illuminate\Support\Facades\Log;

class RezultatePvController extends Controller
{
    public function generatePDFpf($id_solicitare)
    {
        $solicitare = Solicitari::where('oameni', $id_solicitare)->first();
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie)->first();
        $isfuri = isfuri::where('contor', $solicitare->id_isf)->first();

        $pdf = Pdf::loadView('rezultatePvPf', compact('solicitare', 'comisie', 'isfuri'));

        Solicitari::where('oameni', $id_solicitare)->update(['status' => 'PV REZULTATE PRINTAT']);

        return response()->json([
            'pdf' => base64_encode($pdf->output()),
            'status' => 200
        ]);
    }

    public function generatePDFpj($id_solicitare_pj)
    {
        $solicitare = Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->first();
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie)->first();
        $isfuri = isfuri::where('contor', $solicitare->id_isf)->first();
        $solicitari = Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->get();
        Log::info('isfuri' . $isfuri);
        $pdf = Pdf::loadView('rezultatePvPj', compact('solicitari', 'comisie', 'isfuri'));

        Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->update(['status' => 'PV REZULTATE PRINTAT']);

        return response()->json([
            'pdf' => base64_encode($pdf->output()),
            'status' => 200
        ]);
    }
    public function validareRezultatPf(Request $request, $id_solicitare)
    {
        $solicitare = Solicitari::where('oameni', $id_solicitare);
        $solicitareData = $solicitare->first();

        $tipComisieAutAnterioara = strtolower($request->input('tipComisieAutAnterioara'));
        Log::info('tip comisie ant ' . $tipComisieAutAnterioara);
        $updatedStatus = 'PV REZULTATE VALIDATE';

        switch (strtolower($solicitareData->tip_comisie)) {
            case 'reprogramare':
            case 'reexaminare':
                if ($tipComisieAutAnterioara === 'examinare') {
                    Log::info('este examinare');
                    $updatedStatus = 'PV REZULTATE VALIDATE';
                    break;
                } else if ($tipComisieAutAnterioara === 'reautorizare') {
                    Log::info('este reautorizare');
                    $updatedStatus = 'AVIZE GENERATE';



                    break;
                }
            case 'reautorizare':
                $updatedStatus = 'AVIZE GENERATE';
                break;
            default:
                $updatedStatus = 'PV REZULTATE VALIDATE';

        }

        if (strtolower($solicitareData->calificativ) === 'necorespunzător' || strtolower($solicitareData->calificativ) === 'absent') {
            Log::info('este necorespunzator');
            $updatedStatus = 'NECORESPUNZATOR';
        }


        if(strtolower($solicitare->tip_comisie) === 'reautorizare' && $updatedStatus !== 'NECORESPUNZATOR'){
            Solicitari::where('oameni', $id_solicitare)->update(['cum_e' => 'Autorizat']);
        }elseif ($updatedStatus === 'NECORESPUNZATOR'){
            Solicitari::where('oameni', $id_solicitare)->update(['cum_e' => 'Neautorizat']);

        }


        Log::info('updatedStatus' . $updatedStatus);
        $solicitare->update(['status' => $updatedStatus]);

        return response()->json([
            'requestStatus' => 200,
            'status' => $updatedStatus,
        ]);
    }
    public function validareRezultatPj($id_solicitare_pj)
    {

        $solicitari = Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->get();
        // $tipComisieAutAnterioara = $request->input('tipComisieAutAnterioara');
        $updatedStatus = 'PV REZULTATE VALIDATE';

        switch (strtolower($solicitari[0]->tip_comisie)) {
            // case 'reprogramare':
            // case 'reexaminare':
            //     if ($tipComisieAutAnterioara === 'examinare') {
            //         $updatedStatus = 'PV REZULTATE VALIDATE';
            //     } else if ($tipComisieAutAnterioara === 'reautorizare') {
            //         $updatedStatus = 'AVIZE GENERATE';
            //     }
            case 'reautorizare':
                $updatedStatus = 'AVIZE GENERATE';
                break;
            default:
                $updatedStatus = 'PV REZULTATE VALIDATE';

        }

        Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->update(['status' => $updatedStatus]);

        foreach ($solicitari as $solicitare) {
            Log::info('calificativ' . $solicitare->calificativ);
            if (strtolower($solicitare->calificativ) === 'necorespunzător' || strtolower($solicitare->calificativ) === 'absent') {
                Log::info('este necorespunzator');
                $updatedStatus = 'NECORESPUNZATOR';

                Solicitari::where('oameni', $solicitare->oameni)->update(['status' => 'NECORESPUNZATOR']);
            }
        }

        if(strtolower($solicitari[0]->tip_comisie) === 'reautorizare' && $updatedStatus !== 'NECORESPUNZATOR'){
            Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->update(['cum_e' => 'Autorizat']);
        }elseif ($updatedStatus === 'NECORESPUNZATOR'){
            Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->update(['cum_e' => 'Neautorizat']);

        }

        return response()->json([
            'requestStatus' => 200,
            'status' => $updatedStatus,
        ]);
    }

    public function edit(string $oameni)
    {

    }
}
