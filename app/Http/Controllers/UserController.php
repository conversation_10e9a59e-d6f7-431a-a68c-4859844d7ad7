<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Solicitari;
use App\Models\isfuri;
use App\Models\User;
use App\Models\UnitatiFeroviare;
use Illuminate\Support\Facades\DB;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $sort = $request->get('sort', 'name_asc'); // default sorting

    switch ($sort) {
        case 'name_asc':
            $users = User::orderBy('name', 'asc')->get();
            break;
        case 'name_desc':
            $users = User::orderBy('name', 'desc')->get();
            break;
        case 'isf_asc':
            $users = User::orderBy('isf', 'asc')->get();
            break;
        case 'isf_desc':
            $users = User::orderBy('isf', 'desc')->get();
            break;
        default:
            $users = User::orderBy('name', 'asc')->get();
            break;
    }
        $users = User::all();
        $menus = config('customVariables.menus');
        return view('admin.usermanagement', compact('menus','users'));
        
    }

    public function show($id)
    {
        // Code for retrieving and displaying a specific user by ID
        $user = User::find($id);
        return view('users.show', compact('user'));
    }

    public function create()
    {
        // Code for displaying the user creation form
    }

    public function store(Request $request)
    {
        // Code for storing a new user in the database
    }

    public function edit($id)
    {
        // Code for displaying the user edit form
    }

    public function update(Request $request)
    {
        foreach ($request->ISF as $userId => $isfValue) {
            $user = User::find($userId);
            if ($user) {
                $user->ISF = $isfValue;
                $user->is_admin = $request->is_admin[$userId];
                $user->save();
            }
        }
    
        return back()->with('success', 'Users updated successfully.');
    }

    public function destroy($id)
    {
        // Code for deleting a user from the database
    }
}
