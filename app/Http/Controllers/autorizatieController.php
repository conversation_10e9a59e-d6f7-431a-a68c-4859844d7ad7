<?php

namespace App\Http\Controllers;

use App\Models\Comisie;
use App\Models\isfuri;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Solicitari;
use App\Models\Functii;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class autorizatieController extends Controller
{
    public function generatePDF($oameni)
    {
        try {
            $solicitareCurenta = Solicitari::where('oameni', $oameni)->first();
            $comisie = Comisie::where('nr_comisie', $solicitareCurenta->nr_comisie)->first();
            $isf = Isfuri::where('nr_isf', $solicitareCurenta->id_isf)->first();



            $tipSolicitAnterioare = ['duplicate', 'schimbarenume', 'vizeperiodice'];

            // Set cum_e based on tip_comisie and status
            if (strtolower($solicitareCurenta->tip_comisie) === 'suspendare') {
                Solicitari::where('oameni', $oameni)->update(['cum_e' => 'Suspendată']);
            } elseif ($solicitareCurenta->status === 'NECORESPUNZATOR') {
                Solicitari::where('oameni', $oameni)->update(['cum_e' => 'Neautorizat']);
            } else {
                // For AVIZE_GENERATE status and non-Suspendare tip_comisie
                Solicitari::where('oameni', $oameni)->update(['cum_e' => 'Autorizat']);
            }

            if (in_array(strtolower($solicitareCurenta->tip_comisie), $tipSolicitAnterioare)) {
                $pdfs = [];
                Log::info('id aut anterioara string' . $solicitareCurenta->id_aut_anterioara);
                $idSolicitareAnterioara = $solicitareCurenta->id_aut_anterioara;


                // la schimbare nume, actualizam numele pt toate solicitarile selectate
                if (strtolower($solicitareCurenta->tip_comisie) === 'schimbarenume') {
                    Solicitari::where('oameni', intval($idSolicitareAnterioara))->update(['solicitant_nume' => $solicitareCurenta->solicitant_nume, 'solicitant_prenume' => $solicitareCurenta->solicitant_prenume]);
                }
                $solicitare = Solicitari::where('oameni', intval($idSolicitareAnterioara))->first();


                $pdf = Pdf::loadView('autorizatie', compact('solicitare', 'comisie', 'isf', 'solicitareCurenta'));
                array_push($pdfs, base64_encode($pdf->output()));

                Solicitari::where('oameni', $oameni)->update(['status' => 'AVIZE GENERATE']);

                return response()->json([
                    'pdfs' => $pdfs,
                    'status' => 200
                ]);

            } else {
                $pdf = Pdf::loadView('autorizatie', compact('solicitareCurenta', 'comisie', 'isf'));

                Solicitari::where('oameni', $oameni)->update(['status' => 'AVIZE GENERATE']);

                return response()->json([
                    'pdfs' => [base64_encode($pdf->output())],
                    'status' => 200
                ]);
            }
        } catch (Exception $e) {
            Log::error('eroare generare autorizatie ' . $e->getMessage());
            throw new Exception('Nu s-a putut trece la pasul urmator.');
        }


    }


    public function introducereNr_aut(Request $request)
    {

        $solicitare = Solicitari::where('oameni', $request->input('oameni'))->first();

        $nrAutDuplicat = Solicitari::where('nr_aut', $request->nr_aut)->exists();

        if ($nrAutDuplicat) {
            return response()->json([
                'status' => 409,
                'message' => 'Numărul de autorizație există deja la altă persoană.'
            ]);
        }

        $functie = Functii::where('domeniu', $solicitare->functie)->first();
        $serie_aut = $functie->serie_aut;


        Log::info('nr-aut pe care il caut: ' . $request->nr_aut);


        Solicitari::where('oameni', $request->input('oameni'))->update(['nr_aut' => $request->nr_aut, 'serie_aut' => $serie_aut, 'status' => 'NR AUTORIZARE INTRODUS']);
        return response()->json([
            'status' => 200,
        ]);
    }


}

