<?php

namespace App\Http\Controllers;

use App\Models\FunctiiMetrorex;
use App\Models\PersonalMetrorex;
use Illuminate\Http\Request;

use App\Models\Comisie;
use App\Models\isfuri;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Solicitari;
use App\Models\Functii;
use Illuminate\Support\Facades\Log;


class autorizatieMetrorexController extends Controller
{
    public function generatePDF($id)
    {
        //in personal_metrorex stochez randul in functie de id-ul persoanei. Dupa, in tabela functie_metrorex caut functia dupa id-ul persoanei respective($personal_metrorex->functie) in coloana 'domeniu'.
        $personal_metrorex = PersonalMetrorex::where('id', $id)->first();
        $functie_metrorex = FunctiiMetrorex::where('domeniu', $personal_metrorex->functie)->first();
        Log::info('foto ' . $personal_metrorex->fotografie);

        $imagePath = 'storage/uploads/images/' . $personal_metrorex->fotografie;

        Log::info('imagepath ' . $imagePath);

        $pdf = Pdf::loadView('autorizatieMetrorexPF', compact('personal_metrorex', 'functie_metrorex', 'imagePath'));

        $pdfContent = $pdf->output();

        return response()->json([
            'pdf' => base64_encode($pdfContent)
        ]);
    }
}
