<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Comisie;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Solicitari; // Assuming the model is named Solicitari
use App\Models\isfuri;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;


class autorizatiiEmiseController extends Controller
{
    public function generateAutorizatiiEmise(Request $request)
    {
        $nr_ISF = $request->input('nr_ISF');
        $cod_serviciu = Auth::user()->isf; // Get the currently authenticated user cod serviciu

        Log::info('nrisf' . $nr_ISF);
        Log::info('cod_serviciu' . $cod_serviciu);


        $isf = isfuri::where('cod_serviciu', $cod_serviciu)->first();
        // Log::info('Caut comisie cu nr_ISF: ' . $nr_ISF . ' și ISF: ' . $isf->ISF);

        // aici cauta dupa nr_isf si iau prima inregistrare gasita cu acel numar. Ce se intampla cand s-au introdus 1042 in mai multe sedii?
        $comisie = Comisie::join('personal', 'comisie.nr_comisie', '=', 'personal.nr_comisie')
            ->join('isfuri', 'personal.id_isf', '=', 'isfuri.contor')
            ->whereIn('personal.tip_comisie', ['Examinare', 'Reexaminare', 'Reautorizare', 'Duplicate', 'VizePeriodice', 'Reprogramare', 'SchimbareNume'])
            ->where('comisie.nr_ISF', $nr_ISF)
            ->where('isfuri.contor', $isf->contor) // Filtering by the correct id_isf
            ->orderBy('comisie.nr_comisie', 'desc')
            ->select('comisie.*')
            ->first();



        // caut comisia in functie de nr_ISF. 
        // Log::info('comisie gasita' . $comisie);

        Solicitari::where('nr_comisie', $comisie->nr_comisie)
            ->whereIn('tip_comisie', ['Examinare', 'Reexaminare', 'Reautorizare', 'Duplicate', 'VizePeriodice', 'Reprogramare', 'SchimbareNume'])
            ->where('status', '!=', 'NECORESPUNZATOR')
            ->update(['data_reemiterii' => Carbon::now()->format('Y-m-d')]); // caut solicitarea in functie de comisie->nr_comisie

        $solicitari = Solicitari::where('nr_comisie', $comisie->nr_comisie)
            ->whereIn('tip_comisie', ['Examinare', 'Reexaminare', 'Reautorizare', 'Duplicate', 'VizePeriodice', 'Reprogramare', 'SchimbareNume'])
            ->where('status', '!=', 'NECORESPUNZATOR')
            ->get(); // caut solicitarea in functie de comisie->nr_comisie

        // Log::info('isf' . $solicitari[0]->id_isf);
        // if (!$isf && $comisie->ISF) {
        //     $isf = isfuri::where('ISF', $comisie->ISF)->first();
        // }

        $pdf = Pdf::loadView('autorizatiiEmise', compact('solicitari', 'comisie', 'isf'));

        return response()->json([
            'pdf' => base64_encode($pdf->output()),
            'status' => 200
        ]);
    }
}
