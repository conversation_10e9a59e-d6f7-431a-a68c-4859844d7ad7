<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Comisie;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Solicitari; // Assuming the model is named Solicitari
use App\Models\UnitatiFeroviare;
use App\Models\Personal;

class facturareController extends Controller
{
    public function generatePDF($oameni)
    {
        $solicitare = Solicitari::where('oameni', $oameni)->first();
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie)->first(); //the nr_comisie column must match the value of $solicitare->nr_comisie.
        $personal = Personal::where('cnp', $solicitare->cnp)->first();

        $pdf = Pdf::loadView('facturare', compact('solicitare', 'comisie', 'personal'));

        return $pdf->download('facturare.pdf');
    }
}
