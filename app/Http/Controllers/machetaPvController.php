<?php

namespace App\Http\Controllers;

use App\Models\Comisie;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Solicitari; // Assuming the model is named Solicitari
use App\Models\isfuri;
use Illuminate\Support\Facades\Log;

class machetaPvController extends Controller
{
    public function generatePDFpf($oameni)
    {
        $solicitare = Solicitari::where('oameni', $oameni)->first();
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie)->first();
        $isfuri = isfuri::where('contor', $solicitare->id_isf)->first();

        $pdf = Pdf::loadView('machetaPvPf', compact('solicitare', 'comisie', 'isfuri'));

        Solicitari::where('oameni', $oameni)->update(['status' => 'PV TIPARIT']);

        return response()->json([
            'pdf' => base64_encode($pdf->output()),
            'status' => 200
        ]);
    }

    public function generatePDFpj($id_solicitare_pj)
    {
        $solicitare = Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->first();
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie)->first();
        $isfuri = isfuri::where('contor', $solicitare->id_isf)->first();
        $solicitari = Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->get();
        Log::info('isfuri' . $isfuri);
        $pdf = Pdf::loadView('machetaPvPj', compact('solicitari', 'comisie', 'isfuri'));

        Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->update(['status' => 'PV TIPARIT']);

        return response()->json([
            'pdf' => base64_encode($pdf->output()),
            'status' => 200
        ]);
    }

    public function validareMachetaPvPf($oameni)
    {
        Solicitari::where('oameni', $oameni)->update(['status' => 'PV VALIDAT']);


        return response()->json([
            'status' => 200,
        ]);
    }

    public function validareMachetaPvPj($id_solicitare_pj)
    {
        Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->update(['status' => 'PV VALIDAT']);


        return response()->json([
            'status' => 200,
        ]);
    }
}
