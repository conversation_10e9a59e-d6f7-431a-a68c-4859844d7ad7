<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Solicitari;
use App\Models\Comisie;
use App\Models\Personal;
use App\Models\Permis;
use App\Models\PermiseActive;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\UnitatiFeroviare;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

require_once app_path('Http/Controllers/solicitariController.php');
class permisController extends Controller
{
    // public function generatePDF($id)
    // {
    //     $id_permis = Permis::where('id', $id)->first(); 
    //     $prenume_titular = Permis::where('prenume_titular', $id_permis->prenume_titular)->first(); // coloana prenume titular must match the value of $id_permis->prenume_titular
    //     $nume_titular = Permis::where('nume_titular', $id_permis->nume_titular)->first();
    //     $data_nasterii = Permis::where('data_nasterii', $id_permis->data_nasterii)->first();
    //     $locul_nasterii = Permis::where('locul_nasterii', $id_permis->locul_nasterii)->first();
    //     $data_emitere = Permis::where('data_emitere', $id_permis->data_emitere)->first();
    //     $data_expirare = Permis::where('data_expirare', $id_permis->data_expirare)->first();
    //     $denumire_autoritate_emitenta = Permis::where('denumire_autoritate_emitenta', $id_permis->denumire_autoritate_emitenta)->first();
    //     $numar_atribuit_angajator = Permis::where('numar_atribuit_angajator', $id_permis->numar_atribuit_angajator)->first();
    //     $nr_permis = Permis::where('nr_permis', $id_permis->nr_permis)->first();
    //     // $fotografie_titular = Permis::where('fotografie_titular', $id_permis->fotografie_titular)->first();
    //     // $semnatura_titular = Permis::where('semnatura_titular', $id_permis->semnatura_titular)->first();
    //     $limba_materna = Permis::where('limba_materna', $id_permis->limba_materna)->first();





    //     $pdf = Pdf::loadView('permis', compact('id_permis', 'prenume_titular', 'nume_titular', 'data_nasterii', 'locul_nasterii', 'data_emitere', 'data_expirare', 'denumire_autoritate_emitenta', 'numar_atribuit_angajator', 'nr_permis',  'limba_materna'));

    //     return $pdf->download('permis.pdf');
    // }

    public function index(Request $request)
    {
        Log::info('A fost apelată metoda indexPermise.');
        $columns = [
            'id' => 'ID',
            'nr_permis' => 'Număr Permis',
            'nume_titular' => 'Nume',
            'prenume_titular' => 'Prenume',
            'data_emitere' => 'Dată Emitere',
            'cnp' => 'CNP',
            'judete' => 'Județ',
            // 'nr_card' => 'Număr Card',
            // 'motiv' => 'Motiv',
            // 'numar_permis_NEI' => 'Număr Permis NEI',
            // 'data_depunere' => 'Dată Depunere',
            // 'data_primire' => 'Dată Primire',
            // 'numar_identificare' => 'Număr Identificare',
            // 'strada_si_numar' => 'Adresă',
            // 'cod_postal' => 'Cod Poștal'
            // 'denumire_autoritate_emitenta',
            // 'statut_permis',
            // 'depus_solicitant',
            // 'depus_entitate',
            // 'angajator',
            // 'sex',
            // 'data_nasterii',
            // 'locul_nasterii',
            // 'nationalitate',
            // 'limba_materna',
            // 'tip_eliberare',
            // 'tara',
            // 'judet',
            // 'localitate',
            // 'telefon',
            // 'email',
            // 'fotografie',
            // 'data_primei_eliberari',
            // 'nr_intern_reg',
            // 'proces',
            // 'arhiva_docs',
            // 'nsaro',
            // 'data_expirare',
            // 'posta_de_trimitere',
            // 'numar_atribuit_angajator',
            // 'upload_cerere_semnat',
            // 'upload_caracter_personal',
            // 'upload_ciclu_invatamint',
            // 'upload_stare_fizica',
            // 'upload_stare_psihologica',
            // 'upload_competenta_profesionala',
            // 'upload_document_identitate',
            // 'numar_inregistrare_facultativ',
            // 'updated_at',
            // 'created_at',
            // 'operator',
            // 'inspector',
            // 'numar_bon',
            // 'printat',
            // 'vedere',
            // 'auditiv',
        ];

        $queryAfisarePermise = Permis::select(array_keys($columns))->orderBy('nr_permis', 'DESC');

        // fac căutare după nr_permis pe blade-ul de vizualizare
        // iau inputul din searchbar după key->search
        $search = $request->input('search');
        // verific ca acesta sa nu fie empty
        if (!empty($search)) {

            $permise = Permis::where('nr_permis', 'like', '%' . $search . '%')->get();
            if ($permise->isNotEmpty()) {

                Log::info('NR_PERMIS GASIT-> ' . $permise[0]->nr_permis);
                $queryAfisarePermise->where('nr_permis', 'like', '%' . $search . '%');
            }
        }

        $menus = config('customVariables.menus');


        $isAdmin = auth()->user()->hasRole('admin');
        $isSuperAdmin = auth()->user()->hasRole('super_admin');
        $isRegularUser = auth()->user()->hasRole('user');

        //querry-ul pentru a cauta permisele




        // returnez pagina pt rezultate
        $dateTabelPermise = $queryAfisarePermise->paginate(10);

        return view('permis.vizualizare', compact('dateTabelPermise', 'columns', 'menus', 'search'));
    }

    public function generatePDF($id)
    {
        $permis = Permis::find($id);

        if (!$permis) {
            return response()->json(['error' => 'Permis not found'], 404);
        }

        // Creează PDF-ul folosind view-ul 'permis' și variabilele necesare
        $pdf = Pdf::loadView('permis', [
            'prenume_titular' => $permis->prenume_titular,
            'nume_titular' => $permis->nume_titular,
            'data_nasterii' => $permis->data_nasterii,
            'locul_nasterii' => $permis->locul_nasterii,
            'data_emitere' => $permis->data_emitere,
            'data_expirare' => $permis->data_expirare,
            'denumire_autoritate_emitenta' => $permis->denumire_autoritate_emitenta,
            'numar_atribuit_angajator' => $permis->numar_atribuit_angajator,
            'nr_permis' => $permis->nr_permis,
            // 'fotografie_titular' => $permis->fotografie_titular,
            // 'semnatura_titular' => $permis->semnatura_titular,
            'limba_materna' => $permis->limba_materna,
        ]);

        $pdfContent = $pdf->output();

        return response()->json([
            'pdf' => base64_encode($pdfContent)
        ]);
    }
    public function create()
    {
        $unitatiFeroviare = UnitatiFeroviare::all();

        return view('permis.create', compact('unitatiFeroviare'));
    }

    /**
     * Check a resource is in storage.
     */
    public function check(Request $request)
    {
        Log::info('se face check');

        $validator = Validator::make($request->all(), [
            'permis-cnp' => 'required|string|size:13',
            'permis-nume' => 'required|string|max:255',
            'permis-prenume' => 'required|string|max:255',
        ], [
            'permis-cnp.required' => 'CNP-ul este obligatoriu.',
            'permis-cnp.size' => 'CNP-ul trebuie să aibă exact 13 caractere.',
            'permis-nume.required' => 'Numele este obligatoriu.',
            'permis-nume.max' => 'Numele nu poate avea mai mult de 255 de caractere.',
            'permis-prenume.required' => 'Prenumele este obligatoriu.',
            'permis-prenume.max' => 'Prenumele nu poate avea mai mult de 255 de caractere.',
        ]);

        // Add custom CNP validation
        $validator->after(function ($validator) use ($request) {
            if (!valid_CNP($request->input('permis-cnp'))) {
                Log::info('CNP-ul introdus nu este valid.');
                $validator->errors()->add('permis-cnp', 'CNP-ul introdus nu este valid.');
            }
        });

        if ($validator->fails()) {
            return back()->withInput()->withErrors($validator);
        }

        Log::info('trece de validare');


        $columns = [
            'id',
            'motiv',
            'data_primire',
            'nume_titular',
            'prenume_titular',
            'sex',
            'data_nasterii',
            'locul_nasterii'
        ];
        $permiseExists = PermiseActive::where('cnp', $request->input('permis-cnp'))->exists();
        Log::info('permiseExists' . $permiseExists);

        if ($permiseExists) {
            $dateTabel = PermiseActive::select(...$columns)->where('cnp', $request->input('permis-cnp'))->paginate(10);
            return back()->with('message', 'Utilizatorul are deja un permis.')->with('hasPermis', true)->with('columns', $columns)->with('dateTabel', $dateTabel);
        } else {
            return back()->withInput()->with('message', 'Utilizatorul nu are permis, creează un permis.')
                ->with('showSecondForm', true);
        }
    }

    /**
     * Store a newly created resource in storage.
     */

    public function modificarePermis(Request $request)
    {
        Log::info('A fost apelată metoda modificarePermis.');
        $columns = [
            'id' => 'ID',
            'nr_permis' => 'Număr Permis',
            'nume_titular' => 'Nume',
            'prenume_titular' => 'Prenume',
            'data_emitere' => 'Dată Emitere',
            'cnp' => 'CNP',
            'judete' => 'Județ',
        ];


        $unitatiFeroviare = UnitatiFeroviare::all();

        $queryAfisarePermise = Permis::select(array_keys($columns))->orderBy('nr_permis', 'DESC');

        // fac căutare după nr_permis pe blade-ul de vizualizare
        // iau inputul din searchbar după key->search
        $search = $request->input('search');
        // verific ca acesta sa nu fie empty
        if (!empty($search)) {

            $permise = Permis::where('nr_permis', 'like', '%' . $search . '%')->get();
            if ($permise->isNotEmpty()) {

                Log::info('NR_PERMIS GASIT-> ' . $permise[0]->nr_permis);
                $queryAfisarePermise->where('nr_permis', 'like', '%' . $search . '%');
            }
        }

        $menus = config('customVariables.menus');


        $isAdmin = auth()->user()->hasRole('admin');
        $isSuperAdmin = auth()->user()->hasRole('super_admin');
        $isRegularUser = auth()->user()->hasRole('user');

        //querry-ul pentru a cauta permisele




        // returnez pagina pt rezultate
        $dateTabelPermise = $queryAfisarePermise->paginate(10);

        return view('permis.modificarePermis', compact('dateTabelPermise', 'columns', 'menus', 'search', 'unitatiFeroviare'));
    }


    public function acordareNumarPermis(Request $request)
    {
        Log::info('A fost apelată metoda acordarePermis.');

        $menus = config('customVariables.menus');


        $isAdmin = auth()->user()->hasRole('admin');
        $isSuperAdmin = auth()->user()->hasRole('super_admin');
        $isRegularUser = auth()->user()->hasRole('user');

        return view('permis.acordareNumarPermis', compact('menus'));
    }
    public function permisNeacordat(Request $request)
    {

    }

    public function permisDublat(Request $request)
    {

    }

    public function cardNeacordat(Request $request)
    {

    }

    public function cardDublat(Request $request)
    {

    }
    public function store(Request $request)
    {
        Log::info('A fost apelată metoda store.');

        $validator = Validator::make($request->all(), [
            // Basic information
            'permis-cnp' => 'required|string|size:13',
            'permis-nume' => 'required|string|max:255|regex:/^[a-zA-ZăâîșțĂÂÎȘȚ\s\-]+$/',
            'permis-prenume' => 'required|string|max:255|regex:/^[a-zA-ZăâîșțĂÂÎȘȚ\s\-]+$/',

            // Second form fields
            'unitati-feroviare' => 'required|string|max:255|exists:unitati_feroviare,unitate',
            'loc-nastere' => 'required|string|max:255',
            'limba-materna' => 'required|string|in:română,maghiară,germană,alta',
            'custom-limba-materna' => 'required_if:limba-materna,alta|string|max:100',
            'nr-angajator' => 'required|integer|min:1|max:999999',
            'judet' => 'required|string|max:100',

            // Optional fields
            'restrictii-medicale-1' => 'nullable|boolean',
            'restrictii-medicale-2' => 'nullable|boolean',
        ], [
            // Basic information messages
            'permis-cnp.required' => 'CNP-ul este obligatoriu.',
            'permis-cnp.size' => 'CNP-ul trebuie să aibă exact 13 caractere.',
            'permis-nume.required' => 'Numele este obligatoriu.',
            'permis-nume.max' => 'Numele nu poate avea mai mult de 255 de caractere.',
            'permis-nume.regex' => 'Numele poate conține doar litere, spații și cratime.',
            'permis-prenume.required' => 'Prenumele este obligatoriu.',
            'permis-prenume.max' => 'Prenumele nu poate avea mai mult de 255 de caractere.',
            'permis-prenume.regex' => 'Prenumele poate conține doar litere, spații și cratime.',

            // Second form messages
            'unitati-feroviare.required' => 'Unitatea feroviară este obligatorie.',
            'unitati-feroviare.max' => 'Unitatea feroviară nu poate avea mai mult de 255 de caractere.',
            'unitati-feroviare.exists' => 'Unitatea feroviară selectată nu este validă.',
            'loc-nastere.required' => 'Locul nașterii este obligatoriu.',
            'loc-nastere.max' => 'Locul nașterii nu poate avea mai mult de 255 de caractere.',
            'limba-materna.required' => 'Limba maternă este obligatorie.',
            'limba-materna.in' => 'Limba maternă selectată nu este validă.',
            'custom-limba-materna.required_if' => 'Vă rugăm să specificați limba maternă.',
            'custom-limba-materna.max' => 'Limba maternă nu poate avea mai mult de 100 de caractere.',
            'nr-angajator.required' => 'Numărul atribuit de angajator este obligatoriu.',
            'nr-angajator.integer' => 'Numărul atribuit de angajator trebuie să fie un număr întreg.',
            'nr-angajator.min' => 'Numărul atribuit de angajator trebuie să fie cel puțin 1.',
            'nr-angajator.max' => 'Numărul atribuit de angajator nu poate fi mai mare de 999999.',
            'judet.required' => 'Județul domiciliului este obligatoriu.',
            'judet.max' => 'Județul nu poate avea mai mult de 100 de caractere.',

            // Optional field messages
            'input-in-tara.max' => 'Identificatorul țării nu poate avea mai mult de 255 de caractere.',
        ]);

        // Add custom CNP validation
        $validator->after(function ($validator) use ($request) {
            if (!valid_CNP($request->input('permis-cnp'))) {
                $validator->errors()->add('permis-cnp', 'CNP-ul introdus nu este valid.');
            }
        });

        if ($validator->fails()) {
            Log::info('Nu trece de validare store' . $validator->errors());
            return back()->withInput()->withErrors($validator);
        }
        Log::info('trece de validare store');

        $exists = PermiseActive::where('cnp', $request->input('permis-cnp'))->exists();

        if ($exists) {
            return back()->withInput()->with('message', 'Utilizatorul are deja un permis.');
        }

        // Store the permit data to database
        try {
            $permis = new PermiseActive();

            // Basic information from first form
            $permis->cnp = $request->input('permis-cnp');
            $permis->nume = $request->input('permis-nume');
            $permis->prenume = $request->input('permis-prenume');

            // Information from second form - find UnitatiFeroviare by unitate name and get its id
            $unitateFeroviara = UnitatiFeroviare::where('unitate', $request->input('unitati-feroviare'))->first();
            if (!$unitateFeroviara) {
                return back()->withInput()->with('error', 'Unitatea feroviară selectată nu a fost găsită.');
            }
            $permis->id_unitate = $unitateFeroviara->firma;
            $permis->loc_nastere = $request->input('loc-nastere');

            // Handle mother language
            $limbaMaterna = $request->input('limba-materna');
            if ($limbaMaterna === 'alta') {
                $permis->limba_materna = $request->input('custom-limba-materna');
            } else {
                $permis->limba_materna = $limbaMaterna;
            }

            $permis->numar_atribuit_angajator = $request->input('nr-angajator');
            $permis->judet_domiciliu = $request->input('judet');

            // Handle medical restrictions as individual boolean fields
            $permis->utilizare_ochelari = $request->has('restrictii-medicale-1');
            $permis->utilizare_proteza = $request->has('restrictii-medicale-2');


            // Set default values and timestamps
            $permis->data_emitere = now();
            $permis->status = 'activ';
            $permis->tara = 'RO'; // Default to Romania
            $permis->nationalitate = 'română'; // Default nationality
            $permis->an = now()->year;


            $permis->save();

            Log::info('Permis salvat cu succes pentru CNP: ' . $request->input('permis-cnp'));

            return redirect()->route('permise.index')->with('success', 'Permisul a fost creat cu succes!');

        } catch (\Exception $e) {
            Log::error('Eroare la salvarea permisului: ' . $e->getMessage());
            return back()->withInput()->with('error', 'A apărut o eroare la salvarea permisului. Vă rugăm să încercați din nou.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {


    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {

        $unitatiFeroviare = UnitatiFeroviare::all();
        $permis = Permis::findOrFail($id);

        return view('permis.edit', compact('permis', 'unitatiFeroviare'));
    }

    public function search(Request $request)
    {
        $part1 = $request->input('part1');
        $part2 = $request->input('part2');
        $year = $request->input('year');
        $part4 = $request->input('part4');


        $searchTerm = $part1 . ' ' . $part2 . ' ' . $year . ' ' . $part4;

        $permis = Permis::where('numar_permis_NEI', $searchTerm)->first();

        $unitatiFeroviare = UnitatiFeroviare::all();

        return view('permis.edit', compact('permis', ), compact('unitatiFeroviare'));
    }
}
// 1. Prenume titular
// 2. Nume titular
// 3. Data si locul nasterii titular
// 4a. Data eliberarii permisului(AN/LUNA/ZI)
// 4b. Data expirarii permisului (AN/LUNA/ZI)
// 4c. Numele autoritatii emitente
// 4d. Numar identificare angajat
// 5. Numar permis
// 6. Fotografie titular
// 7. Semnatura titular
// 8. Domiciliu titular(optional) ????????????????
// 9a.1 Limba materna
// 9a.2 Informatii suplimentare ????????????????
// 9b. Restrictii medicale ????????????????

