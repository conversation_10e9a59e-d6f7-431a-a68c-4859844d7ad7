<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PermisSpecial; 
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;
class permisSpecialController extends Controller
{
    public function permisSpecial() {
        return view('permisSpecial.create');
    }


    public function index()
    {
        $columns = [
            'id' => 'ID',
            'tip_permis' => 'TIP PERMIS',
            'nr_serie' => 'NR. SERIE',
            'nume' => 'NUME',
            'prenume' => 'PRENUME',
            'functie' => 'FUNCȚIE',
            'unitate' => 'UNITATE',
            'ministru' => 'MINISTRU',
            'data_emitere' => 'DATĂ EMITERE',
            'data_valabilitate' => 'DATĂ VALABILITATE',
            'poza' => 'POZĂ'
        ];

        $selectColumns = array_keys($columns); 

        $query = PermisSpecial::select(array_keys($columns))->orderBy('id', 'DESC');


        $dateTabel = $query->paginate(10);

        $menus = config('customVariables.menus');

        return view('permisSpecial.vizualizare', compact('menus', 'dateTabel', 'columns'));
    }


    public function store(Request $request){
        // dd($request->all());
        Log::info('pozaaa' . $request);
        $request->validate([
            'tip_permis' => 'required|string|max:255',
            'nr_serie' => 'required|string|max:255',
            'nume' => 'required|string|max:255',
            'prenume' => 'required|string|max:255',
            'functie' => 'required|string|max:255',
            'unitate' => 'required|string|max:255',
            'ministru' => 'required|string|max:255',
            'data_emitere' => 'required',
            'data_valabilitate' => 'required',
            'poza' => 'nullable',
        ]);
    
        try {
            Log::info('poza' . $request->poza);
            if ($request->has('poza')) {
                $imageData = $request->poza;

                if (strpos($imageData, 'data:image') === 0) {
                    $imageData = explode(',', $imageData)[1];
                }

                $image = base64_decode($imageData);

                if ($image === false) {
                    throw new \Exception('Imagine invalidă (nu s-a putut decoda)');
                }

                $imageName = uniqid() . '.png';
                $path = public_path('uploads/images/' . $imageName);

                file_put_contents($path, $image);
        
                $permis = PermisSpecial::create([
                    'tip_permis' => $request->tip_permis,
                    'nr_serie' => $request->nr_serie,
                    'nume' => $request->nume,
                    'prenume' => $request->prenume,
                    'functie' => $request->functie,
                    'unitate' => $request->unitate,
                    'ministru' => $request->ministru,
                    'data_emitere' => $request->data_emitere,
                    'data_valabilitate' => $request->data_valabilitate,
                    'poza' => 'uploads/images/' . $imageName,
                ]);
            } else {
                $permis = PermisSpecial::create([
                    'tip_permis' => $request->tip_permis,
                    'nr_serie' => $request->nr_serie,
                    'nume' => $request->nume,
                    'prenume' => $request->prenume,
                    'functie' => $request->functie,
                    'unitate' => $request->unitate,
                    'ministru' => $request->ministru,
                    'data_emitere' => $request->data_emitere,
                    'data_valabilitate' => $request->data_valabilitate,
                    'poza' => null,
                ]);
            }
        
            return response()->json(['message' => 'Permis salvat']);
        } catch (\Exception $e) {
            Log::info('msj' . $e);
            return response()->json(['message' => 'Eroare server: ' . $e->getMessage()], 500);
        }
        
        
    }


    public function genereazaPdf($id)
    {
        $permis = PermisSpecial::findOrFail($id);
        $nume = $permis->nume;
        $prenume = $permis->prenume;

        $pdf = Pdf::loadView('permisSpecial', compact('permis'));

        return $pdf->download('permis_'.$nume.'_'.$prenume.'.pdf');
    }

}