<?php

namespace App\Http\Controllers;

use App\Models\Cenafer;
use App\Models\Comisie;
use App\Models\Tarife;
use App\Models\TipComisie;
use Illuminate\Http\Request;
use App\Models\Solicitari;
use App\Models\isfuri;
use App\Models\UnitatiFeroviare;
use App\Models\Functii;
use App\Models\ArticoleSuspendare;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;


function valid_CNP($input)
{

    if (strlen($input) != 13 || !ctype_digit($input)) {
        return false;
    }


    for ($i = 0; $i <= 12; $i++) {
        $cnp[] = intval($input[$i]);
    }


    if ($cnp[0] < 1 || $cnp[0] > 9) {
        return false;
    }


    $an = ($cnp[0] == 1 || $cnp[0] == 2) ? 1900 : (($cnp[0] == 3 || $cnp[0] == 4) ? 1800 : 2000);
    $an += $cnp[1] * 10 + $cnp[2];


    $luna = $cnp[3] * 10 + $cnp[4];
    if ($luna < 1 || $luna > 12) {
        return false;
    }


    $zi = $cnp[5] * 10 + $cnp[6];
    if ($zi < 1 || $zi > 31) {
        return false;
    }


    $judet = $cnp[7] * 10 + $cnp[8];
    if ($judet < 1 || $judet > 52) {
        return false;
    }


    $coef = [2, 7, 9, 1, 4, 6, 3, 5, 8, 2, 7, 9];
    $suma = 0;
    for ($i = 0; $i <= 11; $i++) {
        $suma += $cnp[$i] * $coef[$i];
    }


    $rest = $suma % 11;


    if (($rest < 10 && $rest == $cnp[12]) || ($rest == 10 && $cnp[12] == 1)) {
        return true;
    } else {
        return false;
    }
}



class solicitariController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        Log::info('A fost apelată metoda index2.');
        $columns = [
            'nr_comisie',
            'tip_comisie',
            'solicitant_nume',
            'solicitant_prenume',
            'cnp',
            'nr_aut',
            'serie_aut',
            'data_elib',
            'valabilitate',
            'nume',
            'prenume',
            'functie',
            'domeniu',
            'autorizatie',
            'unitate',
            'localitate_om',
            'adresa_om',
            'serie_ci',
            'nr_ci',
            'data_suspend1',
            'data_suspend2',
            'literaOMTCT',
            'tiparit',
            'oameni',
            'data_reemiterii',
            'directorASFR',
            'status',
            'id_solicitare_pj',
            'ids_aut_anterioare',
            'id_isf'

        ];

        //$columns = auth()->user()->isAdmin() ? $adminColumns : $userColumns;
        $isAdmin = auth()->user()->hasRole('admin');
        $isSuperAdmin = auth()->user()->hasRole('super_admin');
        $isRegularUser = auth()->user()->hasRole('user');


        $cnp = $request->input('cnp');
        $search = $request->input('search');
        $isf = $request->input('isf');

        $query = Solicitari::select(...$columns)->orderBy('oameni', 'DESC');

        // $query = Solicitari::leftJoin('comisie', 'personal.nr_comisie', '=', 'comisie.nr_comisie') // adaugă join-ul
        //     ->selectRaw(implode(', ', array_merge($columns, [
        //         'comisie.nr_ISF as comisie_nr_ISF', // adăugăm coloanele din comisie
        //         'comisie.data_ISF as comisie_data_ISF'
        //     ])))
        //     ->orderBy('personal.oameni', 'DESC');


        if ($isRegularUser) {
            $nr_ISF_user = auth()->user()->isf;

            $isf_user = isfuri::where('cod_serviciu', $nr_ISF_user)->first();

            $id_isf_user = $isf_user->nr_ISF;
            $denumire_isf = $isf_user->ISF;


            $query->where(function ($q) use ($id_isf_user, $denumire_isf) {
                $q->where('id_isf', $id_isf_user)
                    ->orWhereIn('nr_comisie', function ($subquery) use ($denumire_isf) {
                        $subquery->select('nr_comisie')
                            ->from('comisie')
                            ->whereRaw('LEFT(ISF, 2) = ?', [substr($denumire_isf, 0, 2)]);
                    });
            });
        }

        if (!empty($cnp)) {
            $query->where('cnp', $cnp);
        }

        if (!empty($search)) {

            $comisii = Comisie::where('nr_ISF', 'like', '%' . $search . '%')->get();
            if ($comisii->isNotEmpty()) {
                Log::info('nr comisie' . $comisii[0]->nr_comisie);

                $query->whereIn(
                    'nr_comisie',
                    $comisii->map(function ($comisie) {
                        return $comisie->nr_comisie;
                    })
                );
            }
        }
        if (!empty($isf)) {
            $query->where('id_isf', $isf);
        }

        $dateTabel = $query->paginate(5);
        Log::info('date tabel: ' . json_encode($dateTabel));

        foreach ($dateTabel as $item) {
            if (strtolower($item->tip_comisie) === 'reprogramare' || strtolower($item->tip_comisie) === 'reexaminare') {

                if ($item->id_comisie_ant_scmap && $item->id_comisie_ant_scmap !== 0) {
                    $solicitareAnt = Solicitari::where('nr_comisie', $item->id_comisie_ant_scmap)->where('cnp', $item->cnp)->first();
                    $item->aut_anterioara = $solicitareAnt;
                } else {
                    Log::info('item nr comisie ' . $item->nr_comisie);
                    Log::info('item id isf ' . $item->id_isf);
                    Log::info('item nr_aprobare ' . $item->oameni);
                    $comisieItem = Comisie::where('nr_comisie', $item->nr_comisie)->first();
                    $comisiiNrIsf = Comisie::where('nr_ISF', $comisieItem->nr_aprobare)->where('data_ISF', $comisieItem->data_aprobare)->orderBy('nr_comisie')->get();
                    Log::info('Query res: ' . json_encode($comisiiNrIsf));
                    Log::info('Query SQL: ' . Comisie::where('nr_ISF', $item->nr_aprobare)->toSql());
                    Log::info('Query bindings: ' . json_encode(Comisie::where('nr_ISF', $item->nr_aprobare)->getBindings()));
                    Log::info('Found comisii count: ' . $comisiiNrIsf->count());
                    foreach ($comisiiNrIsf as $comisieNrIsf) {
                        Log::info('comisieNrIsf: ' . json_encode($comisieNrIsf));

                        $solicitareIdIsf = Solicitari::where('nr_comisie', $comisieNrIsf->nr_comisie)->first();
                        Log::info('solicitareIdIsf: ' . json_encode($solicitareIdIsf));
                        if ($solicitareIdIsf === null) {
                            continue;
                        }
                        if ($solicitareIdIsf->id_isf === $item->id_isf) {
                            $item->aut_anterioara = $solicitareIdIsf;
                            break;
                        }
                    }
                    Log::info('item aut ant ' . $item->aut_anterioara);
                }

            }
        }

        $menus = config('customVariables.menus');
        $customFilter = [
            'menus' => [
                [
                    'title' => 'Filtrează',
                    'submenu' => [
                        [
                            'title' => 'Regională',
                            'submenu' => [
                                ['title' => 'București', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 1]))],
                                ['title' => 'Brașov', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 2]))],
                                ['title' => 'Craiova', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 3]))],
                                ['title' => 'Timișoara', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 4]))],
                                ['title' => 'Cluj', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 5]))],
                                ['title' => 'Iași', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 6]))],
                                ['title' => 'Galați', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 7]))],
                                ['title' => 'Constanța', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 8]))],
                            ],
                        ],
                    ],
                ],
            ]
        ];
        $filterMenus = [];
        if (!auth()->user()->hasRole('user')) {
            Log::info('user is not regular');
            $filterMenus = $customFilter['menus'];
        }

        return view('solicitari.specific', compact('menus', 'dateTabel', 'columns', 'filterMenus'));
    }

    public function indexTotDespreOPersoana(Request $request)
    {
        Log::info('A fost apelată metoda indexTotDespreOPersoana.');
        $columns = [
            'nr_comisie',
            'tip_comisie',
            'solicitant_nume',
            'solicitant_prenume',
            'cnp',
            'nr_aut',
            'serie_aut',
            'data_elib',
            'valabilitate',
            'nume',
            'prenume',
            'functie',
            'domeniu',
            'autorizatie',
            'unitate',
            'localitate_om',
            'adresa_om',
            'serie_ci',
            'nr_ci',
            'data_suspend1',
            'data_suspend2',
            'literaOMTCT',
            'tiparit',
            'oameni',
            'data_reemiterii',
            'directorASFR',
            'status',
            'id_solicitare_pj'
        ];

        $columnsInRegistru = [
            'cum_e',
            'solicitant_nume',
            'solicitant_prenume',
            'cnp',
            'functie',
            'domeniu',
            'autorizatie',
            'unitate',
            'nr_aut',
            'serie_aut',
            'nr_permis',
            //nr_permis UE
            'data_elib',
            'valabilitate',
            'data_suspend1',
            'data_suspend2',
            'data_retrag',
            'normativ',
            'literaOMTCT',
            //Solicitant(nume pj sau pf)
            'mat1_nota1',
            'mat1_nota2',
            'mat1_nota3',
            'mat1_nota4',
            'mat1_nota5',
            'mat2_nota1',
            'mat2_nota2',
            'mat2_nota3',
            'mat2_nota4',
            'mat2_nota5',
            'mat3_nota1',
            'mat3_nota2',
            'mat3_nota3',
            'mat3_nota4',
            'mat3_nota5',
            'mat1_medie',
            'mat2_medie',
            'mat3_medie',
            'medie_t',
            'calificativ_t',
            'calificativ_p',
            'calificativ',
            'director_general',
            'minister',
            'data_reemiterii'
        ];

        $columnsInLucru = [
            'solicitant_nume',
            'solicitant_prenume',
            'cnp',
            'functie',
            'autorizatie',
            'domeniu',
            //Activitate
            'serie_ci',
            'nr_ci',
            'data_elib',
            'valabilitate',
            //Solicitant(nume pj sau pf)
            'mat1_nota1',
            'mat1_nota2',
            'mat1_nota3',
            'mat1_nota4',
            'mat1_nota5',
            'mat2_nota1',
            'mat2_nota2',
            'mat2_nota3',
            'mat2_nota4',
            'mat2_nota5',
            'mat3_nota1',
            'mat3_nota2',
            'mat3_nota3',
            'mat3_nota4',
            'mat3_nota5',
            'mat1_medie',
            'mat2_medie',
            'mat3_medie',
            'medie_t',
            'calificativ_t',
            'calificativ_p',
            'calificativ',
            'director_general',
            'minister'
        ];

        $columnsInArhiva = [
            'solicitant_nume',
            'solicitant_prenume',
            'cnp',
            'functie',
            'autorizatie',
            'domeniu',
            //Activitate
            'serie_ci',
            'nr_ci',
            'data_elib',
            'valabilitate',
            'mat1_nota1',
            'mat1_nota2',
            'mat1_nota3',
            'mat1_nota4',
            'mat1_nota5',
            'mat2_nota1',
            'mat2_nota2',
            'mat2_nota3',
            'mat2_nota4',
            'mat2_nota5',
            'mat3_nota1',
            'mat3_nota2',
            'mat3_nota3',
            'mat3_nota4',
            'mat3_nota5',
            'mat1_medie',
            'mat2_medie',
            'mat3_medie',
            'medie_t',
            'calificativ_t',
            'calificativ_p',
            'calificativ',
            'director_general',
            'minister'
        ];


        // $userColumns = [
        //     'nume', //personal-solicitant_nume
        //     'prenume', //personal-solicitant_prenume
        //     'nr_solicitare',
        //     'redactat', //comisie - redactat
        //     'data_ora_redactat', //comisie - data_ora_redactat
        //     'status'
        // ];

        //$columns = auth()->user()->isAdmin() ? $adminColumns : $userColumns;

        $cnp = $request->input('cnp');
        $search = $request->input('search');

        $isAdmin = auth()->user()->hasRole('admin');
        $isSuperAdmin = auth()->user()->hasRole('super_admin');
        $isRegularUser = auth()->user()->hasRole('user');

        $queryInLucru = Solicitari::select(...$columnsInLucru)->whereNotIn('status', ['AVIZE GENERATE', 'NECORESPUNZATOR']);
        $queryFinalizare = Solicitari::select(...$columnsInRegistru)->where(function ($q) {
            $q->where('status', 'AVIZE GENERATE')->orWhere('status', 'NECORESPUNZATOR');
        })->where('valabilitate', '>=', Carbon::now());
        $queryArhiva = Solicitari::select(...$columnsInArhiva)->where('valabilitate', '<', Carbon::now());



        Log::info('Coloane selectate: ', $columns);
        Log::info('CNP: ' . $cnp);
        Log::info('Search: ' . $search);

        if ($isRegularUser) {
            $nr_ISF_user = auth()->user()->isf;

            $isf_user = isfuri::where('cod_serviciu', $nr_ISF_user)->first();

            $id_isf_user = $isf_user->nr_ISF;
            $denumire_isf = $isf_user->ISF;


            $queryInLucru->where(function ($q) use ($id_isf_user, $denumire_isf) {
                $q->where('id_isf', $id_isf_user)
                    ->orWhereIn('nr_comisie', function ($subquery) use ($denumire_isf) {
                        $subquery->select('nr_comisie')
                            ->from('comisie')
                            ->whereRaw('LEFT(ISF, 2) = ?', [substr($denumire_isf, 0, 2)]);
                    });
            });
            $queryFinalizare->where(function ($q) use ($id_isf_user, $denumire_isf) {
                $q->where('id_isf', $id_isf_user)
                    ->orWhereIn('nr_comisie', function ($subquery) use ($denumire_isf) {
                        $subquery->select('nr_comisie')
                            ->from('comisie')
                            ->whereRaw('LEFT(ISF, 2) = ?', [substr($denumire_isf, 0, 2)]);
                    });
            });
            $queryArhiva->where(function ($q) use ($id_isf_user, $denumire_isf) {
                $q->where('id_isf', $id_isf_user)
                    ->orWhereIn('nr_comisie', function ($subquery) use ($denumire_isf) {
                        $subquery->select('nr_comisie')
                            ->from('comisie')
                            ->whereRaw('LEFT(ISF, 2) = ?', [substr($denumire_isf, 0, 2)]);
                    });
            });
        }

        if (!empty($cnp)) {
            $queryInLucru->where('cnp', $cnp);
            $queryFinalizare->where('cnp', $cnp);
            $queryArhiva->where('cnp', $cnp);
        }


        if ($search) {
            if (ctype_digit($search)) {
                $subqueryFinalizare = DB::table('personal')
                    ->select('cnp', 'functie', 'autorizatie', 'domeniu')
                    ->where('cnp', 'like', '%' . $search . '%')
                    ->distinct()
                    ->get();

                Log::info('subqueryFinalizare = ' . json_encode($subqueryFinalizare));

                $oameniIds = [];
                foreach ($subqueryFinalizare as $item) {
                    $queryFinalizare2 = DB::table('personal')->select('oameni')->where('cnp', $item->cnp)->where('functie', $item->functie)->where('autorizatie', $item->autorizatie)->where('domeniu', $item->domeniu)->orderBy('oameni', 'desc')->first();
                    if ($queryFinalizare2) {
                        $oameniIds[] = $queryFinalizare2->oameni;
                    }
                }
                Log::info('arr = ' . json_encode($oameniIds));
                $queryInLucru->where('cnp', 'like', '%' . $search . '%');
                $queryFinalizare->whereIn('oameni', $oameniIds);
                $queryArhiva->where('cnp', 'like', '%' . $search . '%');
            } else {
                $words = preg_split('/\s+/', trim($search));
                $queryInLucru->where(function ($secondaryQuery) use ($words) {
                    if (count($words) === 1) {
                        // Search the single word in both 'solicitant_nume' and 'solicitant_prenume'
                        $secondaryQuery->where('solicitant_nume', 'LIKE', "%{$words[0]}%")
                            ->orWhere('solicitant_prenume', 'LIKE', "%{$words[0]}%");
                    } else if (count($words) > 1) {
                        // Search the first word in 'solicitant_nume'
                        $secondaryQuery->where('solicitant_nume', 'LIKE', "%{$words[0]}%")->where('solicitant_prenume', 'LIKE', "%$words[1]%");


                    }
                });
                $queryFinalizare->where(function ($secondaryQuery) use ($words) {
                    if (count($words) === 1) {
                        // Search the single word in both 'solicitant_nume' and 'solicitant_prenume'
                        $secondaryQuery->where('solicitant_nume', 'LIKE', "%{$words[0]}%")
                            ->orWhere('solicitant_prenume', 'LIKE', "%{$words[0]}%");
                    } else if (count($words) > 1) {
                        // Search the first word in 'solicitant_nume'
                        $secondaryQuery->where('solicitant_nume', 'LIKE', "%{$words[0]}%")->where('solicitant_prenume', 'LIKE', "%$words[1]%");


                    }
                });
                $queryArhiva->where(function ($secondaryQuery) use ($words) {
                    if (count($words) === 1) {
                        // Search the single word in both 'solicitant_nume' and 'solicitant_prenume'
                        $secondaryQuery->where('solicitant_nume', 'LIKE', "%{$words[0]}%")
                            ->orWhere('solicitant_prenume', 'LIKE', "%{$words[0]}%");
                    } else if (count($words) > 1) {
                        // Search the first word in 'solicitant_nume'
                        $secondaryQuery->where('solicitant_nume', 'LIKE', "%{$words[0]}%")->where('solicitant_prenume', 'LIKE', "%$words[1]%");


                    }
                });
            }

        }

        $dateTabelInLucru = $queryInLucru->paginate(5);
        $dateTabelFinalizare = $queryFinalizare->paginate(5);
        $dateTabelArhiva = $queryArhiva->paginate(5);


        // $distinctInLucru = DB::table('personal')
        //     ->select($columnsInLucru)
        //     ->where('cnp', $cnp)
        //     ->groupBy('functie', 'autorizatie', 'domeniu')
        //     ->get()->paginate(5);

        $menus = config('customVariables.menus');
        $customFilter = [
            'menus' => [
                [
                    'title' => 'Filtrează',
                    'submenu' => [
                        [
                            'title' => 'Regională',
                            'submenu' => [
                                ['title' => 'București', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 1]))],
                                ['title' => 'Brașov', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 2]))],
                                ['title' => 'Craiova', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 3]))],
                                ['title' => 'Timișoara', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 4]))],
                                ['title' => 'Cluj', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 5]))],
                                ['title' => 'Iași', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 6]))],
                                ['title' => 'Galați', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 7]))],
                                ['title' => 'Constanța', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 8]))],
                            ],
                        ],
                    ],
                ],
            ]
        ];
        $filterMenus = $customFilter['menus'];

        return view('totDespreOPersoana', compact('menus', 'dateTabelInLucru', 'columnsInLucru', 'columnsInRegistru', 'columnsInArhiva', 'dateTabelFinalizare', 'dateTabelArhiva', 'filterMenus'));
    }



    public function indexGrouped(Request $request)
    {
        $columns = [
            'nume',
            'prenume',
            'cnp'
        ];

        $search = $request->input('search');

        $query = DB::table('personal')
            ->select(DB::raw('solicitant_nume as nume'), DB::raw('solicitant_prenume as prenume'), DB::raw('MAX(cnp) as cnp'))
            ->groupBy('cnp', 'solicitant_nume', 'solicitant_prenume')
            ->useIndex('cnp');

        $isRegularUser = auth()->user()->hasRole('user');
        if ($isRegularUser) {
            $nr_ISF_user = auth()->user()->isf;

            $isf_user = isfuri::where('cod_serviciu', $nr_ISF_user)->first();

            $id_isf_user = $isf_user->nr_ISF;
            $denumire_isf = $isf_user->ISF;


            $query->where(function ($q) use ($id_isf_user, $denumire_isf) {
                $q->where('id_isf', $id_isf_user)
                    ->orWhereIn('nr_comisie', function ($subquery) use ($denumire_isf) {
                        $subquery->select('nr_comisie')
                            ->from('comisie')
                            ->whereRaw('LEFT(ISF, 2) = ?', [substr($denumire_isf, 0, 2)]);
                    });
            });
        }



        if ($search) {
            if (ctype_digit($search)) {
                $query->having('cnp', 'like', '%' . $search . '%');
            } else {
                $words = preg_split('/\s+/', trim($search));
                $query->where(function ($secondaryQuery) use ($words) {
                    if (count($words) === 1) {
                        // Search the single word in both 'solicitant_nume' and 'solicitant_prenume'
                        $secondaryQuery->where('solicitant_nume', 'LIKE', "%{$words[0]}%")
                            ->orWhere('solicitant_prenume', 'LIKE', "%{$words[0]}%");
                    } else if (count($words) > 1) {
                        // Search the first word in 'solicitant_nume'
                        $secondaryQuery->where('solicitant_nume', 'LIKE', "%{$words[0]}%")->where('solicitant_prenume', 'LIKE', "%$words[1]%");


                    }
                });
            }

        }

        $dateTabel = $query->paginate(10);
        $dateTabel->appends(['search' => $search]);

        $menus = config('customVariables.menus');
        $customFilter = [
            'menus' => [
                [
                    'title' => 'Filtrează',
                    'submenu' => [
                        [
                            'title' => 'Regională',
                            'submenu' => [
                                ['title' => 'București', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 1]))],
                                ['title' => 'Brașov', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 2]))],
                                ['title' => 'Craiova', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 3]))],
                                ['title' => 'Timișoara', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 4]))],
                                ['title' => 'Cluj', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 5]))],
                                ['title' => 'Iași', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 6]))],
                                ['title' => 'Galați', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 7]))],
                                ['title' => 'Constanța', 'url' => '?' . http_build_query(array_merge(request()->query(), ['isf' => 8]))],
                            ],
                        ],
                    ],
                ],
            ]
        ];
        $filterMenus = $customFilter['menus'];

        return view('solicitari.grouped', compact('menus', 'columns', 'dateTabel', 'search', 'filterMenus'));
    }

    public function getTipAutorizatii(Request $request)
    {
        $selectedDataId = $request->input('selectedDataId');
        $functiiSelectedNodes = [];

        while ($selectedDataId !== '0') {
            $node = Functii::select(['tip_aut', 'tata', 'domeniu'])->where('tip_aut', $selectedDataId)->first();

            if (!$node) {
                break;
            }

            $functiiSelectedNodes[] = $node->toArray(); // Convert model to array
            $selectedDataId = $node->tata;
        }

        // Return the functiiSelectedNodes as JSON response
        return response()->json(['functiiSelectedNodes' => array_reverse($functiiSelectedNodes)]);
    }



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {

        $functions = Functii::where('tata', 0)->with('children')->get();

        $tipSolicitare = TipComisie::all();

        $unitatiColumns = [
            'unitate',
            'cod_fiscal',
        ];
        Log::info('colsUnit' . $unitatiColumns[0]);
        $unitatiFeroviare = UnitatiFeroviare::orderBy('unitate')->get();

        $optiuniArtSuspendare = ArticoleSuspendare::get();

        $cenaferuri = Cenafer::all();
        $isfuri = isfuri::all();
        return view('solicitari.create', compact('unitatiFeroviare', 'unitatiColumns', 'tipSolicitare', 'functions', 'cenaferuri', 'isfuri', 'optiuniArtSuspendare'));
        // la introducere serie si numar CI, introducem in personal petru CNP ul respectiv
    }


    /**
     * Store a newly created resource in storage.
     */
    public function storePersFizica(Request $request)
    {
        function storeType3SolicitarePf($request, $idsAutorizatiiAnterioare)
        {
            DB::beginTransaction();
            try {

                $comisieNoua = new Comisie();
                $comisieNoua->cerere_uf_nr = $request->input('numarCerereSolicitant');
                $comisieNoua->cerere_uf_data = $request->input('dataCerereSolicitant');
                $comisieNoua->CENAFER = $request->input('locatieCenafer');
                $comisieNoua->nr_CENAFER = $request->input('numarCenafer');
                $comisieNoua->data_CENAFER = $request->input('dataCenafer');
                $comisieNoua->nr_ISF = $request->input('numarIesireSolicitare');
                $comisieNoua->data_ISF = $request->input('dataIesireSolicitare');
                $comisieNoua->redactat = $request->input('redactataDeSolicitare');
                $comisieNoua->data_ora_redactat = $request->input('dataOraRedactareSolicitare');


                $columnsComisie = [
                    'nume_cenafer1',
                    'prenume_cenafer1',
                    'serie_cenafer1',
                    'numar_cenafer1',
                    'nume_cenafer2',
                    'prenume_cenafer2',
                    'serie_cenafer2',
                    'numar_cenafer2',
                    'loc_exam_t',
                    'data_exam_t1',
                    'ora_exam_t1',
                    'loc_exam_p',
                    'data_exam_p1',
                    'ora_exam_p1',
                    'urgent',
                    'nr_aprobare',
                    'data_aprobare',
                    'tip_comisie_id',
                    'nr_ISF',
                    'data_ISF'

                ];

                $querySolicitari = Solicitari::select()->where('oameni', $idsAutorizatiiAnterioare[0])->first();
                Log::info(json_encode($querySolicitari));
                $queryComisie = Comisie::select(...$columnsComisie)->where('nr_comisie', $querySolicitari->nr_comisie)->first();
                Log::info($queryComisie);
                //trebuie trase din  autorizatia precedenta
                $comisieNoua->nume_cenafer1 = $queryComisie->nume_cenafer1 || '';
                $comisieNoua->prenume_cenafer1 = $queryComisie->prenume_cenafer1;
                $comisieNoua->serie_cenafer1 = $queryComisie->serie_cenafer1;
                $comisieNoua->numar_cenafer1 = $queryComisie->numar_cenafer1;
                $comisieNoua->nume_cenafer2 = $queryComisie->nume_cenafer2;
                $comisieNoua->prenume_cenafer2 = $queryComisie->prenume_cenafer2;
                $comisieNoua->serie_cenafer2 = $queryComisie->serie_cenafer2;
                $comisieNoua->numar_cenafer2 = $queryComisie->numar_cenafer2;
                $comisieNoua->loc_exam_t = $queryComisie->loc_exam_t;
                $comisieNoua->data_exam_t1 = $queryComisie->data_exam_t1;
                $comisieNoua->ora_exam_t1 = $queryComisie->ora_exam_t1;
                $comisieNoua->loc_exam_p = $queryComisie->loc_exam_p;
                $comisieNoua->data_exam_p1 = $queryComisie->data_exam_p1;
                $comisieNoua->ora_exam_p1 = $queryComisie->ora_exam_p1;
                $comisieNoua->urgent = $queryComisie->urgent;
                $comisieNoua->nr_aprobare = $queryComisie->nr_aprobare;
                $comisieNoua->data_aprobare = $queryComisie->data_aprobare;
                $comisieNoua->tip_comisie_id = $queryComisie->tip_comisie_id;

                $tarif = Tarife::get()->where('tip_solicitare', $request->input('tipSolicitare'))->first();
                Log::info('tarif' . $tarif);
                if ($tarif) {
                    $tarifTotal = $tarif['tarif_orar'] * $tarif['nr_ore'];

                    $comisieNoua->tarif_total = $tarifTotal;
                }


                $comisieNoua->save();
                Log::info('created new comisie');

                $comisieNouaId = $comisieNoua->nr_comisie;
                Log::info('comisie noua id' . $comisieNouaId);
                $solicitariAnterioareUtilizator = Solicitari::select()->whereIn('oameni', $idsAutorizatiiAnterioare)->where('cnp', $request->input('solicitantPersFizicaCnp'))->get();
                while ($solicitariAnterioareUtilizator->isNotEmpty()) {
                    $solicitareAnterioaraUtilizator = $solicitariAnterioareUtilizator->shift(); // Remove and process the first item

                    Log::info('iterating over ' . json_encode($solicitareAnterioaraUtilizator));

                    $solicitare = new Solicitari();
                    // Daca este autorizare in functie, adaugam si suspendari pt tot ce e sub acea functie
                    if (strtolower($request->input('tipSolicitare')) === 'suspendare' && $solicitareAnterioaraUtilizator->autorizatie === 'Autorizare în funcție' && empty($solicitareAnterioaraUtilizator->domeniu)) {
                        Log::info('suspendare autorizare in functie');
                        $solicitariCeTrebuieSuspendate = Solicitari::selectRaw('domeniu, autorizatie, GROUP_CONCAT(oameni) as oameni_grupati')
                            ->where('cnp', $solicitareAnterioaraUtilizator->cnp)
                            ->where('tip_comisie', '!=', 'Suspendare')
                            ->where('functie', $solicitareAnterioaraUtilizator->functie)
                            ->whereNotNull('domeniu')
                            ->where('domeniu', '!=', '')
                            ->groupBy('domeniu', 'autorizatie')
                            ->get();
                        Log::info('solicitari ce trebuie suspendate ' . json_encode($solicitariCeTrebuieSuspendate));

                        foreach ($solicitariCeTrebuieSuspendate as $solicitareCeTrebuieSusepndata) {
                            $solicitareGasita = Solicitari::where('oameni', explode(',', $solicitareCeTrebuieSusepndata->oameni_grupati)[0])->first();
                            $solicitariAnterioareUtilizator = $solicitariAnterioareUtilizator->merge([$solicitareGasita]);

                        }
                        Log::info('solicitari ant user ' . json_encode($solicitariAnterioareUtilizator));


                    }

                    $solicitare->fill($solicitareAnterioaraUtilizator->toArray()); // Copiem toate câmpurile din solicitarea anterioară
                    $solicitare->id_aut_anterioara = $solicitareAnterioaraUtilizator->oameni;



                    if (strtolower($request->input('tipSolicitare')) === 'vizeperiodice') {
                        $solicitare->data_elib = $solicitareAnterioaraUtilizator->data_elib;

                        // if ($solicitareAnterioaraUtilizator->valabilitate->greaterThan(Carbon::now()->addDays(45))) {
                        //     return response()->json(['message' => 'Solicitarea poate fi înregistrată doar cu 45 de zile înainte de a-i expira valabilitatea!'], 500);
                        // }

                        // Start with the initial date (either data_val1 or valabilitate)
                        $baseDate = !empty($solicitareAnterioaraUtilizator->data_val1) //2020-07-26
                            ? Carbon::parse($solicitareAnterioaraUtilizator->data_val1)
                            : Carbon::parse($solicitareAnterioaraUtilizator->valabilitate);

                        // Copy the original date for calculations
                        $currentDate = $baseDate->copy();

                        // Set data_val1 if it's empty
                        if (empty($solicitareAnterioaraUtilizator->data_val1)) {
                            $solicitare->data_val1 = $currentDate;
                        }
                        //elib 22-06-2015
                        // data val 1 21-06-2020

                        // data val 2 20-06-2025

                        // Populate all data_val fields with consecutive 5-year intervals
                        for ($i = 2; $i <= 6; $i++) {
                            $column = "data_val{$i}";

                            // Add 5 years to the current date
                            $currentDate = $currentDate->copy()->addYears(5)->subDays(1);

                            // Set the value for this column
                            $solicitare->$column = $currentDate;

                            // If this date is greater than today + 45 days, update valabilitate and stop
                            if ($currentDate->greaterThan(Carbon::now())) {
                                $solicitare->valabilitate = $currentDate;
                                break;
                            }
                        }

                        // If we've gone through all columns and still haven't found a future date,
                        // add another 5 years to the last date and set it as valabilitate
                        if ($currentDate->lessThanOrEqualTo(Carbon::now())) {
                            $finalDate = $currentDate->copy()->addYears(5)->subDays(1);
                            $solicitare->valabilitate = $finalDate;
                        }
                    } else {
                        $solicitare->data_elib = Carbon::parse($solicitareAnterioaraUtilizator->data_elib)->format('Y-m-d');
                        $solicitare->valabilitate = Carbon::parse($solicitareAnterioaraUtilizator->valabilitate)->format('Y-m-d');
                    }

                    $solicitare->nr_comisie = $comisieNouaId;
                    $solicitare->tip_comisie = $request->input('tipSolicitare');
                    $solicitare->id_isf = $request->input('id_isf');
                    $solicitare->solicitant_nume = $request->input('solicitantPersFizicaNume');
                    $solicitare->solicitant_prenume = $request->input('solicitantPersFizicaPrenume');
                    $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                    $solicitare->cnp = $request->input('solicitantPersFizicaCnp');
                    $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi');
                    $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate');
                    $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa');
                    $solicitare->status = 'IN LUCRU';
                    $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);

                    $solicitare->motiv_suspendare = $request->input('motivSuspendare');

                    $solicitare->incetare_suspendare_nr_aviz = $request->input('incetareSuspendareNrAviz');
                    $solicitare->incetare_suspendare_eliberat_aviz = $request->input('incetareSuspendareEliberatAviz');
                    $solicitare->incetare_suspendare_data_aviz = $request->input('incetareSuspendareDataAviz');

                    $solicitare->incetare_suspendare_nr_certificat = $request->input('incetareSuspendareNrCertificat');
                    $solicitare->incetare_suspendare_eliberat_certificat = $request->input('incetareSuspendareEliberatCertificat');
                    $solicitare->incetare_suspendare_data_certificat = $request->input('incetareSuspendareDataCertificat');


                    $solicitare->perioada_suspendare = $request->input('nrZileSuspendare');


                    $solicitare->save();

                    Log::info('created new solicitare');
                }

                DB::commit();

                Log::info('DB commited successfully');

                return response()->json(['message' => 'Solicitare înregistrată cu succes!'], 200);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error saving data', ['error' => $e->getMessage()]);
                return response()->json(['message' => 'Error saving data', 'error' => $e->getMessage()], 500);
            }
        }
        ;

        function storeDbRecords($request, $idsAutorizatiiAnterioare, $nrComisieSCMAP)
        {
            DB::beginTransaction();
            try {
                // noua intrare in Comisie
                $comisieNoua = new Comisie();
                $comisieNoua->cerere_uf_nr = $request->input('numarCerereSolicitant');
                $comisieNoua->cerere_uf_data = $request->input('dataCerereSolicitant');
                $comisieNoua->CENAFER = $request->input('locatieCenafer');
                $comisieNoua->nr_CENAFER = $request->input('numarCenafer');
                $comisieNoua->data_CENAFER = $request->input('dataCenafer');
                $comisieNoua->nume_cenafer1 = $request->input('numeMembruComisie');
                $comisieNoua->prenume_cenafer1 = $request->input('prenumeMembruComisie');
                // $comisieNoua->serie_cenafer1 = $request->input('serieAtestatMembru');
                // $comisieNoua->numar_cenafer1 = $request->input('numarAtestatMembru');
                $comisieNoua->nume_cenafer2 = $request->input('numeReprezentantCenafer');
                $comisieNoua->prenume_cenafer2 = $request->input('prenumeReprezentantCenafer');
                // $comisieNoua->serie_cenafer2 = $request->input('serieReprezentantCenafer');
                // $comisieNoua->numar_cenafer2 = $request->input('numarReprezentantCenafer');
                $comisieNoua->redactat = $request->input('redactataDeSolicitare');
                // $comisieNoua->pv_nr_iesire = $request->input('numarIesireSolicitare');
                $comisieNoua->nr_ISF = $request->input('numarIesireSolicitare');
                // $comisieNoua->pv_data_iesire = $request->input('dataIesireSolicitare');
                $comisieNoua->data_ISF = $request->input('dataIesireSolicitare');
                $comisieNoua->data_ora_redactat = $request->input('dataOraRedactareSolicitare');// coloana noua data_ora_redactat
                $comisieNoua->loc_exam_t = $request->input('locatieExaminareTeoretica');
                $dataExamT1 = date('Y-m-d', strtotime($request->input('dataInceputExaminareTeoretica')));
                $oraExamT1 = date('H:i:s', strtotime($request->input('dataInceputExaminareTeoretica')));
                $comisieNoua->data_exam_t1 = $dataExamT1;
                $comisieNoua->ora_exam_t1 = $oraExamT1;
                $comisieNoua->loc_exam_p = $request->input('locatieExaminarePractica');
                $dataExamP1 = date('Y-m-d', strtotime($request->input('dataInceputExaminarePractica')));
                $oraExamP1 = date('H:i:s', strtotime($request->input('dataInceputExaminarePractica')));
                $comisieNoua->data_exam_p1 = $dataExamP1;
                $comisieNoua->ora_exam_p1 = $oraExamP1;
                $comisieNoua->urgent = $request->input('regimUrgenta') === false ? 'Nu' : 'Da';
                $comisieNoua->nr_aprobare = $request->input('numarAprobareInitialaScmap');
                $comisieNoua->data_aprobare = $request->input('dataAprobareInitialaScmap');
                $comisieNoua->tip_comisie_id = $request->input('tipSolicitareId');

                $tarif = Tarife::get()->where('tip_solicitare', $request->input('tipSolicitare'))->first();
                Log::info('tarif' . $tarif);
                if ($tarif) {
                    $tarifTotal = $tarif['tarif_orar'] * $tarif['nr_ore'];

                    $comisieNoua->tarif_total = $tarifTotal;
                }


                $comisieNoua->save();
                Log::info('created new comisie');

                $comisieNouaId = $comisieNoua->nr_comisie;
                if (strtolower($request->input('tipSolicitare')) === 'reautorizare') {
                    $solicitariAnterioareUtilizator = Solicitari::select()->whereIn('oameni', $idsAutorizatiiAnterioare)->where('cnp', $request->input('solicitantPersFizicaCnp'))->get();
                    foreach ($solicitariAnterioareUtilizator as $solicitareAnterioaraUtilizator) {
                        $solicitare = new Solicitari();
                        $solicitare->nr_comisie = $comisieNouaId;
                        $solicitare->tip_comisie = $request->input('tipSolicitare');

                        $solicitare->id_isf = $request->input('id_isf'); // coloana noua cu referinta la ISFURI
                        $solicitare->nume = $request->input('numePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaNume
                        $solicitare->prenume = $request->input('prenumePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaPrenume
                        $solicitare->solicitant_nume = $request->input('solicitantPersFizicaNume');
                        $solicitare->solicitant_prenume = $request->input('solicitantPersFizicaPrenume');
                        // $solicitare->unitate = $request->input('unitateIsf'); // nu e necesar pt pers fizica
                        $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                        $solicitare->cnp = $request->input('solicitantPersFizicaCnp');
                        $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi');
                        $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate');
                        $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa');
                        $solicitare->functie = $request->input('functie');
                        $solicitare->autorizatie = $request->input('autorizare');
                        $solicitare->domeniu = $request->input('activitate');
                        $solicitare->directorASFR = 'Petru BOGDAN';
                        $solicitare->status = 'IN LUCRU';
                        $solicitare->ids_aut_anterioare = null;
                        $solicitare->data_elib = $solicitareAnterioaraUtilizator->data_elib;
                        $solicitare->valabilitate = $solicitareAnterioaraUtilizator->valabilitate;
                        Log::info('copy old data');
                        // Copy id_comisie_ant_scmap from previous authorization if it exists
                        if (isset($solicitareAnterioaraUtilizator->id_comisie_ant_scmap)) {
                            $solicitare->id_comisie_ant_scmap = $solicitareAnterioaraUtilizator->id_comisie_ant_scmap;
                        }
                        $solicitare->id_aut_anterioara = $solicitareAnterioaraUtilizator->oameni;

                        // Copy all data_val fields from previous authorization
                        for ($i = 1; $i <= 6; $i++) {
                            $field = "data_val{$i}";
                            if (isset($solicitareAnterioaraUtilizator->$field)) {
                                $solicitare->$field = $solicitareAnterioaraUtilizator->$field;
                            }
                        }
                        $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);
                        $solicitare->nr_aut = $solicitareAnterioaraUtilizator->nr_aut;
                        $solicitare->serie_aut = $solicitareAnterioaraUtilizator->serie_aut;

                        $solicitare->save();
                    }
                } else {

                    $solicitare = new Solicitari();
                    $solicitare->nr_comisie = $comisieNouaId;
                    $solicitare->tip_comisie = $request->input('tipSolicitare');

                    $solicitare->id_isf = $request->input('id_isf'); // coloana noua cu referinta la ISFURI
                    $solicitare->nume = $request->input('numePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaNume
                    $solicitare->prenume = $request->input('prenumePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaPrenume
                    $solicitare->solicitant_nume = $request->input('solicitantPersFizicaNume');
                    $solicitare->solicitant_prenume = $request->input('solicitantPersFizicaPrenume');
                    // $solicitare->unitate = $request->input('unitateIsf'); // nu e necesar pt pers fizica
                    $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                    $solicitare->cnp = $request->input('solicitantPersFizicaCnp');
                    $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi');
                    $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate');
                    $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa');
                    $solicitare->functie = $request->input('functie');
                    $solicitare->autorizatie = $request->input('autorizare');
                    $solicitare->domeniu = $request->input('activitate');
                    $solicitare->directorASFR = 'Petru BOGDAN';
                    $solicitare->status = 'IN LUCRU';
                    $solicitare->ids_aut_anterioare = null;
                    $solicitare->data_elib = Carbon::now()->format('Y-m-d');
                    $solicitare->valabilitate = Carbon::parse($dataExamT1)->addYears(5)->subDays(1)->format('Y-m-d');
                    $solicitare->data_val1 = Carbon::parse($dataExamT1)->addYears(5)->subDays(1)->format('Y-m-d');
                    $solicitare->id_comisie_ant_scmap = $nrComisieSCMAP;



                    $solicitare->save();
                }

                Log::info('created new solicitare');

                DB::commit();

                Log::info('DB commited successfully');

                return response()->json(['message' => 'Solicitare înregistrată cu succes!'], 200);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error saving data', ['error' => $e->getMessage()]);
                return response()->json(['message' => 'Error saving data', 'error' => $e->getMessage()], 500);
            }
        }

        $cnp = $request->input('solicitantPersFizicaCnp');
        $functie = $request->input('functie');
        $autorizare = $request->input('autorizare');
        $domeniu = $request->input('activitate');

        $dataExamenTeoretic = date('Y-m-d', strtotime($request->input('dataInceputExaminareTeoretica')));

        $checkTypes = ['examinare', 'reexaminare', 'reprogramare', 'reautorizare'];
        $nrComisieSCMAP = null;
        Log::info('functie' . $functie);
        Log::info('autorizare' . $autorizare);
        Log::info('domeniu' . $domeniu);

        // Facem check urile doar pt tipurile de solicitari care se programeaza pentru o anumita functie
        if (in_array(strtolower($request->input('tipSolicitare')), $checkTypes)) {
            $autorizatieExistentaFirst = false;
            $autorizatieExistentaFirst = Solicitari::where('cnp', $cnp)
                ->where('autorizatie', $autorizare)
                ->when(!empty($functie), function ($query) use ($functie) {
                    return $query->where('functie', $functie);
                })
                ->when(!empty($domeniu), function ($query) use ($domeniu) {
                    return $query->where('domeniu', $domeniu);
                })
                // ->where('tip_comisie','!=','Suspendare')
                ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS', 'PV REZULTATE VALIDATE', 'NECORESPUNZATOR']);



            // Fix: Get the record first, then check if it exists
            $autorizatieRecord = $autorizatieExistentaFirst->first();
            Log::info('autorizatie existenta: ' . $autorizatieRecord);


            if ($autorizatieRecord && !$autorizatieExistentaFirst->where('tip_comisie', '=', 'Suspendare')->exists()) {
                Log::info('autorizatie existenta: ' . $autorizatieRecord->oameni);
                $checker = ['reautorizare', 'reprogramare'];
                if ($autorizatieRecord->valabilitate >= now()) {
                    return response()->json([
                        'message' => 'Utilizatorul are deja o autorizație valabilă pentru funcția/autorizarea/activitatea selectată',
                    ], 500);
                } else if (in_array(strtolower($request->input('tipSolicitare')), $checker)) {
                    return response()->json([
                        'message' => 'Utilizatorul are deja o autorizație care nu este valabilă pentru funcția/autorizarea/activitatea selectată.',
                    ], 500);
                }
            }


            if ($autorizare !== 'Autorizare în funcție' && $autorizare !== 'Permis de conducere pe tip de locomotivã-automotor') {
                $autorizatieExistenta = false;
                // Verifica daca functia este de mecanic si utilizatorul are permis european
                if (str_starts_with($functie, 'MECANIC')) {
                    $autorizatieExistenta = Solicitari::where('cnp', $cnp)
                        ->where('autorizatie', 'Permis de mecanic de locomotivã')
                        ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS', 'PV REZULTATE VALIDATE'])
                        ->whereDate('valabilitate', '>=', now()) // Verifică dacă este încă valabilă
                        ->exists();

                    if (!$autorizatieExistenta) {
                        // Verifică dacă utilizatorul are deja o autorizație emisă in functie și valabilă pentru funcția respectivă
                        $autorizatieExistenta = Solicitari::where('cnp', $cnp)
                            ->where('functie', $functie)

                            ->whereIn('autorizatie', ['Autorizare în funcție', 'Permis de conducere pe tip de locomotivã-automotor'])
                            ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS', 'PV REZULTATE VALIDATE'])

                            ->whereDate('valabilitate', '>=', now()) // Verifică dacă este încă valabilă
                            ->exists();
                    }
                } else {
                    // Verifică dacă utilizatorul are deja o autorizație emisă in functie și valabilă pentru funcția respectivă
                    $autorizatieExistenta = Solicitari::where('cnp', $cnp)
                        ->where('functie', $functie)

                        ->where('autorizatie', 'Autorizare în funcție', 'Permis de conducere pe tip de locomotivã-automotor')
                        ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS', 'PV REZULTATE VALIDATE'])

                        ->whereDate('valabilitate', '>=', now()) // Verifică dacă este încă valabilă
                        ->exists();
                }


                // de verificat daca are suspendare pt functie

                if (!$autorizatieExistenta) {
                    return response()->json([
                        'message' => 'Utilizatorul nu are o autorizație emisă și valabilă pentru această funcție.',
                    ], 500);
                }
            }

            $solicitariInLucru = Solicitari::where('cnp', $cnp)
                ->where('status', '!=', 'AVIZE GENERATE')
                ->whereIn('nr_comisie', function ($query) use ($dataExamenTeoretic) {
                    $query->select('nr_comisie')
                        ->from('comisie')
                        ->whereDate('data_exam_t1', $dataExamenTeoretic);
                })
                ->get();

            if ($solicitariInLucru->where('functie', $functie)->count() >= 3) {
                return response()->json([
                    'message' => 'Utilizatorul are deja trei solicitări în lucru pentru această funcție în ziua examinării.',
                ], 500);
            }

            // Dacă există o solicitare pentru o funcție diferită în aceeași zi de examinare, blochează introducerea
            if ($solicitariInLucru->where('functie', '!=', $functie)->count() > 0) {
                return response()->json([
                    'message' => 'Utilizatorul are deja o solicitare în lucru pentru o funcție diferită în ziua examinării.',
                ], 500);
            }

            // check daca au trecut 30 de zile de la data ultimei examinari scmap - data_exam_t1
            if (strtolower($request->input('tipSolicitare')) === 'reexaminare' || strtolower($request->input('tipSolicitare')) === 'reprogramare') {
                $comisiiNrIsf = Comisie::where('nr_ISF', $request->input('numarAprobareInitialaScmap'))->where('data_ISF', $request->input('dataAprobareInitialaScmap'))->orderBy('nr_comisie')->get();
                Log::info('Found comisii count: ' . $comisiiNrIsf->count());
                foreach ($comisiiNrIsf as $comisieNrIsf) {
                    Log::info('comisieNrIsf: ' . json_encode($comisieNrIsf));

                    $solicitareIdIsf = Solicitari::where('nr_comisie', $comisieNrIsf->nr_comisie)->first();
                    Log::info('solicitareIdIsf: ' . json_encode($solicitareIdIsf));
                    if ($solicitareIdIsf === null) {
                        continue;
                    }
                    if ($solicitareIdIsf->id_isf === $request->input('id_isf')) {

                        // if (Carbon::parse($comisieNrIsf->data_exam_t1)->diffInDays(Carbon::now()) < 30) {
                        //     return response()->json([
                        //         'message' => 'Au trecut mai putin de 30 de zile de la data ultimei examinari.',
                        //     ], 500);
                        // } else {
                        $nrComisieSCMAP = $comisieNrIsf->nr_comisie;
                        // }
                        break;
                    }
                }
            }
        }




        // initial IN LUCRU
        // dupa apasare TIPARIRE -> COMUNICARE TIPARITA
        // dupa ce dai validare comunicare -> COMUNICARE VALIDATA
        // dupa ce dai tiparire proces verbal -> PV TIPARIT
        // dupa ce dai validare proces verbal -> PV VALIDAT
        // dupa ce introduci notele -> REZULTATE INTRODUSE
        // dupa ce printezi PV cu note -> PV REZULTATE PRINTAT
        // dupa ce validezi PV cu note -> PV REZULTATE VALIDATE
        // dupa ce introduci nr autorizare -> NR AUTORIZARE INTRODUS
        // ultimul AVIZE GENERATE
        //examinare, preschimbare, reautorizare, suspendare
        $type2Values = ['reexaminare', 'reprogramare', 'reautorizare'];

        $type3Values = [
            'vizeperiodice',
            'duplicate',
            'schimbarenume',
            'suspendare',
            'incetaresuspendare',

            'retragereautorizatie'
        ];




        $fieldsToValidate = [
            'tipSolicitare' => 'required|string|max:255',
            'numarCerereSolicitant' => 'required|string|max:50',
            'dataCerereSolicitant' => 'required',
            'locatieCenafer' => 'required|string|max:255',
            'numarCenafer' => 'nullable|string|max:50',
            'dataCenafer' => 'nullable',
            'numarIesireSolicitare' => 'required|string|max:50',
            'dataIesireSolicitare' => 'required',
            'redactataDeSolicitare' => 'required|string|max:80',
            'dataOraRedactareSolicitare' => 'required',
            'solicitantPersFizicaNume' => 'required|string|max:255',
            'solicitantPersFizicaPrenume' => 'required|string|max:255',
            'solicitantPersFizicaSerieCi' => 'required|string|size:2|regex:/^[a-zA-Z]+$/',
            'solicitantPersFizicaCnp' => 'required|string|size:13|regex:/^[0-9]+$/',
            'solicitantPersFizicaNumarCi' => 'required|string|max:6|regex:/^[0-9]+$/',
            'solicitantPersFizicaLocalitate' => 'required|string|max:255',
            'solicitantPersFizicaAdresa' => 'required|string|max:255',
            'regimUrgenta' => 'required|boolean',
            'numeMembruComisie' => 'required|string|max:255',
            'prenumeMembruComisie' => 'required|string|max:255',
            // 'serieAtestatMembru' => 'required|string|max:50',
            // 'numarAtestatMembru' => 'required|string|max:50',
            'locatieExaminareTeoretica' => 'required|string|max:255',
            'dataInceputExaminareTeoretica' => 'required',
            'dataSfarsitExaminareTeoretica' => 'required',
            'locatieExaminarePractica' => 'required',
            'dataInceputExaminarePractica' => 'required',
            'dataSfarsitExaminarePractica' => 'required',
            'numeReprezentantCenafer' => 'required|string|max:255',
            'prenumeReprezentantCenafer' => 'required|string|max:255',
            // 'serieReprezentantCenafer' => 'required|string|max:50',
            // 'numarReprezentantCenafer' => 'required|string|max:50',
            'id_isf' => 'required|numeric',
            'numePresedinteComisie' => 'required|string|max:255',
            'prenumePresedinteComisie' => 'required|string|max:255',
            'functie' => 'required|string|max:255',
            'autorizare' => 'required|string|max:255',
            'activitate' => 'nullable|string|max:255',
            'emailCfrIsf' => 'required|string|max:255',
            'numarAprobareInitialaScmap' => 'nullable|string|max:50',
            'dataAprobareInitiala' => 'nullable',
            'solicitantPersJuridicaDenumire' => 'nullable|string|max:255',
            'solicitantPersJuridicaCif' => 'nullable|string|max:255',
            'solicitanti' => 'nullable|array',
            'numeReprezentantUnitateFeroviara' => 'nullable|string|max:255',
            'prenumeReprezentantUnitateFeroviara' => 'nullable|string|max:255',
            'serieAtestatReprezentant' => 'nullable|string|max:50',
            'numarAtestatReprezentant' => 'nullable|string|max:50',
            'idsAutorizatiiAnterioareSelected' => 'nullable|array',
            'motivSuspendare' => 'nullable|string|max:255',
            'incetareSuspendareNrAviz' => 'nullable|string|max:255',
            'incetareSuspendareEliberatAviz' => 'nullable|string|max:255',
            'incetareSuspendareDataAviz' => 'nullable|string',
            'incetareSuspendareNrCertificat' => 'nullable|string|max:255',
            'incetareSuspendareEliberatCertificat' => 'nullable|string|max:255',
            'incetareSuspendareDataCertificat' => 'nullable|string',

        ];

        switch (strtolower($request->input('tipSolicitare'))) {
            case $type2Values[0]://reexaminare
            case $type2Values[2]://reaturoizare
                $fieldsToValidate['numeReprezentantCenafer'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeReprezentantCenafer'] = 'nullable|string|max:255';
                // $fieldsToValidate['serieReprezentantCenafer'] = 'nullable|string|max:50';
                // $fieldsToValidate['numarReprezentantCenafer'] = 'nullable|string|max:50';
                $fieldsToValidate['numarAprobareInitialaScmap'] = 'nullable|string|max:50';
                $fieldsToValidate['dataAprobareInitialaScmap'] = 'nullable';
                if (strtolower($request->input('tipSolicitare') === $type2Values[2])) {
                    $fieldsToValidate['locatieCenafer'] = 'nullable';
                    $fieldsToValidate['numarCenafer'] = 'nullable';
                    $fieldsToValidate['dataCenafer'] = 'nullable';
                    $fieldsToValidate['idsAutorizatiiAnterioareSelected'] = 'required|array';
                    $fieldsToValidate['numarAprobareInitialaScmap'] = 'nullable|string|max:50';
                    $fieldsToValidate['dataAprobareInitialaScmap'] = 'nullable';
                }
                break;


            case $type2Values[1]://reprogramare
                $fieldsToValidate['regimUrgenta'] = 'nullable|boolean';
                $fieldsToValidate['numarAprobareInitialaScmap'] = 'required|string|max:50';
                $fieldsToValidate['dataAprobareInitialaScmap'] = 'required';
                break;


            case $type3Values[0]://vize
            case $type3Values[1]://duplicat
            case $type3Values[2]://schimbare nume
            case $type3Values[3]://suspendare
            case $type3Values[4]://incetare suspendare
            case $type3Values[5]://retragere
                $fieldsToValidate['regimUrgenta'] = 'nullable|boolean';
                $fieldsToValidate['functie'] = 'nullable|string';
                $fieldsToValidate['autorizare'] = 'nullable|string';
                $fieldsToValidate['numePresedinteComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumePresedinteComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['numeMembruComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeMembruComisie'] = 'nullable|string|max:255';
                // $fieldsToValidate['serieAtestatMembru'] = 'nullable|string|max:50';
                // $fieldsToValidate['numarAtestatMembru'] = 'nullable|string|max:50';
                $fieldsToValidate['numeReprezentantCenafer'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeReprezentantCenafer'] = 'nullable|string|max:255';
                // $fieldsToValidate['serieReprezentantCenafer'] = 'nullable|string|max:50';
                // $fieldsToValidate['numarReprezentantCenafer'] = 'nullable|string|max:50';
                $fieldsToValidate['numeReprezentantUnitateFeroviara'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeReprezentantUnitateFeroviara'] = 'nullable|string|max:255';
                $fieldsToValidate['serieAtestatReprezentant'] = 'nullable|string|max:50';
                $fieldsToValidate['numarAtestatReprezentant'] = 'nullable|string|max:50';
                $fieldsToValidate['locatieExaminareTeoretica'] = 'nullable|string|max:255';
                $fieldsToValidate['dataInceputExaminareTeoretica'] = 'nullable';
                $fieldsToValidate['dataSfarsitExaminareTeoretica'] = 'nullable';
                $fieldsToValidate['locatieExaminarePractica'] = 'nullable|string|max:255';
                $fieldsToValidate['dataInceputExaminarePractica'] = 'nullable';
                $fieldsToValidate['dataSfarsitExaminarePractica'] = 'nullable';
                $fieldsToValidate['idsAutorizatiiAnterioareSelected'] = 'required|array';
                $fieldsToValidate['numarCenafer'] = 'nullable';
                $fieldsToValidate['dataCenafer'] = 'nullable';
                if (strtolower($request->input('tipSolicitare') === $type3Values[3])) {
                    $fieldsToValidate['motivSuspendare'] = 'required|string|max:255';
                    if ($request->input('motivSuspendare') === 'Art. 8 alin 1. lit. s)') {
                        $fieldsToValidate['nrZileSuspendare'] = 'required|numeric|min:90|max:5000';
                    }
                }
                if (strtolower($request->input('tipSolicitare') === $type3Values[4])) {
                    $fieldsToValidate['incetareSuspendareNrAviz'] = 'required|string|max:255';
                    $fieldsToValidate['incetareSuspendareEliberatAviz'] = 'required|string|max:255';
                    $fieldsToValidate['incetareSuspendareDataAviz'] = 'required|string';
                    $fieldsToValidate['incetareSuspendareNrCertificat'] = 'required|string|max:255';
                    $fieldsToValidate['incetareSuspendareEliberatCertificat'] = 'required|string|max:255';
                    $fieldsToValidate['incetareSuspendareDataCertificat'] = 'required|string';

                }

                //functie, autorizare, activitate
                break;

            default:
            // default nu face nimic



        }
        $validator = Validator::make($request->all(), $fieldsToValidate);
        if ($validator->fails()) {
            // Return validation errors
            return response()->json([
                'message' => 'Eroare de validare',
                'errors' => $validator->errors()->keys(),
            ], 422);
        }

        Log::info('validated all req fields');

        if (in_array(strtolower($request->input('tipSolicitare')), $type3Values) && $request->input('idsAutorizatiiAnterioareSelected')) {
            Log::info('Solicitare cu autorizatii anterioare');
            return storeType3SolicitarePf($request, $request->input('idsAutorizatiiAnterioareSelected'));
        } else {
            Log::info('Solicitare normala');

            return storeDbRecords($request, $request->input('idsAutorizatiiAnterioareSelected'), $nrComisieSCMAP);
        }


    }



    public function storePersJuridica(Request $request)
    {

        function storeType3SolicitarePj($request, $idsAutorizatiiAnterioare, $solicitantiFailed)
        {
            DB::beginTransaction();
            try {
                // noua intrare in Comisie
                $comisieNoua = new Comisie();
                $comisieNoua->cerere_uf_nr = $request->input('numarCerereSolicitant');
                $comisieNoua->cerere_uf_data = $request->input('dataCerereSolicitant');
                $comisieNoua->CENAFER = $request->input('locatieCenafer');
                $comisieNoua->nr_CENAFER = $request->input('numarCenafer');
                $comisieNoua->data_CENAFER = $request->input('dataCenafer');
                $comisieNoua->nr_ISF = $request->input('numarIesireSolicitare');
                $comisieNoua->data_ISF = $request->input('dataIesireSolicitare');
                $comisieNoua->redactat = $request->input('redactataDeSolicitare');
                $comisieNoua->data_ora_redactat = $request->input('dataOraRedactareSolicitare');// coloana noua data_ora_redactat

                $columnsComisie = [
                    'nume_cenafer1',
                    'prenume_cenafer1',
                    'serie_cenafer1',
                    'numar_cenafer1',
                    'nume_uf',
                    'prenume_uf',
                    'serie_uf',
                    'numar_uf',
                    'loc_exam_t',
                    'data_exam_t1',
                    'ora_exam_t1',
                    'loc_exam_p',
                    'data_exam_p1',
                    'ora_exam_p1',
                    'urgent',
                    'nr_aprobare',
                    'data_aprobare',
                    'tip_comisie_id',
                    'nr_ISF',
                    'data_ISF'

                ];


                $querySolicitari = Solicitari::select()->where('oameni', $idsAutorizatiiAnterioare[0])->first();
                Log::info(json_encode($querySolicitari));
                $queryComisie = Comisie::select(...$columnsComisie)->where('nr_comisie', $querySolicitari->nr_comisie)->first();
                Log::info($queryComisie);

                $comisieNoua->nume_cenafer1 = $queryComisie->nume_cenafer1 || '';
                $comisieNoua->prenume_cenafer1 = $queryComisie->prenume_cenafer1;
                $comisieNoua->serie_cenafer1 = $queryComisie->serie_cenafer1;
                $comisieNoua->numar_cenafer1 = $queryComisie->numar_cenafer1;
                $comisieNoua->nume_uf = $queryComisie->nume_uf;
                $comisieNoua->prenume_uf = $queryComisie->prenume_uf;
                $comisieNoua->serie_uf = $queryComisie->serie_uf;
                $comisieNoua->numar_uf = $queryComisie->numar_uf;
                $comisieNoua->loc_exam_t = $queryComisie->loc_exam_t;
                $comisieNoua->data_exam_t1 = $queryComisie->data_exam_t1;
                $comisieNoua->ora_exam_t1 = $queryComisie->ora_exam_t1;
                $comisieNoua->loc_exam_p = $queryComisie->loc_exam_p;
                $comisieNoua->data_exam_p1 = $queryComisie->data_exam_p1;
                $comisieNoua->ora_exam_p1 = $queryComisie->ora_exam_p1;
                $comisieNoua->urgent = $queryComisie->urgent;
                $comisieNoua->nr_aprobare = $queryComisie->nr_aprobare;
                $comisieNoua->data_aprobare = $queryComisie->data_aprobare;
                $comisieNoua->tip_comisie_id = $queryComisie->tip_comisie_id;

                $tarif = Tarife::get()->where('tip_solicitare', $request->input('tipSolicitare'))->first();
                Log::info('tarif' . $tarif);
                if ($tarif) {
                    $tarifTotal = $tarif['tarif_orar'] * $tarif['nr_ore'];

                    $comisieNoua->tarif_total = $tarifTotal;
                }

                $comisieNoua->save();
                Log::info('created new comisie');

                $comisieNouaId = $comisieNoua->nr_comisie;
                $latestIdSolicitarePj = Solicitari::max('id_solicitare_pj');
                $newIdSolicitarePj = $latestIdSolicitarePj ? $latestIdSolicitarePj + 1 : 1;

                Log::info('request solicitanti' . json_encode($request->input('solicitanti')));
                foreach ($request->input('solicitanti') as $solicitant) {
                    $solicitariAnterioareUtilizator = Solicitari::select()->whereIn('oameni', $idsAutorizatiiAnterioare)->where('cnp', $solicitant['cnp'])->get();
                    while ($solicitariAnterioareUtilizator->isNotEmpty()) {
                        $solicitareAnterioaraUtilizator = $solicitariAnterioareUtilizator->shift(); // Remove and process the first item


                        Log::info('iterating over ' . json_encode($solicitariAnterioareUtilizator));
                        Log::info('current item ' . json_encode($solicitareAnterioaraUtilizator));


                        $solicitare = new Solicitari();
                        // Daca este autorizare in functie, adaugam si suspendari pt tot ce e sub acea functie
                        if (strtolower($request->input('tipSolicitare')) === 'suspendare' && $solicitareAnterioaraUtilizator->autorizatie === 'Autorizare în funcție' && empty($solicitareAnterioaraUtilizator->domeniu)) {
                            Log::info('suspendare autorizare in functie');
                            $solicitariCeTrebuieSuspendate = Solicitari::selectRaw('domeniu, autorizatie, GROUP_CONCAT(oameni) as oameni_grupati')
                                ->where('cnp', $solicitareAnterioaraUtilizator->cnp)
                                ->where('tip_comisie', '!=', 'Suspendare')
                                ->where('functie', $solicitareAnterioaraUtilizator->functie)
                                ->whereNotNull('domeniu')
                                ->where('domeniu', '!=', '')
                                ->groupBy('domeniu', 'autorizatie')
                                ->get();
                            Log::info('solicitari ce trebuie suspendate ' . json_encode($solicitariCeTrebuieSuspendate));

                            foreach ($solicitariCeTrebuieSuspendate as $solicitareCeTrebuieSusepndata) {
                                $solicitareGasita = Solicitari::where('oameni', explode(',', $solicitareCeTrebuieSusepndata->oameni_grupati)[0])->first();
                                $solicitariAnterioareUtilizator = $solicitariAnterioareUtilizator->merge([$solicitareGasita]);

                            }
                            Log::info('solicitari ant user ' . json_encode($solicitariAnterioareUtilizator));


                        }

                        // querySolicitari trebuie schimbat si filtrat dupa cnp solicitant curent
                        $solicitare->fill($solicitareAnterioaraUtilizator->toArray()); // Copiem toate câmpurile din solicitarea anterioară
                        $solicitare->id_aut_anterioara = $solicitareAnterioaraUtilizator->oameni;
                        Log::info('solicitare filled ' . $solicitare->functie);
                        Log::info('solicitare filled ' . $solicitare->domeniu);
                        Log::info('solicitare filled ' . $solicitare->autorizatie);

                        if (strtolower($request->input('tipSolicitare')) === 'vizeperiodice') {

                            $solicitare->data_elib = $solicitareAnterioaraUtilizator->data_elib;

                            // if ($solicitareAnterioaraUtilizator->valabilitate->greaterThan(Carbon::now()->addDays(45))) {
                            //      $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul nu are o autorizație emisă și valabilă pentru această funcție';
                            // }
                            // Start with the initial date (either data_val1 or valabilitate)
                            $baseDate = !empty($solicitareAnterioaraUtilizator->data_val1)
                                ? Carbon::parse($solicitareAnterioaraUtilizator->data_val1)
                                : Carbon::parse($solicitareAnterioaraUtilizator->valabilitate);

                            // Copy the original date for calculations
                            $currentDate = $baseDate->copy();

                            // Set data_val1 if it's empty
                            if (empty($solicitareAnterioaraUtilizator->data_val1)) {
                                $solicitare->data_val1 = $currentDate;
                            }

                            // Populate all data_val fields with consecutive 5-year intervals
                            for ($i = 2; $i <= 6; $i++) {
                                $column = "data_val{$i}";

                                // Add 5 years to the current date
                                $currentDate = $currentDate->copy()->addYears(5)->subDays(1);

                                // Set the value for this column
                                $solicitare->$column = $currentDate;

                                // If this date is greater than today, update valabilitate and stop
                                if ($currentDate->greaterThan(Carbon::now()->addMonths(12))) {
                                    $solicitare->valabilitate = $currentDate;
                                    break;
                                }
                            }

                            // If we've gone through all columns and still haven't found a future date,
                            // add another 5 years to the last date and set it as valabilitate
                            if ($currentDate->lessThanOrEqualTo(Carbon::now())) {
                                $finalDate = $currentDate->copy()->addYears(5)->subDays(1);
                                $solicitare->valabilitate = $finalDate;
                            }


                        } else {
                            $solicitare->data_elib = Carbon::parse($solicitareAnterioaraUtilizator->data_elib)->format('Y-m-d');
                            $solicitare->valabilitate = Carbon::parse($solicitareAnterioaraUtilizator->valabilitate)->format('Y-m-d');
                        }

                        $solicitare->nr_comisie = $comisieNouaId;
                        $solicitare->tip_comisie = $request->input('tipSolicitare');

                        $solicitare->id_isf = $request->input('id_isf'); // coloana noua cu referinta la ISFURI
                        $solicitare->nume = $request->input('numePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaNume
                        $solicitare->prenume = $request->input('prenumePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaPrenume
                        $solicitare->unitate = $request->input('solicitantPersJuridicaDenumire');
                        $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                        $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi'); // mai e necesar?
                        $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate'); // adresa de la pers juridica?
                        $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa'); // adresa de la pers juridica?
                        $solicitare->solicitant_nume = $solicitant['nume'];
                        $solicitare->solicitant_prenume = $solicitant['prenume'];
                        $solicitare->cnp = $solicitant['cnp'];
                        $solicitare->id_solicitare_pj = $newIdSolicitarePj;
                        $solicitare->directorASFR = 'Petru BOGDAN';
                        $solicitare->status = 'IN LUCRU';
                        $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);
                        $solicitare->motiv_suspendare = $solicitant['motivSuspendare'];
                        $solicitare->incetare_suspendare_nr_aviz = $solicitant['incetareSuspendareNrAviz'];
                        $solicitare->incetare_suspendare_eliberat_aviz = $solicitant['incetareSuspendareEliberatAviz'];
                        $solicitare->incetare_suspendare_data_aviz = $solicitant['incetareSuspendareDataAviz'];
                        $solicitare->incetare_suspendare_nr_certificat = $solicitant['incetareSuspendareNrCertificat'];
                        $solicitare->incetare_suspendare_eliberat_certificat = $solicitant['incetareSuspendareEliberatCertificat'];
                        $solicitare->incetare_suspendare_data_certificat = $solicitant['incetareSuspendareDataCertificat'];

                        $solicitare->perioada_suspendare = $solicitant['nrZileSuspendare'];


                        if ($request->input('tipSolicitare') === 'VizePeriodice') {

                        }




                        $solicitare->save();

                        Log::info('created new solicitare', ['solicitant' => $solicitant]);
                    }
                }
                Log::info('commiting transaction');
                DB::commit();

                Log::info('DB commited successfully');

                Log::info('solicitantiFailed' . implode(', ', $solicitantiFailed));


                if (count($solicitantiFailed) > 0) {
                    $message = 'Solicitare înregistrată cu succes! Utilizatorii cu CNP-urile următoare au următoarele erori și nu au fost înregistrați: ' . implode(', ', $solicitantiFailed);
                    // Log::info('message ' . $message);
                    Log::info('unii solicitanti nu au fost inreg');

                    //return response()->json(['message' => 'Unii solicitanti nu au fost inregistrati'], 500);
                    return $message;

                } else {
                    Log::info('toti solicitantii au fost inreg');
                    //return response()->json(['message' => 'Solicitare înregistrată cu succes!'], 200);
                    return 'Solicitare înregistrată cu succes!';

                }

                //, 'solicitantiFailed' => implode(', ', $solicitantiFailed)
// , 'solicitantiFailed' => implode(', ', $solicitantiFailed)
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error saving data', ['error' => $e->getMessage()]);
                return response()->json(['message' => 'Error saving data', 'error' => $e->getMessage()], 500);
            }
        }

        function storeDbRecordsPJ($request, $solicitantiFailed, $idsAutorizatiiAnterioare)
        {
            DB::beginTransaction();
            try {
                // noua intrare in Comisie
                $comisieNoua = new Comisie();
                $comisieNoua->cerere_uf_nr = $request->input('numarCerereSolicitant');
                $comisieNoua->cerere_uf_data = $request->input('dataCerereSolicitant');
                $comisieNoua->CENAFER = $request->input('locatieCenafer');
                $comisieNoua->nr_CENAFER = $request->input('numarCenafer');
                $comisieNoua->data_CENAFER = $request->input('dataCenafer');
                $comisieNoua->nume_cenafer1 = $request->input('numeMembruComisie');
                $comisieNoua->prenume_cenafer1 = $request->input('prenumeMembruComisie');
                // $comisieNoua->serie_cenafer1 = $request->input('serieAtestatMembru');
                // $comisieNoua->numar_cenafer1 = $request->input('numarAtestatMembru');
                $comisieNoua->nume_uf = $request->input('numeReprezentantUnitateFeroviara');
                $comisieNoua->prenume_uf = $request->input('prenumeReprezentantUnitateFeroviara');
                $comisieNoua->serie_uf = $request->input('serieAtestatReprezentant');
                $comisieNoua->numar_uf = $request->input('numarAtestatReprezentant');
                $comisieNoua->redactat = $request->input('redactataDeSolicitare');
                // $comisieNoua->pv_nr_iesire = $request->input('numarIesireSolicitare');
                $comisieNoua->nr_ISF = $request->input('numarIesireSolicitare');
                // $comisieNoua->pv_data_iesire = $request->input('dataIesireSolicitare');
                $comisieNoua->data_ISF = $request->input('dataIesireSolicitare');
                $comisieNoua->data_ora_redactat = $request->input('dataOraRedactareSolicitare');// coloana noua data_ora_redactat
                $comisieNoua->loc_exam_t = $request->input('locatieExaminareTeoretica');
                $dataExamT1 = date('Y-m-d', strtotime($request->input('dataInceputExaminareTeoretica')));
                $oraExamT1 = date('H:i:s', strtotime($request->input('dataInceputExaminareTeoretica')));
                $comisieNoua->data_exam_t1 = $dataExamT1;
                $comisieNoua->ora_exam_t1 = $oraExamT1;
                $comisieNoua->loc_exam_p = $request->input('locatieExaminarePractica');
                $dataExamP1 = date('Y-m-d', strtotime($request->input('dataInceputExaminarePractica')));
                $oraExamP1 = date('H:i:s', strtotime($request->input('dataInceputExaminarePractica')));
                $comisieNoua->data_exam_p1 = $dataExamP1;
                $comisieNoua->ora_exam_p1 = $oraExamP1;
                $comisieNoua->urgent = $request->input('regimUrgenta') == false ? 'Nu' : 'Da';
                $comisieNoua->nr_aprobare = $request->input('numarAprobareInitialaScmap');
                $comisieNoua->data_aprobare = $request->input('dataAprobareInitialaScmap');
                $comisieNoua->tip_comisie_id = $request->input('tipSolicitareId');



                $comisieNoua->save();
                Log::info('created new comisie');

                $comisieNouaId = $comisieNoua->nr_comisie;
                $latestIdSolicitarePj = Solicitari::max('id_solicitare_pj');
                $newIdSolicitarePj = $latestIdSolicitarePj ? $latestIdSolicitarePj + 1 : 1;


                if (strtolower($request->input('tipSolicitare')) === 'reautorizare') {
                    foreach ($request->input('solicitanti') as $solicitant) {
                        $solicitariAnterioareUtilizator = Solicitari::select()->whereIn('oameni', $idsAutorizatiiAnterioare)->where('cnp', $solicitant['cnp'])->get();
                        foreach ($solicitariAnterioareUtilizator as $solicitareAnterioaraUtilizator) {
                            $solicitare = new Solicitari();
                            $solicitare->nr_comisie = $comisieNouaId;
                            $solicitare->tip_comisie = $request->input('tipSolicitare');

                            $solicitare->id_isf = $request->input('id_isf'); // coloana noua cu referinta la ISFURI
                            $solicitare->nume = $request->input('numePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaNume
                            $solicitare->prenume = $request->input('prenumePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaPrenume
                            $solicitare->unitate = $request->input('solicitantPersJuridicaDenumire');
                            $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                            $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi'); // mai e necesar?
                            $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate'); // adresa de la pers juridica?
                            $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa'); // adresa de la pers juridica?
                            $solicitare->functie = $request->input('functie');
                            $solicitare->autorizatie = $request->input('autorizare');
                            $solicitare->domeniu = $request->input('activitate');
                            $solicitare->solicitant_nume = $solicitant['nume'];
                            $solicitare->solicitant_prenume = $solicitant['prenume'];
                            $solicitare->cnp = $solicitant['cnp'];
                            $solicitare->id_solicitare_pj = $newIdSolicitarePj;
                            $solicitare->directorASFR = 'Petru BOGDAN';
                            $solicitare->status = 'IN LUCRU';
                            $solicitare->data_elib = $solicitareAnterioaraUtilizator->data_elib;
                            $solicitare->valabilitate = $solicitareAnterioaraUtilizator->valabilitate;
                            Log::info('copy old data');
                            // Copy id_comisie_ant_scmap from previous authorization if it exists
                            if (isset($solicitareAnterioaraUtilizator->id_comisie_ant_scmap)) {
                                $solicitare->id_comisie_ant_scmap = $solicitareAnterioaraUtilizator->id_comisie_ant_scmap;
                            }
                            $solicitare->id_aut_anterioara = $solicitareAnterioaraUtilizator->oameni;

                            // Copy all data_val fields from previous authorization
                            for ($i = 1; $i <= 6; $i++) {
                                $field = "data_val{$i}";
                                if (isset($solicitareAnterioaraUtilizator->$field)) {
                                    $solicitare->$field = $solicitareAnterioaraUtilizator->$field;
                                }
                            }
                            $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);
                            $solicitare->nr_aut = $solicitareAnterioaraUtilizator->nr_aut;
                            $solicitare->serie_aut = $solicitareAnterioaraUtilizator->serie_aut;


                            $solicitare->save();

                            Log::info('created new solicitare', ['solicitant' => $solicitant]);
                        }
                    }
                } else {

                    foreach ($request->input('solicitanti') as $solicitant) {
                        $solicitare = new Solicitari();
                        $solicitare->nr_comisie = $comisieNouaId;
                        $solicitare->tip_comisie = $request->input('tipSolicitare');

                        $solicitare->id_isf = $request->input('id_isf'); // coloana noua cu referinta la ISFURI
                        $solicitare->nume = $request->input('numePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaNume
                        $solicitare->prenume = $request->input('prenumePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaPrenume
                        $solicitare->unitate = $request->input('solicitantPersJuridicaDenumire');
                        $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                        $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi'); // mai e necesar?
                        $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate'); // adresa de la pers juridica?
                        $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa'); // adresa de la pers juridica?
                        $solicitare->functie = $request->input('functie');
                        $solicitare->autorizatie = $request->input('autorizare');
                        $solicitare->domeniu = $request->input('activitate');
                        $solicitare->solicitant_nume = $solicitant['nume'];
                        $solicitare->solicitant_prenume = $solicitant['prenume'];
                        $solicitare->cnp = $solicitant['cnp'];
                        $solicitare->id_solicitare_pj = $newIdSolicitarePj;
                        $solicitare->directorASFR = 'Petru BOGDAN';
                        $solicitare->status = 'IN LUCRU';
                        $solicitare->data_elib = Carbon::now()->format('Y-m-d');
                        $solicitare->valabilitate = Carbon::parse($dataExamT1)->addYears(5)->subDays(1)->format('Y-m-d');
                        $solicitare->data_val1 = Carbon::parse($dataExamT1)->addYears(5)->subDays(1)->format('Y-m-d');
                        if (isset($solicitant['id_comisie_ant_scmap'])) {
                            $solicitare->id_comisie_ant_scmap = $solicitant['id_comisie_ant_scmap'];
                        }



                        $solicitare->save();

                        Log::info('created new solicitare', ['solicitant' => $solicitant]);
                    }
                }
                DB::commit();

                Log::info('DB commited successfully');

                Log::info('solicitantiFailed' . implode(', ', $solicitantiFailed));

                Log::info(print_r($solicitantiFailed, true));
                if (count($solicitantiFailed) > 0) {
                    $message = 'Solicitare înregistrată cu succes! Utilizatorii cu CNP-urile următoare au următoarele erori și nu au fost înregistrați: ' . implode(', ', $solicitantiFailed);
                    // Log::info('message ' . $message);
                    Log::info('unii solicitanti nu au fost inreg');

                    // return response()->json(['message' => 'Unii solicitanti nu au fost inregistrati'], 500);
                    return $message;

                } else {
                    Log::info('toti solicitantii au fost inreg');
                    // return response()->json(['message' => 'Solicitare înregistrată cu succes!'], 200);
                    return 'Solicitare înregistrată cu succes!';

                }



            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error saving data', ['error' => $e->getMessage()]);
                return response()->json(['message' => 'Error saving data', 'error' => $e->getMessage()], 500);
            }
        }
        $solicitantiFailed = [];
        $nrComisieSCMAP = null;
        $solicitantiSucces = array_filter($request->input('solicitanti'), function ($solicitantCurent) use ($request, &$solicitantiFailed, &$nrComisieSCMAP) {


            $cnp = $solicitantCurent['cnp'];
            $functie = $request->input('functie');
            $autorizare = $request->input('autorizare');
            $domeniu = $request->input('activitate');

            $dataExamenTeoretic = date('Y-m-d', strtotime($request->input('dataInceputExaminareTeoretica')));

            $checkTypes = ['examinare', 'reexaminare', 'reprogramare', 'reautorizare'];

            // Facem check urile doar pt tipurile de solicitari care se programeaza pentru o anumita functie
            if (in_array(strtolower($request->input('tipSolicitare')), $checkTypes)) {


                $autorizatieExistentaFirst = false;
                $autorizatieExistentaFirst = Solicitari::where('cnp', $cnp)
                    ->where('autorizatie', $autorizare)
                    ->when(!empty($functie), function ($query) use ($functie) {
                        return $query->where('functie', $functie);
                    })
                    ->when(!empty($domeniu), function ($query) use ($domeniu) {
                        return $query->where('domeniu', $domeniu);
                    })
                    // ->where('tip_comisie','!=','Suspendare')
                    ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS', 'PV REZULTATE VALIDATE', 'NECORESPUNZATOR']);

                // Fix: Get the record first, then check if it exists
                $autorizatieRecord = $autorizatieExistentaFirst->first();
                Log::info('autorizatie existenta: ' . $autorizatieRecord);


                if ($autorizatieRecord) {
                    $checker = ['reautorizare', 'reprogramare'];
                    Log::info('autorizatie existenta: ' . $autorizatieRecord->oameni);

                    if ($autorizatieRecord->valabilitate >= now()) {
                        $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul are deja o autorizație valabilă pentru funcția/autorizarea/activitatea selectată';
                        return false;
                    } else if (in_array(strtolower($request->input('tipSolicitare')), $checker)) {
                        $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul are deja o autorizație care nu este valabilă pentru funcția/autorizarea/activitatea selectată.';
                        return false;
                    }
                }

                if ($autorizare !== 'Autorizare în funcție' && $autorizare !== 'Permis de conducere pe tip de locomotivã-automotor') {
                    $autorizatieExistenta = false;
                    Log::info('check if starts with mecanic');

                    if (str_starts_with($functie, 'MECANIC')) {
                        Log::info('starts with mecanic');
                        $autorizatieExistenta = Solicitari::where('cnp', $cnp)
                            ->where('autorizatie', 'Permis de mecanic de locomotivã')
                            ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS', 'PV REZULTATE VALIDATE'])
                            ->whereDate('valabilitate', '>=', now()) // Verifică dacă este încă valabilă
                            ->exists();

                        if (!$autorizatieExistenta) {
                            // Verifică dacă utilizatorul are deja o autorizație emisă in functie și valabilă pentru funcția respectivă
                            Log::info('autorizatie inexistenta, cautam permis conducere sau aut');

                            $autorizatieExistenta = Solicitari::where('cnp', $cnp)
                                ->where('functie', $functie)

                                ->whereIn('autorizatie', ['Autorizare în funcție', 'Permis de conducere pe tip de locomotivã-automotor'])
                                ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS', 'PV REZULTATE VALIDATE'])

                                ->whereDate('valabilitate', '>=', now()) // Verifică dacă este încă valabilă
                                ->exists();

                            Log::info('autorizatie existenta' . $autorizatieExistenta);

                        }
                    } else {
                        Log::info('Nu e pt mecanic, intra pe else');
                        // Verifică dacă utilizatorul are deja o autorizație emisă in functie și valabilă pentru funcția respectivă
                        $autorizatieExistenta = Solicitari::where('cnp', $cnp)
                            ->where('functie', $functie)

                            ->whereIn('autorizatie', ['Autorizare în funcție', 'Permis de conducere pe tip de locomotivã-automotor'])
                            ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS', 'PV REZULTATE VALIDATE'])

                            ->whereDate('valabilitate', '>=', now()) // Verifică dacă este încă valabilă
                            ->exists();
                        Log::info('autorizatieExistenta exists' . $autorizatieExistenta);

                    }
                    // de verificat daca are suspendare pt functie

                    if (!$autorizatieExistenta) {
                        // return response()->json([
                        //     'message' => 'Utilizatorul nu are o autorizație emisă și valabilă pentru această funcție.',
                        // ], 500);
                        $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul nu are o autorizație emisă și valabilă pentru această funcție';
                        return false;
                    }
                }

                $solicitariInLucru = Solicitari::where('cnp', $cnp)
                    ->where('status', '!=', 'AVIZE GENERATE')
                    ->whereIn('nr_comisie', function ($query) use ($dataExamenTeoretic) {
                        $query->select('nr_comisie')
                            ->from('comisie')
                            ->whereDate('data_exam_t1', $dataExamenTeoretic);
                    })
                    ->get();

                Log::info('Trece de solicitari in lucru');
                $countSolicitariInregistrareCurenta = Solicitari::where('cnp', $cnp)
                    ->whereIn('oameni', $request->input('idsAutorizatiiAnterioareSelected'))
                    ->count();

                Log::info('Trece de count');

                if ($solicitariInLucru->where('functie', $functie)->count() >= 3) {
                    // de luat in considerare si toate solicitarile care vin pentru solicitantul actual, numaram ids solicitari anterioare pt solicitant curent
                    // + $countSolicitariInregistrareCurenta va fi pentru orice functie, va trebui filtrat dupa functie

                    $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul are deja trei solicitări în lucru pentru această funcție în ziua examinării';
                    return false;

                }
                Log::info('Trece de count check');

                // Dacă există o solicitare pentru o funcție diferită în aceeași zi de examinare, blochează introducerea
                // if ($solicitariInLucru->where('functie', '!=', $functie)->count() > 0) {

                //     $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul are deja o solicitare în lucru pentru o funcție diferită în ziua examinării';
                //     return false;

                // }
                Log::info('Trece de count check 2');

                // check daca au trecut 30 de zile de la data ultimei examinari scmap - data_exam_t1
                if (strtolower($request->input('tipSolicitare')) === 'reexaminare' || strtolower($request->input('tipSolicitare')) === 'reprogramare') {
                    $comisiiNrIsf = Comisie::where('nr_ISF', operator: $request->input('numarAprobareInitialaScmap'))->where('data_ISF', $request->input('dataAprobareInitialaScmap'))->orderBy('nr_comisie')->get();
                    Log::info('Found comisii count: ' . $comisiiNrIsf->count());
                    foreach ($comisiiNrIsf as $comisieNrIsf) {
                        Log::info('comisieNrIsf: ' . json_encode($comisieNrIsf));

                        $solicitareIdIsf = Solicitari::where('nr_comisie', $comisieNrIsf->nr_comisie)->first();
                        Log::info('solicitareIdIsf: ' . json_encode($solicitareIdIsf));
                        if ($solicitareIdIsf === null) {
                            continue;
                        }
                        if ($solicitareIdIsf->id_isf === $request->input('id_isf')) {

                            // if (Carbon::parse($comisieNrIsf->data_exam_t1)->diffInDays(Carbon::now()) < 30) {
                            // $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Au trecut mai putin de 30 de zile de la data ultimei examinari';
                            // return false;
                            // } else {
                            $nrComisieSCMAP = $comisieNrIsf->nr_comisie;
                            // }
                            break;
                        }
                    }
                }
                Log::info('Trece de check reexaminare');

            }


            Log::info('returns true');

            return true;
        });


        if (!$solicitantiSucces) {
            Log::info('no solicitanti success');

            return response()->json(['message' => 'Niciun solicitant nu a fost înregistrat: ' . implode(',', $solicitantiFailed)], 500);
        }

        $solicitantiSucces = array_map(function ($solicitant) use ($nrComisieSCMAP, $request) {
            // Only add the property if certain conditions are met
            if (
                strtolower($request->input('tipSolicitare')) === 'reexaminare' ||
                strtolower($request->input('tipSolicitare')) === 'reprogramare'
            ) {
                $solicitant['id_comisie_ant_scmap'] = $nrComisieSCMAP ?? null;
            }
            return $solicitant;
        }, $solicitantiSucces);
        Log::info('trece de assign id comisie ant scmap');


        $request->merge(['solicitanti' => $solicitantiSucces]);

        Log::info('solicitanti', $request->input('solicitanti'));


        $type2Values = ['reexaminare', 'reprogramare', 'reautorizare'];

        $type3Values = [
            'vizeperiodice',
            'duplicate',
            'schimbarenume',
            'suspendare',
            'incetaresuspendare',
            'retragereautorizatie'
        ];

        $fieldsToValidate = [
            'tipSolicitare' => 'required|string|max:255',
            'numarCerereSolicitant' => 'required|string|max:50',
            'dataCerereSolicitant' => 'required',
            'locatieCenafer' => 'required|string|max:255',
            'numarCenafer' => 'nullable|string|max:50',
            'dataCenafer' => 'nullable',
            'numarIesireSolicitare' => 'required|string|max:50',
            'dataIesireSolicitare' => 'required',
            'redactataDeSolicitare' => 'required|string|max:80',
            'dataOraRedactareSolicitare' => 'required',
            'solicitantPersFizicaNume' => 'nullable|string|max:255',
            'solicitantPersFizicaPrenume' => 'nullable|string|max:255',
            'solicitantPersFizicaSerieCi' => 'nullable|string|size:2|regex:/^[a-zA-Z]+$/',
            'solicitantPersFizicaCnp' => 'nullable|string|size:13|regex:/^[0-9]+$/',
            'solicitantPersFizicaNumarCi' => 'nullable|string|max:6|regex:/^[0-9]+$/',
            'solicitantPersFizicaLocalitate' => 'nullable|string|max:255',
            'solicitantPersFizicaAdresa' => 'nullable|string|max:255',
            'regimUrgenta' => 'required|boolean',
            'numeMembruComisie' => 'required|string|max:255',
            'prenumeMembruComisie' => 'required|string|max:255',
            // 'serieAtestatMembru' => 'required|string|max:50',
            // 'numarAtestatMembru' => 'required|string|max:50',
            'locatieExaminareTeoretica' => 'required|string|max:255',
            'dataInceputExaminareTeoretica' => 'required',
            'dataSfarsitExaminareTeoretica' => 'required',
            'locatieExaminarePractica' => 'required',
            'dataInceputExaminarePractica' => 'required',
            'dataSfarsitExaminarePractica' => 'required',
            'numeReprezentantCenafer' => 'nullable|string|max:255',
            'prenumeReprezentantCenafer' => 'nullable|string|max:255',
            // 'serieReprezentantCenafer' => 'nullable|string|max:50',
            // 'numarReprezentantCenafer' => 'nullable|string|max:50',
            'id_isf' => 'required|numeric',
            'numePresedinteComisie' => 'required|string|max:255',
            'prenumePresedinteComisie' => 'required|string|max:255',
            'functie' => 'required|string|max:255',
            'autorizare' => 'required|string|max:255',
            'activitate' => 'nullable|string|max:255',
            'emailCfrIsf' => 'required|string|max:255',
            'numarAprobareInitialaScmap' => 'nullable|string|max:50',
            'dataAprobareInitiala' => 'nullable',
            'solicitantPersJuridicaDenumire' => 'required|string|max:255',
            'solicitantPersJuridicaCif' => 'required|string|max:255',
            'solicitanti' => 'required|array',
            'numeReprezentantUnitateFeroviara' => 'nullable|string|max:255',
            'prenumeReprezentantUnitateFeroviara' => 'nullable|string|max:255',
            'serieAtestatReprezentant' => 'nullable|string|max:50',
            'numarAtestatReprezentant' => 'nullable|string|max:50',
            'idsAutorizatiiAnterioareSelected' => 'nullable|array',
            // 'motivSuspendare' => 'nullable|string|max:255',
            // 'incetareSuspendareNrAviz' => 'nullable|string|max:255',
            // 'incetareSuspendareEliberatAviz' => 'nullable|string|max:255',
            // 'incetareSuspendareDataAviz' => 'nullable|string',
            // 'incetareSuspendareNrCertificat' => 'nullable|string|max:255',
            // 'incetareSuspendareEliberatCertificat' => 'nullable|string|max:255',
            // 'incetareSuspendareDataCertificat' => 'nullable|string',


        ];

        switch (strtolower($request->input('tipSolicitare'))) {
            case $type2Values[0]://reexaminare
            case $type2Values[2]:
                $fieldsToValidate['numeReprezentantUnitateFeroviara'] = 'required|string|max:255';
                $fieldsToValidate['prenumeReprezentantUnitateFeroviara'] = 'required|string|max:255';
                $fieldsToValidate['serieAtestatReprezentant'] = 'required|string|max:50';
                $fieldsToValidate['numarAtestatReprezentant'] = 'required|string|max:50';
                $fieldsToValidate['numarAprobareInitialaScmap'] = 'nullable|string|max:50';
                $fieldsToValidate['dataAprobareInitialaScmap'] = 'nullable';
                if (strtolower($request->input('tipSolicitare') === $type2Values[2])) {
                    $fieldsToValidate['locatieCenafer'] = 'nullable';
                    $fieldsToValidate['numarCenafer'] = 'nullable';
                    $fieldsToValidate['dataCenafer'] = 'nullable';
                    $fieldsToValidate['idsAutorizatiiAnterioareSelected'] = 'required|array';
                    $fieldsToValidate['numarAprobareInitialaScmap'] = 'nullable|string|max:50';
                    $fieldsToValidate['dataAprobareInitialaScmap'] = 'nullable';
                }
                break;


            case $type2Values[1]://reprogramare
                $fieldsToValidate['numeReprezentantUnitateFeroviara'] = 'required|string|max:255';
                $fieldsToValidate['prenumeReprezentantUnitateFeroviara'] = 'required|string|max:255';
                $fieldsToValidate['serieAtestatReprezentant'] = 'required|string|max:50';
                $fieldsToValidate['numarAtestatReprezentant'] = 'required|string|max:50';
                $fieldsToValidate['regimUrgenta'] = 'nullable|boolean';
                $fieldsToValidate['numarAprobareInitialaScmap'] = 'required|string|max:50';
                $fieldsToValidate['dataAprobareInitialaScmap'] = 'required';
                break;

            case $type3Values[0]://vize
            case $type3Values[1]://duplicat
            case $type3Values[2]://schimbare nume
            case $type3Values[3]://suspendare
            case $type3Values[4]://incetare suspendare
            case $type3Values[5]://retragere
                $fieldsToValidate['regimUrgenta'] = 'nullable|boolean';
                $fieldsToValidate['functie'] = 'nullable|string';
                $fieldsToValidate['autorizare'] = 'nullable|string';
                $fieldsToValidate['numePresedinteComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumePresedinteComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['numeMembruComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeMembruComisie'] = 'nullable|string|max:255';
                // $fieldsToValidate['serieAtestatMembru'] = 'nullable|string|max:50';
                // $fieldsToValidate['numarAtestatMembru'] = 'nullable|string|max:50';
                $fieldsToValidate['numeReprezentantUnitateFeroviara'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeReprezentantUnitateFeroviara'] = 'nullable|string|max:255';
                $fieldsToValidate['serieAtestatReprezentant'] = 'nullable|string|max:50';
                $fieldsToValidate['numarAtestatReprezentant'] = 'nullable|string|max:50';
                $fieldsToValidate['locatieExaminareTeoretica'] = 'nullable|string|max:255';
                $fieldsToValidate['dataInceputExaminareTeoretica'] = 'nullable';
                $fieldsToValidate['dataSfarsitExaminareTeoretica'] = 'nullable';
                $fieldsToValidate['locatieExaminarePractica'] = 'nullable|string|max:255';
                $fieldsToValidate['dataInceputExaminarePractica'] = 'nullable';
                $fieldsToValidate['dataSfarsitExaminarePractica'] = 'nullable';
                $fieldsToValidate['numarCenafer'] = 'nullable|array';
                $fieldsToValidate['dataCenafer'] = 'nullable|array';
                if (strtolower($request->input('tipSolicitare') === $type3Values[3])) {
                    $fieldsToValidate['solicitanti.*.motivSuspendare'] = 'required|string|max:255';
                    // Check each solicitant's motivSuspendare and add validation rule conditionally
                    if ($request->has('solicitanti') && is_array($request->input('solicitanti'))) {
                        Log::info('validating motive solicitanti');
                        foreach ($request->input('solicitanti') as $index => $solicitant) {
                            if (isset($solicitant['motivSuspendare']) && $solicitant['motivSuspendare'] === 'Art. 8 alin 1. lit. s)') {
                                Log::info('solicitare cu motiv s');
                                $fieldsToValidate["solicitanti.{$index}.nrZileSuspendare"] = 'required|numeric|min:90|max:5000';
                            }
                        }
                    }
                }
                if (strtolower($request->input('tipSolicitare') === $type3Values[4])) {
                    $fieldsToValidate['solicitanti.*.incetareSuspendareNrAviz'] = 'required|string|max:255';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareEliberatAviz'] = 'required|string|max:255';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareDataAviz'] = 'required|string';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareNrCertificat'] = 'required|string|max:255';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareEliberatCertificat'] = 'required|string|max:255';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareDataCertificat'] = 'required|string';

                }
                break;

            default:
            // default nu face nimic



        }
        $validator = Validator::make($request->all(), $fieldsToValidate);
        if ($validator->fails()) {
            // Return validation errors
            return response()->json([
                'message' => 'Eroare de validare',
                'errors' => $validator->errors()->keys(),
            ], 422);
        }

        Log::info('validated all req fields');

        if (in_array(strtolower($request->input('tipSolicitare')), $type3Values) && $request->input('idsAutorizatiiAnterioareSelected')) {
            Log::info('Solicitare cu autorizatii anterioare');

            $respMessage = storeType3SolicitarePj($request, $request->input('idsAutorizatiiAnterioareSelected'), $solicitantiFailed);
            return response()->json([
                'message' => $respMessage,
            ], 200);
        } else {
            Log::info('Solicitare normala');

            $respMessage = storeDbRecordsPJ($request, $solicitantiFailed, $request->input('idsAutorizatiiAnterioareSelected'));
            return response()->json([
                'message' => $respMessage,
            ], 200);

        }

    }

    public function validateCnpAndGetAutorizatii(string $cnp, string $tipSolicitare)
    {
        if (!valid_CNP($cnp))
            return response()->json(['isValid' => false]);

        $columns = [
            'tip_comisie',
            'nr_aut',
            'serie_aut',
            'data_elib',
            'valabilitate',
            'nume',
            'prenume',
            'cnp',
            'functie',
            'domeniu',
            'autorizatie',
            'unitate',
            'oameni',
            'solicitant_nume',
            'solicitant_prenume',

        ];
        Log::info('tip solicitare ' . $tipSolicitare);
        $tipSolicitareFilter = '';
        $calificativFilter = '';
        switch ($tipSolicitare) {
            case 'IncetareSuspendare':
                $tipSolicitareFilter = ['Suspendare'];
                break;

            case 'Reprogramare':
                $calificativFilter = 'Absent';
                $tipSolicitareFilter = ['Examinare', 'Reautorizare'];
                break;

            case 'Reexaminare':
                $calificativFilter = 'Necorespunzator';
                $tipSolicitareFilter = ['Examinare', 'Reautorizare'];
                break;

            default:
                $tipSolicitareFilter = ['Examinare', 'Reexaminare', 'ReExaminare', 'Reautorizare', 'ReAutorizare', 'Duplicate', 'Preschimb.instalatii', 'Preschimbare', 'VizePeriodice', 'IncetareSuspendare', 'Reprogramare', 'ReProgramare', 'Suspendare', 'SchimbareNume', 'RetragereAutorizatie', 'Duplicat'];
                break;
        }

        $allAutorizatii = Solicitari::where('cnp', $cnp)->orderBy('nr_comisie', 'desc');


        $lastAutorizatie = (clone $allAutorizatii)->first($columns); // Clone the query before executing

        $autorizatii = (clone $allAutorizatii)->where('status', 'AVIZE GENERATE')->get($columns);


        $filteredAutorizatii = $autorizatii;


        if ($tipSolicitareFilter !== '') {
            $filteredAutorizatii = $autorizatii->filter(function ($element) use ($tipSolicitareFilter) {
                return isset($element->tip_comisie) && in_array(strtoupper($element->tip_comisie), array_map('strtoupper', $tipSolicitareFilter));
            });

        }
        if ($calificativFilter !== '') {
            $filteredAutorizatii = $autorizatii->filter(function ($element) use ($calificativFilter) {
                return isset($element->calificativ) && $element->tip_comisie === $calificativFilter;
            });
        }


        return response()->json([
            'isValid' => true,
            'autorizatii' => $filteredAutorizatii->values()->toArray(),
            'autorizatiiColumns' => $columns,
            'nume' => $lastAutorizatie ? $lastAutorizatie->solicitant_nume : '',
            'prenume' => $lastAutorizatie ? $lastAutorizatie->solicitant_prenume : ''


        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $solicitare = Solicitari::where('oameni', $id)->first();
        // $type3Values = [
        //     'vizeperiodice',
        //     'duplicate',
        //     'schimbarenume',
        //     'suspendare',
        //     'incetaresuspendare',
        //     'retragereautorizatie'
        // ];
        // if (in_array(strtolower($solicitare->tip_comisie), $type3Values)) {

        // }
        // $validateCnpAndGetAutorizatiiResponse = $this->validateCnpAndGetAutorizatii($solicitare->cnp, $solicitare->tip_comisie);
        // Log::info('validate cnp ' . $validateCnpAndGetAutorizatiiResponse);
        // If this is a legal entity request (has id_solicitare_pj), get all related solicitants
        if (!is_null($solicitare) && $solicitare->id_solicitare_pj) {
            $solicitare->solicitanti = Solicitari::select('solicitant_nume as nume', 'solicitant_prenume as prenume', 'cnp', 'motiv_suspendare')
                ->where('id_solicitare_pj', $solicitare->id_solicitare_pj)->groupBy('cnp', 'solicitant_nume', 'solicitant_prenume', 'motiv_suspendare')
                ->get();

            $solicitare->solicitant_pers_juridica_cif = UnitatiFeroviare::where('unitate', $solicitare->unitate)->first()->cif;

        }
        $comisie = Comisie::where('nr_comisie', $solicitare->nr_comisie)->first();
        // combinam data si ora
        $solicitare->datetime_exam_t1 = null;
        $solicitare->datetime_exam_t1_end = null;

        $solicitare->datetime_exam_p1 = null;
        $solicitare->datetime_exam_p1_end = null;

        if ($comisie->data_exam_t1 && $comisie->ora_exam_t1) {
            $startT1 = Carbon::parse($comisie->data_exam_t1 . ' ' . $comisie->ora_exam_t1);
            $solicitare->datetime_exam_t1 = $startT1->format('Y-m-d\TH:i');
            $solicitare->datetime_exam_t1_end = $startT1->copy()->addHours(2)->format('Y-m-d\TH:i');
        }

        if ($comisie->data_exam_p1 && $comisie->ora_exam_p1) {
            $startP1 = Carbon::parse($comisie->data_exam_p1 . ' ' . $comisie->ora_exam_p1);
            $solicitare->datetime_exam_p1 = $startP1->format('Y-m-d\TH:i');
            $solicitare->datetime_exam_p1_end = $startP1->copy()->addHours(2)->format('Y-m-d\TH:i');
        }
        $functions = Functii::where('tata', 0)->with('children')->get();
        Log::info('solicitare cnp' . $solicitare->cnp);
        Log::info('solicitare solicitanti' . $solicitare->solicitanti);

        $unitatiColumns = ['unitate', 'cod_fiscal'];
        $unitatiFeroviare = UnitatiFeroviare::orderBy('unitate')->get();
        $optiuniArtSuspendare = ArticoleSuspendare::get();
        $cenaferuri = Cenafer::all();
        $isfuri = isfuri::all();

        Log::info('solicitare' . $solicitare->solicitanti);


        return view('solicitari.edit', compact('solicitare', 'comisie', 'unitatiFeroviare', 'unitatiColumns', 'functions', 'cenaferuri', 'isfuri', 'optiuniArtSuspendare'));
    }



    public function search(Request $request)
    {
        $unitatiFeroviare = UnitatiFeroviare::all();

        return view('solicitari.edit', compact('unitatiFeroviare'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function updatePf(Request $request, string $id)
    {
        Log::info('update PF' . $id);

        function updateType3SolicitarePf($request, $idsAutorizatiiAnterioare, $comisieId)
        {
            DB::beginTransaction();
            try {
                $comisieExistenta = Comisie::where('nr_comisie', $comisieId)->first();

                $comisieExistenta->cerere_uf_nr = $request->input('numarCerereSolicitant');
                $comisieExistenta->cerere_uf_data = $request->input('dataCerereSolicitant');
                $comisieExistenta->CENAFER = $request->input('locatieCenafer');
                $comisieExistenta->nr_CENAFER = $request->input('numarCenafer');
                $comisieExistenta->data_CENAFER = $request->input('dataCenafer');
                $comisieExistenta->nr_ISF = $request->input('numarIesireSolicitare');
                $comisieExistenta->data_ISF = $request->input('dataIesireSolicitare');
                $comisieExistenta->redactat = $request->input('redactataDeSolicitare');
                $comisieExistenta->data_ora_redactat = $request->input('dataOraRedactareSolicitare');

                $tarif = Tarife::where('tip_solicitare', $request->input('tipSolicitare'))->first();
                if ($tarif) {
                    $tarifTotal = $tarif['tarif_orar'] * $tarif['nr_ore'];
                    $comisieExistenta->tarif_total = $tarifTotal;
                }

                $comisieExistenta->save();


                // Delete existing solicitari for the existing comisie, later we will create them from scratch
                Solicitari::where('nr_comisie', $comisieId)->delete();
                // Log::info('ids aut anterioare' . $idsAutorizatiiAnterioare);

                $solicitariAnterioareUtilizator = Solicitari::select()->whereIn('oameni', $idsAutorizatiiAnterioare)->where('cnp', $request->input('solicitantPersFizicaCnp'))->get();

                // foreach ($solicitariAnterioareUtilizator as $solicitareAnterioaraUtilizator) {
                //     $solicitare = new Solicitari();


                //     $solicitare->fill($solicitareAnterioaraUtilizator->toArray()); // Copiem toate câmpurile din solicitarea anterioară


                //     if (strtolower($request->input('tipSolicitare')) === 'vizeperiodice') {

                //         $solicitare->data_elib = $solicitareAnterioaraUtilizator->data_elib;

                //         // Start with the initial date (either data_val1 or valabilitate)
                //         $baseDate = !empty($solicitareAnterioaraUtilizator->data_val1)
                //             ? Carbon::parse($solicitareAnterioaraUtilizator->data_val1)
                //             : Carbon::parse($solicitareAnterioaraUtilizator->valabilitate);

                //         // Copy the original date for calculations
                //         $currentDate = $baseDate->copy();

                //         // Set data_val1 if it's empty
                //         if (empty($solicitareAnterioaraUtilizator->data_val1)) {
                //             $solicitare->data_val1 = $currentDate;
                //         }

                //         // Populate all data_val fields with consecutive 5-year intervals
                //         for ($i = 2; $i <= 6; $i++) {
                //             $column = "data_val{$i}";

                //             // Add 5 years to the current date
                //             $currentDate = $currentDate->copy()->addYears(5)->subDays(1);

                //             // Set the value for this column
                //             $solicitare->$column = $currentDate;

                //             // If this date is greater than today, update valabilitate and stop
                //             if ($currentDate->greaterThan(Carbon::now())) {
                //                 $solicitare->valabilitate = $currentDate;
                //                 break;
                //             }
                //         }

                //         // If we've gone through all columns and still haven't found a future date,
                //         // add another 5 years to the last date and set it as valabilitate
                //         if ($currentDate->lessThanOrEqualTo(Carbon::now())) {
                //             $finalDate = $currentDate->copy()->addYears(5)->subDays(1);
                //             $solicitare->valabilitate = $finalDate;
                //         }


                //     } else {
                //         $solicitare->data_elib = Carbon::parse($solicitareAnterioaraUtilizator->data_elib)->format('Y-m-d');
                //         $solicitare->valabilitate = Carbon::parse($solicitareAnterioaraUtilizator->valabilitate)->format('Y-m-d');
                //     }

                //     $solicitare->nr_comisie = $comisieExistenta->nr_comisie;
                //     $solicitare->tip_comisie = $request->input('tipSolicitare');
                //     $solicitare->id_isf = $request->input('id_isf');
                //     $solicitare->solicitant_nume = $request->input('solicitantPersFizicaNume');
                //     $solicitare->solicitant_prenume = $request->input('solicitantPersFizicaPrenume');
                //     $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                //     $solicitare->cnp = $request->input('solicitantPersFizicaCnp');
                //     $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi');
                //     $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate');
                //     $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa');
                //     $solicitare->status = 'IN LUCRU';
                //     $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);

                //     $solicitare->motiv_suspendare = $request->input('motivSuspendare');

                //     $solicitare->incetare_suspendare_nr_aviz = $request->input('incetareSuspendareNrAviz');
                //     $solicitare->incetare_suspendare_eliberat_aviz = $request->input('incetareSuspendareEliberatAviz');
                //     $solicitare->incetare_suspendare_data_aviz = $request->input('incetareSuspendareDataAviz');

                //     $solicitare->incetare_suspendare_nr_certificat = $request->input('incetareSuspendareNrCertificat');
                //     $solicitare->incetare_suspendare_eliberat_certificat = $request->input('incetareSuspendareEliberatCertificat');
                //     $solicitare->incetare_suspendare_data_certificat = $request->input('incetareSuspendareDataCertificat');




                //     $solicitare->save();

                //     Log::info('created new solicitare');
                // }
                while ($solicitariAnterioareUtilizator->isNotEmpty()) {
                    $solicitareAnterioaraUtilizator = $solicitariAnterioareUtilizator->shift(); // Remove and process the first item

                    Log::info('iterating over ' . json_encode($solicitareAnterioaraUtilizator));

                    $solicitare = new Solicitari();
                    // Daca este autorizare in functie, adaugam si suspendari pt tot ce e sub acea functie
                    if (strtolower($request->input('tipSolicitare')) === 'suspendare' && $solicitareAnterioaraUtilizator->autorizatie === 'Autorizare în funcție' && empty($solicitareAnterioaraUtilizator->domeniu)) {
                        Log::info('suspendare autorizare in functie');
                        $solicitariCeTrebuieSuspendate = Solicitari::selectRaw('domeniu, autorizatie, GROUP_CONCAT(oameni) as oameni_grupati')
                            ->where('cnp', $solicitareAnterioaraUtilizator->cnp)
                            ->where('tip_comisie', '!=', 'Suspendare')
                            ->where('functie', $solicitareAnterioaraUtilizator->functie)
                            ->whereNotNull('domeniu')
                            ->groupBy('domeniu', 'autorizatie')
                            ->get();
                        Log::info('solicitari ce trebuie suspendate ' . json_encode($solicitariCeTrebuieSuspendate));

                        foreach ($solicitariCeTrebuieSuspendate as $solicitareCeTrebuieSusepndata) {
                            $solicitareGasita = Solicitari::where('oameni', explode(',', $solicitareCeTrebuieSusepndata->oameni_grupati)[0])->first();
                            $solicitariAnterioareUtilizator = $solicitariAnterioareUtilizator->merge([$solicitareGasita]);

                        }
                        Log::info('solicitari ant user ' . json_encode($solicitariAnterioareUtilizator));


                    }

                    $solicitare->fill($solicitareAnterioaraUtilizator->toArray()); // Copiem toate câmpurile din solicitarea anterioară
                    $solicitare->id_aut_anterioara = $solicitareAnterioaraUtilizator->oameni;



                    if (strtolower($request->input('tipSolicitare')) === 'vizeperiodice') {
                        $solicitare->data_elib = $solicitareAnterioaraUtilizator->data_elib;

                        // if ($solicitareAnterioaraUtilizator->valabilitate->greaterThan(Carbon::now()->addDays(45))) {
                        //     return response()->json(['message' => 'Solicitarea poate fi înregistrată doar cu 45 de zile înainte de a-i expira valabilitatea!'], 500);
                        // }

                        // Start with the initial date (either data_val1 or valabilitate)
                        $baseDate = !empty($solicitareAnterioaraUtilizator->data_val1) //2020-07-26
                            ? Carbon::parse($solicitareAnterioaraUtilizator->data_val1)
                            : Carbon::parse($solicitareAnterioaraUtilizator->valabilitate);

                        // Copy the original date for calculations
                        $currentDate = $baseDate->copy();

                        // Set data_val1 if it's empty
                        if (empty($solicitareAnterioaraUtilizator->data_val1)) {
                            $solicitare->data_val1 = $currentDate;
                        }
                        //elib 22-06-2015
                        // data val 1 21-06-2020

                        // data val 2 20-06-2025

                        // Populate all data_val fields with consecutive 5-year intervals
                        for ($i = 2; $i <= 6; $i++) {
                            $column = "data_val{$i}";

                            // Add 5 years to the current date
                            $currentDate = $currentDate->copy()->addYears(5)->subDays(1);

                            // Set the value for this column
                            $solicitare->$column = $currentDate;

                            // If this date is greater than today + 45 days, update valabilitate and stop
                            if ($currentDate->greaterThan(Carbon::now())) {
                                $solicitare->valabilitate = $currentDate;
                                break;
                            }
                        }

                        // If we've gone through all columns and still haven't found a future date,
                        // add another 5 years to the last date and set it as valabilitate
                        if ($currentDate->lessThanOrEqualTo(Carbon::now())) {
                            $finalDate = $currentDate->copy()->addYears(5)->subDays(1);
                            $solicitare->valabilitate = $finalDate;
                        }
                    } else {
                        $solicitare->data_elib = Carbon::parse($solicitareAnterioaraUtilizator->data_elib)->format('Y-m-d');
                        $solicitare->valabilitate = Carbon::parse($solicitareAnterioaraUtilizator->valabilitate)->format('Y-m-d');
                    }

                    $solicitare->nr_comisie = $comisieExistenta->nr_comisie;
                    $solicitare->tip_comisie = $request->input('tipSolicitare');
                    $solicitare->id_isf = $request->input('id_isf');
                    $solicitare->solicitant_nume = $request->input('solicitantPersFizicaNume');
                    $solicitare->solicitant_prenume = $request->input('solicitantPersFizicaPrenume');
                    $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                    $solicitare->cnp = $request->input('solicitantPersFizicaCnp');
                    $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi');
                    $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate');
                    $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa');
                    $solicitare->status = 'IN LUCRU';
                    $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);

                    $solicitare->motiv_suspendare = $request->input('motivSuspendare');

                    $solicitare->incetare_suspendare_nr_aviz = $request->input('incetareSuspendareNrAviz');
                    $solicitare->incetare_suspendare_eliberat_aviz = $request->input('incetareSuspendareEliberatAviz');
                    $solicitare->incetare_suspendare_data_aviz = $request->input('incetareSuspendareDataAviz');

                    $solicitare->incetare_suspendare_nr_certificat = $request->input('incetareSuspendareNrCertificat');
                    $solicitare->incetare_suspendare_eliberat_certificat = $request->input('incetareSuspendareEliberatCertificat');
                    $solicitare->incetare_suspendare_data_certificat = $request->input('incetareSuspendareDataCertificat');


                    $solicitare->perioada_suspendare = $request->input('nrZileSuspendare');


                    $solicitare->save();

                    Log::info('created new solicitare');
                }


                DB::commit();
                return response()->json(['message' => 'Solicitare actualizată cu succes!'], 200);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error updating data', ['error' => $e->getMessage()]);
                return response()->json(['message' => 'Error updating data', 'error' => $e->getMessage()], 500);
            }
        }

        function updateDbRecords($request, $idsAutorizatiiAnterioare, $comisieId, $idAutAnterioara)
        {
            DB::beginTransaction();
            try {
                $comisieExistenta = Comisie::where('nr_comisie', $comisieId)->first();

                // Update Comisie record
                $comisieExistenta->cerere_uf_nr = $request->input('numarCerereSolicitant');
                $comisieExistenta->cerere_uf_data = $request->input('dataCerereSolicitant');
                $comisieExistenta->CENAFER = $request->input('locatieCenafer');
                $comisieExistenta->nr_CENAFER = $request->input('numarCenafer');
                $comisieExistenta->data_CENAFER = $request->input('dataCenafer');
                $comisieExistenta->nume_cenafer1 = $request->input('numeMembruComisie');
                $comisieExistenta->prenume_cenafer1 = $request->input('prenumeMembruComisie');
                $comisieExistenta->nume_cenafer2 = $request->input('numeReprezentantCenafer');
                $comisieExistenta->prenume_cenafer2 = $request->input('prenumeReprezentantCenafer');
                $comisieExistenta->redactat = $request->input('redactataDeSolicitare');
                $comisieExistenta->nr_ISF = $request->input('numarIesireSolicitare');
                $comisieExistenta->data_ISF = $request->input('dataIesireSolicitare');
                $comisieExistenta->data_ora_redactat = $request->input('dataOraRedactareSolicitare');
                $comisieExistenta->loc_exam_t = $request->input('locatieExaminareTeoretica');

                $dataExamT1 = date('Y-m-d', strtotime($request->input('dataInceputExaminareTeoretica')));
                $oraExamT1 = date('H:i:s', strtotime($request->input('dataInceputExaminareTeoretica')));
                $comisieExistenta->data_exam_t1 = $dataExamT1;
                $comisieExistenta->ora_exam_t1 = $oraExamT1;

                $comisieExistenta->loc_exam_p = $request->input('locatieExaminarePractica');
                $dataExamP1 = date('Y-m-d', strtotime($request->input('dataInceputExaminarePractica')));
                $oraExamP1 = date('H:i:s', strtotime($request->input('dataInceputExaminarePractica')));
                $comisieExistenta->data_exam_p1 = $dataExamP1;
                $comisieExistenta->ora_exam_p1 = $oraExamP1;

                $comisieExistenta->urgent = $request->input('regimUrgenta') === false ? 'Nu' : 'Da';
                $comisieExistenta->nr_aprobare = $request->input('numarAprobareInitialaScmap');
                $comisieExistenta->data_aprobare = $request->input('dataAprobareInitialaScmap');
                $comisieExistenta->tip_comisie_id = $request->input('tipSolicitareId');

                $tarif = Tarife::where('tip_solicitare', $request->input('tipSolicitare'))->first();
                if ($tarif) {
                    $tarifTotal = $tarif['tarif_orar'] * $tarif['nr_ore'];
                    $comisieExistenta->tarif_total = $tarifTotal;
                }

                $comisieExistenta->save();

                // Update Solicitari record
                $solicitari = Solicitari::where('nr_comisie', $comisieId)->get();


                if ($solicitari) {
                    foreach ($solicitari as $solicitare) {
                        $solicitare->tip_comisie = $request->input('tipSolicitare');
                        $solicitare->id_isf = $request->input('id_isf');
                        $solicitare->nume = $request->input('numePresedinteComisie');
                        $solicitare->prenume = $request->input('prenumePresedinteComisie');
                        $solicitare->solicitant_nume = $request->input('solicitantPersFizicaNume');
                        $solicitare->solicitant_prenume = $request->input('solicitantPersFizicaPrenume');
                        $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                        $solicitare->cnp = $request->input('solicitantPersFizicaCnp');
                        $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi');
                        $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate');
                        $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa');
                        $solicitare->functie = $request->input('functie');
                        $solicitare->autorizatie = $request->input('autorizare');
                        $solicitare->domeniu = $request->input('activitate');

                        if (strtolower($request->input('tipSolicitare')) === 'reautorizare') {
                            // $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);

                            $solicitareAnterioaraUtilizator = Solicitari::select()->where('oameni', $idAutAnterioara);

                            $solicitare->nr_aut = $solicitareAnterioaraUtilizator->nr_aut;
                            $solicitare->serie_aut = $solicitareAnterioaraUtilizator->serie_aut;

                        }

                        $solicitare->save();
                    }
                }

                DB::commit();
                return response()->json(['message' => 'Solicitare actualizată cu succes!'], 200);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error updating data', ['error' => $e->getMessage()]);
                return response()->json(['message' => 'Error updating data', 'error' => $e->getMessage()], 500);
            }
        }

        $cnp = $request->input('solicitantPersFizicaCnp');
        $functie = $request->input('functie');
        $autorizare = $request->input('autorizare');
        $dataExamenTeoretic = date('Y-m-d', strtotime($request->input('dataInceputExaminareTeoretica')));

        $checkTypes = ['examinare', 'reexaminare', 'reprogramare', 'reautorizare'];
        // Facem check urile doar pt tipurile de solicitari care se programeaza pentru o anumita functie
        if (in_array(strtolower($request->input('tipSolicitare')), $checkTypes)) {

            if ($autorizare !== 'Autorizare în funcție' && $autorizare !== 'Permis de conducere pe tip de locomotivã-automotor') {
                // Verifică dacă utilizatorul are deja o autorizație emisă in functie și valabilă pentru funcția respectivă
                $autorizatieExistenta = Solicitari::where('cnp', $cnp)
                    ->where('functie', $functie)
                    ->where('oameni', '!=', $id) // Add this line to exclude current record
                    ->whereIn('autorizatie', ['Autorizare în funcție', 'Permis de conducere pe tip de locomotivã-automotor'])
                    ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS'])
                    ->whereDate('valabilitate', '>=', now()) // Verifică dacă este încă valabilă
                    ->exists();
                // de verificat daca are suspendare pt functie

                if (!$autorizatieExistenta) {
                    return response()->json([
                        'message' => 'Utilizatorul nu are o autorizație emisă și valabilă pentru această funcție.',
                    ], 500);
                }
            }

            $solicitariInLucru = Solicitari::where('cnp', $cnp)
                ->where('oameni', '!=', $id) // Add this line to exclude current record
                ->where('status', '!=', 'AVIZE GENERATE')
                ->whereIn('nr_comisie', function ($query) use ($dataExamenTeoretic) {
                    $query->select('nr_comisie')
                        ->from('comisie')
                        ->whereDate('data_exam_t1', $dataExamenTeoretic);
                })
                ->get();

            if ($solicitariInLucru->where('functie', $functie)->count() >= 3) {
                return response()->json([
                    'message' => 'Utilizatorul are deja trei solicitări în lucru pentru această funcție în ziua examinării.',
                ], 500);
            }

            // Dacă există o solicitare pentru o funcție diferită în aceeași zi de examinare, blochează introducerea
            if ($solicitariInLucru->where('functie', '!=', $functie)->count() > 0) {
                return response()->json([
                    'message' => 'Utilizatorul are deja o solicitare în lucru pentru o funcție diferită în ziua examinării.',
                ], 500);
            }
        }

        $type2Values = ['reexaminare', 'reprogramare', 'reautorizare'];

        $type3Values = [
            'vizeperiodice',
            'duplicate',
            'schimbarenume',
            'suspendare',
            'incetaresuspendare',

            'retragereautorizatie'
        ];

        // The rest of the validation rules remain the same
        $fieldsToValidate = [
            'tipSolicitare' => 'required|string|max:255',
            'numarCerereSolicitant' => 'required|string|max:50',
            'dataCerereSolicitant' => 'required',
            'locatieCenafer' => 'required|string|max:255',
            'numarCenafer' => 'nullable|string|max:50',
            'dataCenafer' => 'nullable',
            'numarIesireSolicitare' => 'required|string|max:50',
            'dataIesireSolicitare' => 'required',
            'redactataDeSolicitare' => 'required|string|max:80',
            'dataOraRedactareSolicitare' => 'required',
            'solicitantPersFizicaNume' => 'required|string|max:255',
            'solicitantPersFizicaPrenume' => 'required|string|max:255',
            'solicitantPersFizicaSerieCi' => 'required|string|size:2|regex:/^[a-zA-Z]+$/',
            'solicitantPersFizicaCnp' => 'required|string|size:13|regex:/^[0-9]+$/',
            'solicitantPersFizicaNumarCi' => 'required|string|max:6|regex:/^[0-9]+$/',
            'solicitantPersFizicaLocalitate' => 'required|string|max:255',
            'solicitantPersFizicaAdresa' => 'required|string|max:255',
            'regimUrgenta' => 'required|boolean',
            'numeMembruComisie' => 'required|string|max:255',
            'prenumeMembruComisie' => 'required|string|max:255',
            'locatieExaminareTeoretica' => 'required|string|max:255',
            'dataInceputExaminareTeoretica' => 'required',
            'dataSfarsitExaminareTeoretica' => 'required',
            'locatieExaminarePractica' => 'required',
            'dataInceputExaminarePractica' => 'required',
            'dataSfarsitExaminarePractica' => 'required',
            'numeReprezentantCenafer' => 'required|string|max:255',
            'prenumeReprezentantCenafer' => 'required|string|max:255',
            'id_isf' => 'required|numeric',
            'numePresedinteComisie' => 'required|string|max:255',
            'prenumePresedinteComisie' => 'required|string|max:255',
            'functie' => 'required|string|max:255',
            'autorizare' => 'required|string|max:255',
            'activitate' => 'nullable|string|max:255',
            'emailCfrIsf' => 'required|string|max:255',
            'numarAprobareInitialaScmap' => 'nullable|string|max:50',
            'dataAprobareInitiala' => 'nullable',
            'solicitantPersJuridicaDenumire' => 'nullable|string|max:255',
            'solicitantPersJuridicaCif' => 'nullable|string|max:255',
            'solicitanti' => 'nullable|array',
            'numeReprezentantUnitateFeroviara' => 'nullable|string|max:255',
            'prenumeReprezentantUnitateFeroviara' => 'nullable|string|max:255',
            'serieAtestatReprezentant' => 'nullable|string|max:50',
            'numarAtestatReprezentant' => 'nullable|string|max:50',
            'idsAutorizatiiAnterioareSelected' => 'nullable|array',
            'motivSuspendare' => 'nullable|string|max:255',
            'incetareSuspendareNrAviz' => 'nullable|string|max:255',
            'incetareSuspendareEliberatAviz' => 'nullable|string|max:255',
            'incetareSuspendareDataAviz' => 'nullable|string',
            'incetareSuspendareNrCertificat' => 'nullable|string|max:255',
            'incetareSuspendareEliberatCertificat' => 'nullable|string|max:255',
            'incetareSuspendareDataCertificat' => 'nullable|string',

        ];

        switch (strtolower($request->input('tipSolicitare'))) {
            case $type2Values[0]://reexaminare
            case $type2Values[2]://reaturoizare
                $fieldsToValidate['numeReprezentantCenafer'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeReprezentantCenafer'] = 'nullable|string|max:255';
                $fieldsToValidate['numarAprobareInitialaScmap'] = 'nullable|string|max:50';
                $fieldsToValidate['dataAprobareInitialaScmap'] = 'nullable';
                if (strtolower($request->input('tipSolicitare') === $type2Values[2])) {
                    $fieldsToValidate['locatieCenafer'] = 'nullable';
                    $fieldsToValidate['numarCenafer'] = 'nullable';
                    $fieldsToValidate['dataCenafer'] = 'nullable';
                    $fieldsToValidate['idsAutorizatiiAnterioareSelected'] = 'required|array';
                    $fieldsToValidate['numarAprobareInitialaScmap'] = 'nullable|string|max:50';
                    $fieldsToValidate['dataAprobareInitialaScmap'] = 'nullable';
                }
                break;

            case $type2Values[1]://reprogramare
                $fieldsToValidate['regimUrgenta'] = 'nullable|boolean';
                $fieldsToValidate['numarAprobareInitialaScmap'] = 'required|string|max:50';
                $fieldsToValidate['dataAprobareInitialaScmap'] = 'required';
                break;

            case $type3Values[0]://vize
            case $type3Values[1]://duplicat
            case $type3Values[2]://schimbare nume
            case $type3Values[3]://suspendare
            case $type3Values[4]://incetare suspendare
            case $type3Values[5]://retragere
                $fieldsToValidate['regimUrgenta'] = 'nullable|boolean';
                $fieldsToValidate['functie'] = 'nullable|string';
                $fieldsToValidate['autorizare'] = 'nullable|string';
                $fieldsToValidate['numePresedinteComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumePresedinteComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['numeMembruComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeMembruComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['numeReprezentantCenafer'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeReprezentantCenafer'] = 'nullable|string|max:255';
                $fieldsToValidate['numeReprezentantUnitateFeroviara'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeReprezentantUnitateFeroviara'] = 'nullable|string|max:255';
                $fieldsToValidate['serieAtestatReprezentant'] = 'nullable|string|max:50';
                $fieldsToValidate['numarAtestatReprezentant'] = 'nullable|string|max:50';
                $fieldsToValidate['locatieExaminareTeoretica'] = 'nullable|string|max:255';
                $fieldsToValidate['dataInceputExaminareTeoretica'] = 'nullable';
                $fieldsToValidate['dataSfarsitExaminareTeoretica'] = 'nullable';
                $fieldsToValidate['locatieExaminarePractica'] = 'nullable|string|max:255';
                $fieldsToValidate['dataInceputExaminarePractica'] = 'nullable';
                $fieldsToValidate['dataSfarsitExaminarePractica'] = 'nullable';
                $fieldsToValidate['idsAutorizatiiAnterioareSelected'] = 'required|array';
                $fieldsToValidate['numarCenafer'] = 'nullable';
                $fieldsToValidate['dataCenafer'] = 'nullable';
                if (strtolower($request->input('tipSolicitare') === $type3Values[3])) {
                    $fieldsToValidate['motivSuspendare'] = 'required|string|max:255';
                }
                if (strtolower($request->input('tipSolicitare') === $type3Values[4])) {
                    $fieldsToValidate['incetareSuspendareNrAviz'] = 'required|string|max:255';
                    $fieldsToValidate['incetareSuspendareEliberatAviz'] = 'required|string|max:255';
                    $fieldsToValidate['incetareSuspendareDataAviz'] = 'required|string';
                    $fieldsToValidate['incetareSuspendareNrCertificat'] = 'required|string|max:255';
                    $fieldsToValidate['incetareSuspendareEliberatCertificat'] = 'required|string|max:255';
                    $fieldsToValidate['incetareSuspendareDataCertificat'] = 'required|string';

                }
                break;

            default:
            // default nu face nimic
        }

        $validator = Validator::make($request->all(), $fieldsToValidate);
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Eroare de validare',
                'errors' => $validator->errors()->keys(),
            ], 422);
        }

        Log::info('validated all req fields');
        $solicitare = Solicitari::where('oameni', $id)->first();
        $comisieId = $solicitare->nr_comisie;
        $idAutAnterioara = $solicitare->id_aut_anterioara;

        if (in_array(strtolower($request->input('tipSolicitare')), $type3Values) && $request->input('idsAutorizatiiAnterioareSelected')) {
            Log::info('Solicitare cu autorizatii anterioare');
            return updateType3SolicitarePf($request, $request->input('idsAutorizatiiAnterioareSelected'), $comisieId);
        } else {
            Log::info('Solicitare normala');
            return updateDbRecords($request, $request->input('idsAutorizatiiAnterioareSelected'), $comisieId, $idAutAnterioara);
        }
    }

    public function updatePj(Request $request, string $id)
    {
        Log::info('update PJ' . $id);

        function updateType3SolicitarePj($request, $idsAutorizatiiAnterioare, $solicitantiFailed, $comisieId)
        {
            DB::beginTransaction();
            try {
                // Update existing Comisie
                $comisieExistenta = Comisie::where('nr_comisie', $comisieId)->first();
                if (!$comisieExistenta) {
                    throw new \Exception('Comisia nu a fost găsită');
                }

                $comisieExistenta->cerere_uf_nr = $request->input('numarCerereSolicitant');
                $comisieExistenta->cerere_uf_data = $request->input('dataCerereSolicitant');
                $comisieExistenta->CENAFER = $request->input('locatieCenafer');
                $comisieExistenta->nr_CENAFER = $request->input('numarCenafer');
                $comisieExistenta->data_CENAFER = $request->input('dataCenafer');
                $comisieExistenta->nr_ISF = $request->input('numarIesireSolicitare');
                $comisieExistenta->data_ISF = $request->input('dataIesireSolicitare');
                $comisieExistenta->redactat = $request->input('redactataDeSolicitare');
                $comisieExistenta->data_ora_redactat = $request->input('dataOraRedactareSolicitare');

                $columnsComisie = [
                    'nume_cenafer1',
                    'prenume_cenafer1',
                    'serie_cenafer1',
                    'numar_cenafer1',
                    'nume_uf',
                    'prenume_uf',
                    'serie_uf',
                    'numar_uf',
                    'loc_exam_t',
                    'data_exam_t1',
                    'ora_exam_t1',
                    'loc_exam_p',
                    'data_exam_p1',
                    'ora_exam_p1',
                    'urgent',
                    'nr_aprobare',
                    'data_aprobare',
                    'tip_comisie_id',
                    'nr_ISF',
                    'data_ISF'
                ];

                $querySolicitari = Solicitari::select()->where('oameni', $idsAutorizatiiAnterioare[0])->first();
                $queryComisie = Comisie::select(...$columnsComisie)->where('nr_comisie', $querySolicitari->nr_comisie)->first();

                $comisieExistenta->nume_cenafer1 = $queryComisie->nume_cenafer1 ?? '';
                $comisieExistenta->prenume_cenafer1 = $queryComisie->prenume_cenafer1;
                $comisieExistenta->serie_cenafer1 = $queryComisie->serie_cenafer1;
                $comisieExistenta->numar_cenafer1 = $queryComisie->numar_cenafer1;
                $comisieExistenta->nume_uf = $queryComisie->nume_uf;
                $comisieExistenta->prenume_uf = $queryComisie->prenume_uf;
                $comisieExistenta->serie_uf = $queryComisie->serie_uf;
                $comisieExistenta->numar_uf = $queryComisie->numar_uf;
                $comisieExistenta->loc_exam_t = $queryComisie->loc_exam_t;
                $comisieExistenta->data_exam_t1 = $queryComisie->data_exam_t1;
                $comisieExistenta->ora_exam_t1 = $queryComisie->ora_exam_t1;
                $comisieExistenta->loc_exam_p = $queryComisie->loc_exam_p;
                $comisieExistenta->data_exam_p1 = $queryComisie->data_exam_p1;
                $comisieExistenta->ora_exam_p1 = $queryComisie->ora_exam_p1;
                $comisieExistenta->urgent = $queryComisie->urgent;
                $comisieExistenta->nr_aprobare = $queryComisie->nr_aprobare;
                $comisieExistenta->data_aprobare = $queryComisie->data_aprobare;
                $comisieExistenta->tip_comisie_id = $queryComisie->tip_comisie_id;

                $tarif = Tarife::get()->where('tip_solicitare', $request->input('tipSolicitare'))->first();
                if ($tarif) {
                    $tarifTotal = $tarif['tarif_orar'] * $tarif['nr_ore'];
                    $comisieExistenta->tarif_total = $tarifTotal;
                }

                $comisieExistenta->save();

                // Delete existing solicitari
                Solicitari::where('nr_comisie', $comisieId)->delete();

                $latestIdSolicitarePj = Solicitari::max('id_solicitare_pj');
                $newIdSolicitarePj = $latestIdSolicitarePj ? $latestIdSolicitarePj + 1 : 1;
                // Create new solicitari
                foreach ($request->input('solicitanti') as $solicitant) {
                    $solicitariAnterioareUtilizator = Solicitari::select()
                        ->whereIn('oameni', $idsAutorizatiiAnterioare)
                        ->where('cnp', $solicitant['cnp'])
                        ->get();


                    while ($solicitariAnterioareUtilizator->isNotEmpty()) {
                        $solicitareAnterioaraUtilizator = $solicitariAnterioareUtilizator->shift(); // Remove and process the first item


                        Log::info('iterating over ' . json_encode($solicitariAnterioareUtilizator));
                        Log::info('current item ' . json_encode($solicitareAnterioaraUtilizator));


                        $solicitare = new Solicitari();
                        // Daca este autorizare in functie, adaugam si suspendari pt tot ce e sub acea functie
                        if (strtolower($request->input('tipSolicitare')) === 'suspendare' && $solicitareAnterioaraUtilizator->autorizatie === 'Autorizare în funcție' && empty($solicitareAnterioaraUtilizator->domeniu)) {
                            Log::info('suspendare autorizare in functie');
                            $solicitariCeTrebuieSuspendate = Solicitari::selectRaw('domeniu, autorizatie, GROUP_CONCAT(oameni) as oameni_grupati')
                                ->where('cnp', $solicitareAnterioaraUtilizator->cnp)
                                ->where('tip_comisie', '!=', 'Suspendare')
                                ->where('functie', $solicitareAnterioaraUtilizator->functie)
                                ->whereNotNull('domeniu')
                                ->where('domeniu', '!=', '')
                                ->groupBy('domeniu', 'autorizatie')
                                ->get();
                            Log::info('solicitari ce trebuie suspendate ' . json_encode($solicitariCeTrebuieSuspendate));

                            foreach ($solicitariCeTrebuieSuspendate as $solicitareCeTrebuieSusepndata) {
                                $solicitareGasita = Solicitari::where('oameni', explode(',', $solicitareCeTrebuieSusepndata->oameni_grupati)[0])->first();
                                $solicitariAnterioareUtilizator = $solicitariAnterioareUtilizator->merge([$solicitareGasita]);

                            }
                            Log::info('solicitari ant user ' . json_encode($solicitariAnterioareUtilizator));


                        }

                        // querySolicitari trebuie schimbat si filtrat dupa cnp solicitant curent
                        $solicitare->fill($solicitareAnterioaraUtilizator->toArray()); // Copiem toate câmpurile din solicitarea anterioară
                        $solicitare->id_aut_anterioara = $solicitareAnterioaraUtilizator->oameni;
                        Log::info('solicitare filled ' . $solicitare->functie);
                        Log::info('solicitare filled ' . $solicitare->domeniu);
                        Log::info('solicitare filled ' . $solicitare->autorizatie);

                        if (strtolower($request->input('tipSolicitare')) === 'vizeperiodice') {

                            $solicitare->data_elib = $solicitareAnterioaraUtilizator->data_elib;

                            // if ($solicitareAnterioaraUtilizator->valabilitate->greaterThan(Carbon::now()->addDays(45))) {
                            //      $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul nu are o autorizație emisă și valabilă pentru această funcție';
                            // }
                            // Start with the initial date (either data_val1 or valabilitate)
                            $baseDate = !empty($solicitareAnterioaraUtilizator->data_val1)
                                ? Carbon::parse($solicitareAnterioaraUtilizator->data_val1)
                                : Carbon::parse($solicitareAnterioaraUtilizator->valabilitate);

                            // Copy the original date for calculations
                            $currentDate = $baseDate->copy();

                            // Set data_val1 if it's empty
                            if (empty($solicitareAnterioaraUtilizator->data_val1)) {
                                $solicitare->data_val1 = $currentDate;
                            }

                            // Populate all data_val fields with consecutive 5-year intervals
                            for ($i = 2; $i <= 6; $i++) {
                                $column = "data_val{$i}";

                                // Add 5 years to the current date
                                $currentDate = $currentDate->copy()->addYears(5)->subDays(1);

                                // Set the value for this column
                                $solicitare->$column = $currentDate;

                                // If this date is greater than today, update valabilitate and stop
                                if ($currentDate->greaterThan(Carbon::now())) {
                                    $solicitare->valabilitate = $currentDate;
                                    break;
                                }
                            }

                            // If we've gone through all columns and still haven't found a future date,
                            // add another 5 years to the last date and set it as valabilitate
                            if ($currentDate->lessThanOrEqualTo(Carbon::now())) {
                                $finalDate = $currentDate->copy()->addYears(5)->subDays(1);
                                $solicitare->valabilitate = $finalDate;
                            }


                        } else {
                            $solicitare->data_elib = Carbon::parse($solicitareAnterioaraUtilizator->data_elib)->format('Y-m-d');
                            $solicitare->valabilitate = Carbon::parse($solicitareAnterioaraUtilizator->valabilitate)->format('Y-m-d');
                        }

                        $solicitare->id_solicitare_pj = $newIdSolicitarePj;
                        $solicitare->nr_comisie = $comisieExistenta->nr_comisie;
                        $solicitare->tip_comisie = $request->input('tipSolicitare');

                        $solicitare->id_isf = $request->input('id_isf'); // coloana noua cu referinta la ISFURI
                        $solicitare->nume = $request->input('numePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaNume
                        $solicitare->prenume = $request->input('prenumePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaPrenume
                        $solicitare->unitate = $request->input('solicitantPersJuridicaDenumire');
                        $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                        $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi'); // mai e necesar?
                        $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate'); // adresa de la pers juridica?
                        $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa'); // adresa de la pers juridica?
                        $solicitare->solicitant_nume = $solicitant['nume'];
                        $solicitare->solicitant_prenume = $solicitant['prenume'];
                        $solicitare->cnp = $solicitant['cnp'];
                        $solicitare->directorASFR = 'Petru BOGDAN';
                        $solicitare->status = 'IN LUCRU';
                        $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);
                        $solicitare->motiv_suspendare = $solicitant['motivSuspendare'];
                        $solicitare->incetare_suspendare_nr_aviz = $solicitant['incetareSuspendareNrAviz'];
                        $solicitare->incetare_suspendare_eliberat_aviz = $solicitant['incetareSuspendareEliberatAviz'];
                        $solicitare->incetare_suspendare_data_aviz = $solicitant['incetareSuspendareDataAviz'];
                        $solicitare->incetare_suspendare_nr_certificat = $solicitant['incetareSuspendareNrCertificat'];
                        $solicitare->incetare_suspendare_eliberat_certificat = $solicitant['incetareSuspendareEliberatCertificat'];
                        $solicitare->incetare_suspendare_data_certificat = $solicitant['incetareSuspendareDataCertificat'];

                        $solicitare->perioada_suspendare = $solicitant['nrZileSuspendare'];


                        if ($request->input('tipSolicitare') === 'VizePeriodice') {

                        }




                        $solicitare->save();

                        Log::info('created new solicitare', ['solicitant' => $solicitant]);
                    }
                }

                DB::commit();

                if (count($solicitantiFailed) > 0) {
                    return 'Solicitare actualizată cu succes! Utilizatorii cu CNP-urile următoare au următoarele erori și nu au fost actualizați: ' . implode(', ', $solicitantiFailed);
                } else {
                    return 'Solicitare actualizată cu succes!';
                }

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error updating data', ['error' => $e->getMessage()]);
                return response()->json(['message' => 'Error updating data', 'error' => $e->getMessage()], 500);
            }
        }

        function updateDbRecordsPJ($request, $solicitantiFailed, $idsAutorizatiiAnterioare, $comisieId)
        {
            DB::beginTransaction();
            try {
                // Update existing Comisie record
                $comisie = Comisie::findOrFail($comisieId);
                $comisie->cerere_uf_nr = $request->input('numarCerereSolicitant');
                $comisie->cerere_uf_data = $request->input('dataCerereSolicitant');
                $comisie->CENAFER = $request->input('locatieCenafer');
                $comisie->nr_CENAFER = $request->input('numarCenafer');
                $comisie->data_CENAFER = $request->input('dataCenafer');
                $comisie->nume_cenafer1 = $request->input('numeMembruComisie');
                $comisie->prenume_cenafer1 = $request->input('prenumeMembruComisie');
                $comisie->nume_uf = $request->input('numeReprezentantUnitateFeroviara');
                $comisie->prenume_uf = $request->input('prenumeReprezentantUnitateFeroviara');
                $comisie->serie_uf = $request->input('serieAtestatReprezentant');
                $comisie->numar_uf = $request->input('numarAtestatReprezentant');
                $comisie->redactat = $request->input('redactataDeSolicitare');
                $comisie->nr_ISF = $request->input('numarIesireSolicitare');
                $comisie->data_ISF = $request->input('dataIesireSolicitare');
                $comisie->data_ora_redactat = $request->input('dataOraRedactareSolicitare');
                $comisie->loc_exam_t = $request->input('locatieExaminareTeoretica');

                $dataExamT1 = date('Y-m-d', strtotime($request->input('dataInceputExaminareTeoretica')));
                $oraExamT1 = date('H:i:s', strtotime($request->input('dataInceputExaminareTeoretica')));
                $comisie->data_exam_t1 = $dataExamT1;
                $comisie->ora_exam_t1 = $oraExamT1;

                $comisie->loc_exam_p = $request->input('locatieExaminarePractica');
                $dataExamP1 = date('Y-m-d', strtotime($request->input('dataInceputExaminarePractica')));
                $oraExamP1 = date('H:i:s', strtotime($request->input('dataInceputExaminarePractica')));
                $comisie->data_exam_p1 = $dataExamP1;
                $comisie->ora_exam_p1 = $oraExamP1;

                $comisie->urgent = $request->input('regimUrgenta') == false ? 'Nu' : 'Da';
                $comisie->nr_aprobare = $request->input('numarAprobareInitialaScmap');
                $comisie->data_aprobare = $request->input('dataAprobareInitialaScmap');
                $comisie->tip_comisie_id = $request->input('tipSolicitareId');

                $comisie->save();
                Log::info('updated comisie');

                // Delete existing solicitari for this comisie
                Solicitari::where('nr_comisie', $comisie->nr_comisie)->delete();

                // Create new solicitari
                $latestIdSolicitarePj = Solicitari::max('id_solicitare_pj');
                $newIdSolicitarePj = $latestIdSolicitarePj ? $latestIdSolicitarePj + 1 : 1;
                if (strtolower($request->input('tipSolicitare')) === 'reautorizare') {
                    foreach ($request->input('solicitanti') as $solicitant) {
                        $solicitariAnterioareUtilizator = Solicitari::select()->whereIn('oameni', $idsAutorizatiiAnterioare)->where('cnp', $solicitant['cnp'])->get();
                        foreach ($solicitariAnterioareUtilizator as $solicitareAnterioaraUtilizator) {
                            $solicitare = new Solicitari();
                            $solicitare->nr_comisie = $comisie->nr_comisie;
                            $solicitare->tip_comisie = $request->input('tipSolicitare');

                            $solicitare->id_isf = $request->input('id_isf'); // coloana noua cu referinta la ISFURI
                            $solicitare->nume = $request->input('numePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaNume
                            $solicitare->prenume = $request->input('prenumePresedinteComisie'); // de verificat daca aici trebuie puse, conflict cu solicitantPersFizicaPrenume
                            $solicitare->unitate = $request->input('solicitantPersJuridicaDenumire');
                            $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                            $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi'); // mai e necesar?
                            $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate'); // adresa de la pers juridica?
                            $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa'); // adresa de la pers juridica?
                            $solicitare->functie = $request->input('functie');
                            $solicitare->autorizatie = $request->input('autorizare');
                            $solicitare->domeniu = $request->input('activitate');
                            $solicitare->solicitant_nume = $solicitant['nume'];
                            $solicitare->solicitant_prenume = $solicitant['prenume'];
                            $solicitare->cnp = $solicitant['cnp'];
                            $solicitare->id_solicitare_pj = $newIdSolicitarePj;
                            $solicitare->directorASFR = 'Petru BOGDAN';
                            $solicitare->status = 'IN LUCRU';
                            $solicitare->data_elib = $solicitareAnterioaraUtilizator->data_elib;
                            $solicitare->valabilitate = $solicitareAnterioaraUtilizator->valabilitate;

                            // Copy id_comisie_ant_scmap from previous authorization if it exists
                            if (isset($solicitareAnterioaraUtilizator->id_comisie_ant_scmap)) {
                                $solicitare->id_comisie_ant_scmap = $solicitareAnterioaraUtilizator->id_comisie_ant_scmap;
                            }
                            $solicitare->id_aut_anterioara = $solicitareAnterioaraUtilizator->oameni;


                            // Copy all data_val fields from previous authorization
                            for ($i = 1; $i <= 6; $i++) {
                                $field = "data_val{$i}";
                                if (isset($solicitareAnterioaraUtilizator->$field)) {
                                    $solicitare->$field = $solicitareAnterioaraUtilizator->$field;
                                }
                            }
                            $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);
                            $solicitare->nr_aut = $solicitareAnterioaraUtilizator->nr_aut;
                            $solicitare->serie_aut = $solicitareAnterioaraUtilizator->serie_aut;


                            $solicitare->save();

                            Log::info('created new solicitare', ['solicitant' => $solicitant]);
                        }
                    }
                } else {
                    foreach ($request->input('solicitanti') as $solicitant) {
                        $solicitare = new Solicitari();
                        $solicitare->nr_comisie = $comisie->nr_comisie;
                        $solicitare->tip_comisie = $request->input('tipSolicitare');
                        $solicitare->id_isf = $request->input('id_isf');
                        $solicitare->nume = $request->input('numePresedinteComisie');
                        $solicitare->prenume = $request->input('prenumePresedinteComisie');
                        $solicitare->unitate = $request->input('solicitantPersJuridicaDenumire');
                        $solicitare->serie_ci = $request->input('solicitantPersFizicaSerieCi');
                        $solicitare->nr_ci = $request->input('solicitantPersFizicaNumarCi');
                        $solicitare->localitate_om = $request->input('solicitantPersFizicaLocalitate');
                        $solicitare->adresa_om = $request->input('solicitantPersFizicaAdresa');
                        $solicitare->functie = $request->input('functie');
                        $solicitare->autorizatie = $request->input('autorizare');
                        $solicitare->domeniu = $request->input('activitate');
                        $solicitare->solicitant_nume = $solicitant['nume'];
                        $solicitare->solicitant_prenume = $solicitant['prenume'];
                        $solicitare->cnp = $solicitant['cnp'];
                        $solicitare->id_solicitare_pj = $newIdSolicitarePj;
                        $solicitare->directorASFR = 'Petru BOGDAN';
                        $solicitare->status = 'IN LUCRU';
                        $solicitare->data_elib = Carbon::now()->format('Y-m-d');
                        $solicitare->valabilitate = Carbon::parse($dataExamT1)->addYears(5)->subDays(1)->format('Y-m-d');
                        $solicitare->data_val1 = Carbon::parse($dataExamT1)->addYears(5)->subDays(1)->format('Y-m-d');

                        if (strtolower($request->input('tipSolicitare')) === 'reautorizare') {
                            $solicitare->ids_aut_anterioare = implode(',', $idsAutorizatiiAnterioare);
                        }

                        $solicitare->save();
                        Log::info('updated solicitare', ['solicitant' => $solicitant]);
                    }
                }

                DB::commit();
                Log::info('DB committed successfully');

                if (count($solicitantiFailed) > 0) {
                    $message = 'Solicitare actualizată cu succes! Utilizatorii cu CNP-urile următoare au următoarele erori și nu au fost actualizați: ' . implode(', ', $solicitantiFailed);
                    Log::info('unii solicitanti nu au fost actualizati');
                    return $message;
                } else {
                    Log::info('toti solicitantii au fost actualizati');
                    return 'Solicitare actualizată cu succes!';
                }

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error updating data', ['error' => $e->getMessage()]);
                return response()->json(['message' => 'Error updating data', 'error' => $e->getMessage()], 500);
            }
        }

        $solicitantiFailed = [];

        $solicitantiSucces = array_filter($request->input('solicitanti'), function ($solicitantCurent) use ($request, &$solicitantiFailed) {
            $cnp = $solicitantCurent['cnp'];
            $functie = $request->input('functie');
            $autorizare = $request->input('autorizare');
            $dataExamenTeoretic = date('Y-m-d', strtotime($request->input('dataInceputExaminareTeoretica')));

            $checkTypes = ['examinare', 'reexaminare', 'reprogramare', 'reautorizare'];
            // Facem check urile doar pt tipurile de solicitari care se programeaza pentru o anumita functie
            if (in_array(strtolower($request->input('tipSolicitare')), $checkTypes)) {
                if ($autorizare !== 'Autorizare în funcție' && $autorizare !== 'Permis de conducere pe tip de locomotivã-automotor') {
                    // Verifică dacă utilizatorul are deja o autorizație emisă in functie și valabilă pentru funcția respectivă
                    $autorizatieExistenta = Solicitari::where('cnp', $cnp)
                        ->where('functie', $functie)
                        ->whereIn('autorizatie', ['Autorizare în funcție', 'Permis de conducere pe tip de locomotivã-automotor'])
                        ->whereIn('status', ['AVIZE GENERATE', 'NR AUTORIZARE INTRODUS'])
                        ->whereDate('valabilitate', '>=', now()) // Verifică dacă este încă valabilă
                        ->exists();

                    if (!$autorizatieExistenta) {
                        $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul nu are o autorizație emisă și valabilă pentru această funcție';
                        return false;
                    }
                }

                // Pentru update, excludem solicitarea curentă din verificări
                $solicitariInLucru = Solicitari::where('cnp', $cnp)
                    ->where('status', '!=', 'AVIZE GENERATE')
                    ->where('oameni', '!=', $request->input('oameni')) // Exclude current record
                    ->whereIn('nr_comisie', function ($query) use ($dataExamenTeoretic) {
                        $query->select('nr_comisie')
                            ->from('comisie')
                            ->whereDate('data_exam_t1', $dataExamenTeoretic);
                    })
                    ->get();

                $countSolicitariInregistrareCurenta = Solicitari::where('cnp', $cnp)
                    ->whereIn('oameni', $request->input('idsAutorizatiiAnterioareSelected'))
                    ->where('oameni', '!=', $request->input('oameni')) // Exclude current record
                    ->count();

                if ($solicitariInLucru->where('functie', $functie)->count() >= 3) {
                    $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul are deja trei solicitări în lucru pentru această funcție în ziua examinării';
                    return false;
                }

                if ($solicitariInLucru->where('functie', '!=', $functie)->count() > 0) {
                    $solicitantiFailed[] = $solicitantCurent['cnp'] . '-> Utilizatorul are deja o solicitare în lucru pentru o funcție diferită în ziua examinării';
                    return false;
                }
            }
            return true;
        });

        if (!$solicitantiSucces) {
            return response()->json(['message' => 'Niciun solicitant nu a fost actualizat: ' . implode(',', $solicitantiFailed)], 500);
        }

        $request->merge(['solicitanti' => $solicitantiSucces]);

        Log::info('solicitanti pentru update', $request->input('solicitanti'));

        $type2Values = ['reexaminare', 'reprogramare', 'reautorizare'];

        $type3Values = [
            'vizeperiodice',
            'duplicate',
            'schimbarenume',
            'suspendare',
            'incetaresuspendare',
            'retragereautorizatie'
        ];

        $fieldsToValidate = [
            'tipSolicitare' => 'required|string|max:255',
            'numarCerereSolicitant' => 'required|string|max:50',
            'dataCerereSolicitant' => 'required',
            'locatieCenafer' => 'required|string|max:255',
            'numarCenafer' => 'nullable|string|max:50',
            'dataCenafer' => 'nullable',
            'numarIesireSolicitare' => 'required|string|max:50',
            'dataIesireSolicitare' => 'required',
            'redactataDeSolicitare' => 'required|string|max:80',
            'dataOraRedactareSolicitare' => 'required',
            'solicitantPersFizicaNume' => 'nullable|string|max:255',
            'solicitantPersFizicaPrenume' => 'nullable|string|max:255',
            'solicitantPersFizicaSerieCi' => 'nullable|string|size:2|regex:/^[a-zA-Z]+$/',
            'solicitantPersFizicaCnp' => 'nullable|string|size:13|regex:/^[0-9]+$/',
            'solicitantPersFizicaNumarCi' => 'nullable|string|max:6|regex:/^[0-9]+$/',
            'solicitantPersFizicaLocalitate' => 'nullable|string|max:255',
            'solicitantPersFizicaAdresa' => 'nullable|string|max:255',
            'regimUrgenta' => 'required|boolean',
            'numeMembruComisie' => 'required|string|max:255',
            'prenumeMembruComisie' => 'required|string|max:255',
            'locatieExaminareTeoretica' => 'required|string|max:255',
            'dataInceputExaminareTeoretica' => 'required',
            'dataSfarsitExaminareTeoretica' => 'required',
            'locatieExaminarePractica' => 'required',
            'dataInceputExaminarePractica' => 'required',
            'dataSfarsitExaminarePractica' => 'required',
            'numeReprezentantCenafer' => 'nullable|string|max:255',
            'prenumeReprezentantCenafer' => 'nullable|string|max:255',
            'id_isf' => 'required|numeric',
            'numePresedinteComisie' => 'required|string|max:255',
            'prenumePresedinteComisie' => 'required|string|max:255',
            'functie' => 'required|string|max:255',
            'autorizare' => 'required|string|max:255',
            'activitate' => 'nullable|string|max:255',
            'emailCfrIsf' => 'required|string|max:255',
            'numarAprobareInitialaScmap' => 'nullable|string|max:50',
            'dataAprobareInitiala' => 'nullable',
            'solicitantPersJuridicaDenumire' => 'required|string|max:255',
            'solicitantPersJuridicaCif' => 'required|string|max:255',
            'solicitanti' => 'required|array',
            'numeReprezentantUnitateFeroviara' => 'nullable|string|max:255',
            'prenumeReprezentantUnitateFeroviara' => 'nullable|string|max:255',
            'serieAtestatReprezentant' => 'nullable|string|max:50',
            'numarAtestatReprezentant' => 'nullable|string|max:50',
            'idsAutorizatiiAnterioareSelected' => 'nullable|array',
        ];

        switch (strtolower($request->input('tipSolicitare'))) {
            case $type2Values[0]://reexaminare
            case $type2Values[2]:
                $fieldsToValidate['numeReprezentantUnitateFeroviara'] = 'required|string|max:255';
                $fieldsToValidate['prenumeReprezentantUnitateFeroviara'] = 'required|string|max:255';
                $fieldsToValidate['serieAtestatReprezentant'] = 'required|string|max:50';
                $fieldsToValidate['numarAtestatReprezentant'] = 'required|string|max:50';
                $fieldsToValidate['numarAprobareInitialaScmap'] = 'nullable|string|max:50';
                $fieldsToValidate['dataAprobareInitialaScmap'] = 'nullable';
                if (strtolower($request->input('tipSolicitare') === $type2Values[2])) {
                    $fieldsToValidate['locatieCenafer'] = 'nullable';
                    $fieldsToValidate['numarCenafer'] = 'nullable';
                    $fieldsToValidate['dataCenafer'] = 'nullable';
                    $fieldsToValidate['idsAutorizatiiAnterioareSelected'] = 'required|array';
                    $fieldsToValidate['numarAprobareInitialaScmap'] = 'nullable|string|max:50';
                    $fieldsToValidate['dataAprobareInitialaScmap'] = 'nullable';
                }
                break;

            case $type2Values[1]://reprogramare
                $fieldsToValidate['numeReprezentantUnitateFeroviara'] = 'required|string|max:255';
                $fieldsToValidate['prenumeReprezentantUnitateFeroviara'] = 'required|string|max:255';
                $fieldsToValidate['serieAtestatReprezentant'] = 'required|string|max:50';
                $fieldsToValidate['numarAtestatReprezentant'] = 'required|string|max:50';
                $fieldsToValidate['regimUrgenta'] = 'nullable|boolean';
                $fieldsToValidate['numarAprobareInitialaScmap'] = 'required|string|max:50';
                $fieldsToValidate['dataAprobareInitialaScmap'] = 'required';
                break;

            case $type3Values[0]://vize
            case $type3Values[1]://duplicat
            case $type3Values[2]://schimbare nume
            case $type3Values[3]://suspendare
            case $type3Values[4]://incetare suspendare
            case $type3Values[5]://retragere
                $fieldsToValidate['regimUrgenta'] = 'nullable|boolean';
                $fieldsToValidate['functie'] = 'nullable|string';
                $fieldsToValidate['autorizare'] = 'nullable|string';
                $fieldsToValidate['numePresedinteComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumePresedinteComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['numeMembruComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeMembruComisie'] = 'nullable|string|max:255';
                $fieldsToValidate['numeReprezentantUnitateFeroviara'] = 'nullable|string|max:255';
                $fieldsToValidate['prenumeReprezentantUnitateFeroviara'] = 'nullable|string|max:255';
                $fieldsToValidate['serieAtestatReprezentant'] = 'nullable|string|max:50';
                $fieldsToValidate['numarAtestatReprezentant'] = 'nullable|string|max:50';
                $fieldsToValidate['locatieExaminareTeoretica'] = 'nullable|string|max:255';
                $fieldsToValidate['dataInceputExaminareTeoretica'] = 'nullable';
                $fieldsToValidate['dataSfarsitExaminareTeoretica'] = 'nullable';
                $fieldsToValidate['locatieExaminarePractica'] = 'nullable|string|max:255';
                $fieldsToValidate['dataInceputExaminarePractica'] = 'nullable';
                $fieldsToValidate['dataSfarsitExaminarePractica'] = 'nullable';
                $fieldsToValidate['numarCenafer'] = 'nullable|array';
                $fieldsToValidate['dataCenafer'] = 'nullable|array';
                if (strtolower($request->input('tipSolicitare') === $type3Values[3])) {
                    $fieldsToValidate['solicitanti.*.motivSuspendare'] = 'required|string|max:255';
                }
                if (strtolower($request->input('tipSolicitare') === $type3Values[4])) {
                    $fieldsToValidate['solicitanti.*.incetareSuspendareNrAviz'] = 'required|string|max:255';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareEliberatAviz'] = 'required|string|max:255';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareDataAviz'] = 'required|string';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareNrCertificat'] = 'required|string|max:255';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareEliberatCertificat'] = 'required|string|max:255';
                    $fieldsToValidate['solicitanti.*.incetareSuspendareDataCertificat'] = 'required|string';

                }
                break;

            default:
            // default nu face nimic
        }

        $validator = Validator::make($request->all(), $fieldsToValidate);
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Eroare de validare',
                'errors' => $validator->errors()->keys(),
            ], 422);
        }

        Log::info('validated all req fields');

        $comisieId = Solicitari::where('id_solicitare_pj', $id)->first()->nr_comisie;


        if (in_array(strtolower($request->input('tipSolicitare')), $type3Values) && $request->input('idsAutorizatiiAnterioareSelected')) {
            Log::info('Solicitare cu autorizatii anterioare');
            return updateType3SolicitarePj($request, $request->input('idsAutorizatiiAnterioareSelected'), $solicitantiFailed, $comisieId);
        } else {
            Log::info('Solicitare normala');
            return updateDbRecordsPJ($request, $solicitantiFailed, $request->input('idsAutorizatiiAnterioareSelected'), $comisieId);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function stergereInregPf(string $oameni)
    {
        try {
            DB::beginTransaction();

            $solicitare = Solicitari::where('oameni', $oameni)->first();

            Comisie::where('nr_comisie', $solicitare->nr_comisie)->delete();

            Solicitari::where('oameni', $oameni)->delete();

            DB::commit();

            return response()->json([
                'status' => 200,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ]);
        }


    }

    public function stergereInregPj(string $id_solicitare_pj)
    {
        try {
            DB::beginTransaction();

            $solicitare = Solicitari::where('id_solicitare_pj', $id_solicitare_pj)->first();

            Solicitari::where('nr_comisie', $solicitare->nr_comisie)->delete();

            Comisie::where('nr_comisie', $solicitare->nr_comisie)->delete();

            DB::commit();

            return response()->json([
                'status' => 200,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function intoarcereStatusPf(string $oameni)
    {
        try {
            $solicitare = Solicitari::where('oameni', $oameni)->first();
            $tip = strtolower($solicitare->tip_comisie);

            Log::info('Solicitare găsită:', ['oameni' => $oameni, 'status' => $solicitare?->status]);

            $statusuri = [
                'examinare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'COMUNICARE VALIDATA', 'PV TIPARIT', 'PV VALIDAT', 'REZULTATE INTRODUSE', 'PV REZULTATE PRINTAT', 'PV REZULTATE VALIDATE', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'vizeperiodice' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'duplicate' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'schimbarenume' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'suspendare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'incetaresuspendare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'retragereautorizatie' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'reautorizare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'COMUNICARE VALIDATA', 'PV TIPARIT', 'PV VALIDAT', 'REZULTATE INTRODUSE', 'PV REZULTATE PRINTAT', 'AVIZE GENERATE'],
                'reprogramare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'], // fallback
                'reexaminare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'], // fallback
            ];

            // determină lista de statusuri în funcție de comisia de bază dacă e reprogramare/reexaminare
            if (in_array($tip, ['reprogramare', 'reexaminare']) && $solicitare->id_aut_anterioara) {
                $solicitareAnterioara = Solicitari::where('oameni', $solicitare->id_aut_anterioara)->first();
                $tipComisieBaza = strtolower($solicitareAnterioara->tip_comisie ?? 'examinare');
                $lista = $statusuri[$tipComisieBaza] ?? [];
            } else {
                $lista = $statusuri[$tip] ?? [];
            }

            $indexCurent = array_search($solicitare->status, $lista);

            Log::info('Status curent: ' . $solicitare->status);
            Log::info('Index curent: ' . $indexCurent);

            if ($indexCurent !== false && $indexCurent > 0) {
                $solicitare->status = $lista[$indexCurent - 1];
                $solicitare->save();

                return response()->json(['status' => 200]);
            }

            return response()->json(['status' => 200]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ]);
        }

    }

    public function intoarcereStatusPj(string $oameni)
    {
        try {
            $solicitare = Solicitari::where('oameni', $oameni)->first();
            Log::info('solicitarepj' . $solicitare . 'oamenipj' . $oameni);
            $tip = strtolower($solicitare->tip_comisie);
            $status = $solicitare->status;
            Log::info('Solicitare găsită:', ['oameni' => $oameni, 'status' => $solicitare?->status]);

            $statusuri = [
                'examinare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'COMUNICARE VALIDATA', 'PV TIPARIT', 'PV VALIDAT', 'REZULTATE INTRODUSE', 'PV REZULTATE PRINTAT', 'PV REZULTATE VALIDATE', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'vizeperiodice' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'duplicate' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'schimbarenume' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'suspendare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'incetaresuspendare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'retragereautorizatie' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'],
                'reautorizare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'COMUNICARE VALIDATA', 'PV TIPARIT', 'PV VALIDAT', 'REZULTATE INTRODUSE', 'PV REZULTATE PRINTAT', 'AVIZE GENERATE'],
                'reprogramare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'], // fallback
                'reexaminare' => ['IN LUCRU', 'COMUNICARE TIPARITA', 'NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'], // fallback
            ];
            // determină lista de statusuri în funcție de comisia de bază dacă e reprogramare/reexaminare
            if (in_array($tip, ['reprogramare', 'reexaminare']) && $solicitare->id_aut_anterioara) {

                $solicitareAnterioara = Solicitari::where('oameni', $solicitare->id_aut_anterioara)->first();
                $tipComisieBaza = strtolower($solicitareAnterioara->tip_comisie ?? 'examinare');
                $lista = $statusuri[$tipComisieBaza] ?? [];
            } else {
                $lista = $statusuri[$tip] ?? [];
            }
            $indexCurent = array_search($solicitare->status, $lista);
            Log::info('Status curent: ' . $solicitare->status);
            Log::info('Index curent: ' . $indexCurent);


            if ($indexCurent !== false && $indexCurent > 0) {
                if (in_array($status, ['NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'])) {


                    $solicitare->status = $lista[$indexCurent - 1];
                    $solicitare->save();

                    return response()->json(['status' => 200]);

                } else {
                    $indexCurent = array_search($solicitare->status, $lista);

                    Solicitari::where('id_solicitare_pj', $solicitare->id_solicitare_pj)->whereNotIn('status', ['NR AUTORIZARE INTRODUS', 'AVIZE GENERATE'])->update(['status' => $lista[$indexCurent - 1]]);

                    return response()->json(['status' => 200]);

                }
            }
            return response()->json(['status' => 200]);



        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ]);
        }

    }
}



