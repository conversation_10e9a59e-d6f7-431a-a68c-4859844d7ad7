<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Solicitari extends Model
{
    protected $table = 'personal';

    public $timestamps = false;

    protected $primaryKey = 'oameni';


    protected $fillable = [ // la ce proprietati pot folosi comanda cu fill
        'mat1_nota1',
        'mat1_nota2',
        'mat1_nota3',
        'mat1_nota4',
        'mat1_nota5',
        'mat2_nota1',
        'mat2_nota2',
        'mat2_nota3',
        'mat2_nota4',
        'mat2_nota5',
        'mat3_nota1',
        'mat3_nota2',
        'mat3_nota3',
        'mat3_nota4',
        'mat3_nota5',
        'mat1_medie',
        'mat2_medie',
        'mat3_medie',
        'medie_t',
        'calificativ_t',
        'calificativ_p',
        'calificativ',
        'functie',
        'domeniu',
        'autorizatie',
        'serie_aut',
        'nr_aut',
        'motiv_suspendare',
        'data_val1',
        'data_val2',
        'data_val3',
        'data_val4',
        'data_val5',
        'data_val6',
        'id_aut_anterioara'
        // Add any other fields you plan to update
    ];
}
