<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ModalUnitatiFeroviare extends Component
{
    public $unitati;
    public $columns;

    /**
     * Create a new component instance.
     */
    public function __construct($unitati = [], $columns = [])
    {
        $this->unitati = $unitati;
        $this->columns = $columns;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.modal-unitati-feroviare');
    }
}
