<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class TreeView extends Component
{
    public $functions;

    /**
     * Create a new component instance.
     */
    public function __construct($functions = [])
    {
        $this->functions = $functions;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.tree-view');
    }
}
