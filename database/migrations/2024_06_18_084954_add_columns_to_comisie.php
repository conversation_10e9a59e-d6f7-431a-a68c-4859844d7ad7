<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->dateTime('data_ora_redactat')->nullable(true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->dropColumn('data_ora_redactat');
        });
    }
};
