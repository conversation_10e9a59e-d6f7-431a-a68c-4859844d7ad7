<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            //$table->unsignedInteger('id_isf')->nullable();
            $table->foreign('id_isf')->references('contor')->on('isfuri');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            //$table->dropForeign(['id_isf']);

            $table->dropColumn('id_isf');
        });
    }
};
