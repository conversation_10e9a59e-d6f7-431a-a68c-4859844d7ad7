<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $tipuriComisie = [
            'Reprogramare',
            'Duplicate',
            'VizePeriodice',
            'Suspendare',
            'IncetareSuspendare',
            'Reexaminare',
            'SchimbareNume',
            'RetragereAutorizatie',
            'Examinare',
            'Preschimbare',
            'Reautorizare'
        ];

        foreach ($tipuriComisie as $tip) {
            DB::table('tip_comisie')->insert(['denumire' => $tip]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

        $tipuriComisie = [
            'Reprogramare',
            'Duplicate',
            'VizePeriodice',
            'Reautorizare',
            'Suspendare',
            'IncetareSuspendare',
            'Reexaminare',
            'Sc<PERSON>bareNume',
            'RetragereAutorizatie',
            'Examinare',
            'Preschimbare'
        ];

        // Delete each tip comisie
        DB::table('tip_comisie')->whereIn('denumire', $tipuriComisie)->delete();
    }
};
