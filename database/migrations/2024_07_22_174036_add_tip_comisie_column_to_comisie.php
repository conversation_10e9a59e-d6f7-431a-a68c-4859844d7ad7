<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->unsignedBigInteger('tip_comisie_id')->nullable();
            $table->foreign('tip_comisie_id')->references('id')->on('tip_comisie');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->dropForeign(['tip_comisie_id']);

            $table->dropColumn('tip_comisie_id');
        });
    }
};
