<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tarife', function (Blueprint $table) {
            $table->id();
            $table->string('tip_solicitare');
            $table->unsignedInteger('tarif_orar');
            $table->unsignedInteger('nr_ore');
            $table->unsignedInteger('multiplier_tarif_urgenta');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tarife');
    }
};
