<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $tipuriTarife = [
            ['tip_solicitare' => 'Examinare', 'tarif_orar' => 35, 'nr_ore' => 5, 'multiplier_tarif_urgenta' => 2],
            ['tip_solicitare' => 'Reautorizare', 'tarif_orar' => 35, 'nr_ore' => 5, 'multiplier_tarif_urgenta' => 2],
            ['tip_solicitare' => 'Reexaminare', 'tarif_orar' => 35, 'nr_ore' => 5, 'multiplier_tarif_urgenta' => 2],
            ['tip_solicitare' => 'VizePeriodice', 'tarif_orar' => 35, 'nr_ore' => 2, 'multiplier_tarif_urgenta' => 1],
            ['tip_solicitare' => 'Duplicate', 'tarif_orar' => 35, 'nr_ore' => 1, 'multiplier_tarif_urgenta' => 1],
            ['tip_solicitare' => 'SchimbareNume', 'tarif_orar' => 35, 'nr_ore' => 1, 'multiplier_tarif_urgenta' => 1],

        ];

        foreach ($tipuriTarife as $tip) {
            DB::table('tarife')->insert([
                'tip_solicitare' => $tip['tip_solicitare'],
                'tarif_orar' => $tip['tarif_orar'],
                'nr_ore' => $tip['nr_ore'],
                'multiplier_tarif_urgenta' => $tip['multiplier_tarif_urgenta']
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tipuriTarife = [
            'Examinare',
            'Reautorizare',
            'Reexaminare',
            'VizePeriodice',
            'Duplicate',
            'SchimbareNume',
        ];


        DB::table('tarife')->whereIn('tip_solicitare', $tipuriTarife)->delete();
    }
};
