<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('personal')->where(function ($query) {
            $query->whereNull('status')
                ->orWhere('status', '');
        })->update(['status' => 'AVIZE GENERATE']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

        DB::table('personal')->where('status', 'AVIZE GENERATE')->update(['status' => null]);


    }
};
