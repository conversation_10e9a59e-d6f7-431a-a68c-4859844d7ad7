<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
       Schema::table('isfuri', function (Blueprint $table){
           $table->string('email_ISF', 255)->change();
       });
       

        //DB::table('isfuri')->delete();
        // Populăm datele în tabelul existent
        DB::table('isfuri')->insert([
            [
                'nr_ISF' => '1',
                'ISF' => 'București',
                'sef_ISF' => 'Mihai <PERSON>OȘU',
                'tel_ISF' => '021 / 316.43.00',
                'tel_ISF_CFR' => '91 / 145.624',
                'fax_ISF' => '021 / 316.43.00',
                'fax_ISF_CFR' => '91 / 145.625',
                'cod_serviciu' => '2011',
                'de_la' => '2013-10-15',
                'pana_la' => '2013-10-27',
                'actual' => 0,
                'contor' => 1,
                'email_ISF' => '<EMAIL>',
            ],
            [
                'nr_ISF' => '2',
                'ISF' => 'Brașov',
                'sef_ISF' => 'Laurian CERNAT',
                'tel_ISF' => '0268 / 470.926',
                'tel_ISF_CFR' => '95 / 145.050',
                'fax_ISF' => '0268 / 470.926',
                'fax_ISF_CFR' => '95 / 145.051',
                'cod_serviciu' => '2012',
                'de_la' => '2013-10-15',
                'pana_la' => '2013-12-10',
                'actual' => 0,
                'contor' => 2,
                'email_ISF' => '<EMAIL>',
            ],
            [
                'nr_ISF' => '3',
                'ISF' => 'Craiova',
                'sef_ISF' => 'Ionel CRÎNGUȘ',
                'tel_ISF' => '0251 / 412.030',
                'tel_ISF_CFR' => '92 / 125.000',
                'fax_ISF' => '0251 / 412.030',
                'fax_ISF_CFR' => '92 / 125.001',
                'cod_serviciu' => '2020',
                'de_la' => '2013-10-15',
                'pana_la' => '2013-12-10',
                'actual' => 0,
                'contor' => 3,
                'email_ISF' => '<EMAIL>',
            ],
            [
                'nr_ISF' => '4',
                'ISF' => 'Timișoara',
                'sef_ISF' => 'Dănuț RADOSAV',
                'tel_ISF' => '0256 / 200.244',
                'tel_ISF_CFR' => '93 / 145.350',
                'fax_ISF' => '0256 / 200.244',
                'fax_ISF_CFR' => '93 / 145.352',
                'cod_serviciu' => '2030',
                'de_la' => '2013-10-15',
                'pana_la' => '2013-12-10',
                'actual' => 0,
                'contor' => 4,
                'email_ISF' => '<EMAIL>',
            ],
            [
                'nr_ISF' => '5',
                'ISF' => 'Cluj',
                'sef_ISF' => 'Mircea BIBAN',
                'tel_ISF' => '0264 / 597.953',
                'tel_ISF_CFR' => '94 / 125.035',
                'fax_ISF' => '0264 / 597.953',
                'fax_ISF_CFR' => '94 / 125.033',
                'cod_serviciu' => '2040',
                'de_la' => '2013-10-15',
                'pana_la' => '2013-12-10',
                'actual' => 0,
                'contor' => 5,
                'email_ISF' => '<EMAIL>',
            ],
            [
                'nr_ISF' => '6',
                'ISF' => 'Iași',
                'sef_ISF' => 'Gabriel ZISU',
                'tel_ISF' => '0232 / 212.684',
                'tel_ISF_CFR' => '96 / 125.300',
                'fax_ISF' => '0232 / 212.684',
                'fax_ISF_CFR' => '96 / 125.301',
                'cod_serviciu' => '2050',
                'de_la' => '2013-10-15',
                'pana_la' => '2013-12-10',
                'actual' => 0,
                'contor' => 6,
                'email_ISF' => '<EMAIL>',
            ],
            [
                'nr_ISF' => '7',
                'ISF' => 'Galați',
                'sef_ISF' => 'Cristinel FLOREA',
                'tel_ISF' => '0236 / 415.807',
                'tel_ISF_CFR' => '97 / 125.050',
                'fax_ISF' => '0236 / 415.807',
                'fax_ISF_CFR' => '97 / 125.051',
                'cod_serviciu' => '2060',
                'de_la' => '2013-10-15',
                'pana_la' => '2013-12-10',
                'actual' => 0,
                'contor' => 7,
                'email_ISF' => '<EMAIL>',
            ],
            [
                'nr_ISF' => '8',
                'ISF' => 'Constanța',
                'sef_ISF' => 'Daniel CIHODARU',
                'tel_ISF' => '0241 / 580.254',
                'tel_ISF_CFR' => '98 / 175.300',
                'fax_ISF' => '0241 / 580.254',
                'fax_ISF_CFR' => '98 / 175.301',
                'cod_serviciu' => '2070',
                'de_la' => '2013-10-15',
                'pana_la' => '2013-12-10',
                'actual' => 0,
                'contor' => 8,
                'email_ISF' => '<EMAIL>',
            ],
            // Poți adăuga mai multe înregistrări aici
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

               Schema::table('isfuri', function (Blueprint $table){
           $table->string('email_ISF', 30)->change();
       });

        // Șterge datele inserate
        //DB::table('isfuri')->delete();
        // Șterge datele inserate (opțional)
        DB::table('isfuri')->where('nr_ISF', 1)->delete();
        DB::table('isfuri')->where('nr_ISF', 2)->delete();
        DB::table('isfuri')->where('nr_ISF', 3)->delete();
        DB::table('isfuri')->where('nr_ISF', 4)->delete();
        DB::table('isfuri')->where('nr_ISF', 5)->delete();
        DB::table('isfuri')->where('nr_ISF', 6)->delete();
        DB::table('isfuri')->where('nr_ISF', 7)->delete();
        DB::table('isfuri')->where('nr_ISF', 8)->delete();
    }
};
