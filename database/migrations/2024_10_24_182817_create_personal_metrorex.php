<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
{
    Schema::create('personal_metrorex', function (Blueprint $table) {
        $table->id();
        $table->string('nume')->nullable();
        $table->string('prenume')->nullable();
        $table->string('cnp')->nullable();
        $table->date('data_pv')->nullable();
        $table->integer('numar_pv')->nullable();
        $table->longtext('fotografie')->nullable();
        $table->string('functie')->nullable();
        $table->timestamps();
    });
}


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('personal_metrorex')->truncate();
    }
};
