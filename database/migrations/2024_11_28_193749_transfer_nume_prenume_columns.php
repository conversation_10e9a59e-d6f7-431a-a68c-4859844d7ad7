<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {   // unde nu exista solicitant_nume, solicitant_prenume, vor lua valoarea din nume, prenume
        DB::table('personal')
            ->where(function ($query) {
                $query->whereNull('solicitant_nume')->orWhere('solicitant_nume', '');
            })
            ->where(function ($query) {
                $query->whereNull('solicitant_prenume')->orWhere('solicitant_prenume', '');
            })
            ->update([
                'solicitant_nume' => DB::raw('nume'),
                'solicitant_prenume' => DB::raw('prenume'),
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // fara down, nu ar fi ok sa stergem valorile din coloanele solicitant_nume/prenume
    }
};
