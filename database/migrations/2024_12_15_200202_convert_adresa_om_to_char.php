<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;


return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {        // 1. Create a temporary text column
        Schema::table('personal', function (Blueprint $table) {
            $table->string('temp_adresa_om', 255)->nullable()->charset('utf8mb4')->collation('utf8mb4_romanian_ci');
        });

        // 2. Copy and convert the BLOB data to the temporary column
        DB::statement("
        UPDATE personal 
        SET temp_adresa_om = CONVERT(CAST(adresa_om AS CHAR CHARACTER SET latin1) USING utf8mb4)
        WHERE adresa_om IS NOT NULL
        ");

        // 3. Drop the old BLOB column
        Schema::table('personal', function (Blueprint $table) {
            $table->dropColumn('adresa_om');
        });

        // 4. Rename the temporary column to the original column name
        Schema::table('personal', function (Blueprint $table) {
            $table->renameColumn('temp_adresa_om', 'adresa_om');
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the column back to BLOB
        Schema::table('personal', function (Blueprint $table) {
            $table->binary('adresa_om')->nullable();
        });
    }
};
