<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->string('temp_numar_cenafer1', 255)->nullable();
            $table->string('temp_numar_cenafer2', 255)->nullable();

        });

        // Step 2: Copy the data from numar_cenafer1 to temp_numar_cenafer1, converting to string
        DB::table('comisie')
            ->whereNotNull('numar_cenafer1')
            ->update(['temp_numar_cenafer1' => DB::raw('CAST(numar_cenafer1 AS CHAR)')]);

        DB::table('comisie')
            ->whereNotNull('numar_cenafer2')
            ->update(['temp_numar_cenafer2' => DB::raw('CAST(numar_cenafer2 AS CHAR)')]);

        // Step 3: Drop the original numar_cenafer1 column
        Schema::table('comisie', function (Blueprint $table) {
            $table->dropColumn('numar_cenafer1');
            $table->dropColumn('numar_cenafer2');
        });


        // Step 4: Rename the temporary column to match the original column name
        Schema::table('comisie', function (Blueprint $table) {
            $table->renameColumn('temp_numar_cenafer1', 'numar_cenafer1');
            $table->renameColumn('temp_numar_cenafer2', 'numar_cenafer2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->integer('numar_cenafer1')->nullable();
            $table->integer('numar_cenafer2')->nullable();
        });

        // Step 2: Copy the data back from the string column to the original INT column
        DB::table('comisie')
            ->whereNotNull('numar_cenafer1')
            ->update(['numar_cenafer1' => DB::raw('CAST(numar_cenafer1 AS SIGNED)')]);
        DB::table('comisie')
            ->whereNotNull('numar_cenafer2')
            ->update(['numar_cenafer2' => DB::raw('CAST(numar_cenafer2 AS SIGNED)')]);

        // Step 3: Drop the temporary column
        Schema::table('comisie', function (Blueprint $table) {
            $table->dropColumn('temp_numar_cenafer1');
            $table->dropColumn('temp_numar_cenafer2');

        });
    }
};
