<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->string('pv_nr_intrare', 50)->nullable()->change();
            $table->string('pv_nr_iesire', 50)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->string('pv_nr_intrare', 20)->nullable()->change();
            $table->string('pv_nr_iesire', 15)->nullable()->change();
        });
    }
};
