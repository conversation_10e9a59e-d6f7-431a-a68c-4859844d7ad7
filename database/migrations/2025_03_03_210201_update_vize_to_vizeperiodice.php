<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('personal')
        ->where('tip_comisie', 'Vize')
        ->update([
            'tip_comisie' => 'VizePeriodice',
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('personal')
        ->where('tip_comisie', 'VizePeriodice')
        ->update([
            'tip_comisie' => 'Vize',
        ]);
    }
};
