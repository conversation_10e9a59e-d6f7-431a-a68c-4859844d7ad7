<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->string('temp_numar_uf', 15)->nullable();
        });

        DB::table('comisie')
            ->whereNotNull('numar_uf')
            ->update(['temp_numar_uf' => DB::raw('CAST(numar_uf AS CHAR)')]);

        Schema::table('comisie', function (Blueprint $table) {
            $table->dropColumn('numar_uf');
        });
    
    
        // Step 4: Rename the temporary column to match the original column name
        Schema::table('comisie', function (Blueprint $table) {
            $table->renameColumn('temp_numar_uf', 'numar_uf');
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comisie', function (Blueprint $table) {
            $table->string('temp_numar_uf', 15)->nullable();
        });

        DB::table('comisie')
            ->whereNotNull('numar_uf')
            ->update(['temp_numar_uf' => DB::raw('CAST(numar_uf AS SIGNED)')]);

        Schema::table('comisie', function (Blueprint $table) {
            $table->dropColumn('numar_uf');
        });

        Schema::table('comisie', function (Blueprint $table) {
            $table->renameColumn('temp_numar_uf', 'numar_uf');
        });
    }
};
