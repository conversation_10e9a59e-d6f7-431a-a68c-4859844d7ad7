<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Valorile pentru tata
        $tata_values = [6, 17, 20, 23, 26, 29, 47, 54, 63, 68, 89, 103, 129, 205, 224, 229, 238, 241, 244, 247, 250, 256, 301, 308, 314, 321, 327, 333, 339, 346, 352, 355]; //321, 327, 333, 339, 346, 352, 355
        
        // Incremental pentru tip_aut si pozitie
        // $tip_aut_value = 356;
        $pozitie_value = 356;

        // Valorile pentru domeniu
        $domeniu_values = [
            'efectuarea probelor de frână la trenuri',
            'manipularea instalaţiilor de încălzit şi iluminat de la vagoane',
            'manipularea instalaţiilor de încărcare/descărcare a vagoanelor şi asigurarea părţilor mobile ale vagoanelor',
            'manipularea instalaţiilor de siguranţa circulaţiei cu care sunt echipate locomotivele pentru menţinerea pe loc a acestora la manevră şi/sau în circulaţia trenurilor, în cazul conducerii simplificate, fără mecanic ajutor',
            'manevrarea macazului necentralizat',
            'manipularea barierei mecanice necentralizate',
            'amplasarea, supravegherea şi ridicarea semnalelor mobile',
            'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
            'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
            'instalaţii de asigurare cu încuietori cu chei şi bloc',
            'instalaţii de centralizare electromecanică tip CFR',
            'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
            'instalaţii de centralizare electrodinamică cu interfaţă cu pupitru vertical',
            'instalaţii de centralizare electrodinamică cu interfaţă cu pupitru DOMINO',
            'instalaţii de centralizare electrodinamică cu interfaţă cu manipulator şi luminoschemă',
            'instalaţii de centralizare electronică şi instalaţii de centralizare electrodinamică cu post de comandă computerizat',
            'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
            'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
            'electromecanism de macaz/sabot centralizat acţionat cu manivelă',
            'pupitru local instalaţie de semnalizare automată fără semibariere - SAT, instalaţie de semnalizare automată - BAT',
            'masă de manevră şi coloană de manevră',
            'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică - IFTE',
            'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie - RC',
            'instalaţii de siguranţă nou-construite, reînnoite sau modernizate, alte instalaţii de siguranţă neclasificate în tipul celor mai sus prezentate (de exemplu, instalaţii dispecer, instalaţie telecomandă locomotivă etc.)',
            'interfaţa din postul de comandă pe care personalul o manipulează',
            'instalaţii/interfeţe exterioare postului de comandă pe care personalul le manipulează pe teren',
            'instalaţii/interfeţe diferite ale aceluiaşi post de comandă',
            'instalaţii care nu pot fi grupate conform criteriilor menţionate anterior',
            'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
            'instalaţii pentru pornirea /oprirea grupului electrogen'
        ];

        // Asigură-te că există suficientă varietate pentru domeniu
        $domeniu_count = count($domeniu_values);

        foreach ($tata_values as $index => $tata_value) {
            foreach ($domeniu_values as $indey => $domeniu_value) {
                DB::table('functii')->insert([
                    'tata' => $tata_value,
                    'ceprint' => '',  
                    'serie_aut' => '',
                    'domeniu' => $domeniu_value,
                    'pozitie' => $pozitie_value,
                ]);
                $pozitie_value++;
            }

            // Crește valorile pentru tip_aut și pozitie
            // $tip_aut_value++;
        }
    }

    public function down()
    {
        $start_pozitie = 356;
        $end_pozitie = DB::table('functii')->max('pozitie'); // Găsește ultima poziție inserată
    
        if ($end_pozitie) {
            DB::table('functii')->whereBetween('pozitie', [$start_pozitie, $end_pozitie])->delete();
        }
    }
};