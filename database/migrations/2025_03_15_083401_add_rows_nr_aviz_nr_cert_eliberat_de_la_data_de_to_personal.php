<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            $table->text('incetare_suspendare_motiv')->nullable();
            $table->string('incetare_suspendare_nr_aviz')->nullable();
            $table->string('incetare_suspendare_nr_certificat')->nullable();
            $table->string('incetare_suspendare_eliberat_aviz')->nullable();
            $table->string('incetare_suspendare_eliberat_certificat')->nullable();
            $table->date('incetare_suspendare_data_aviz')->nullable();
            $table->date('incetare_suspendare_data_certificat')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            $table->dropColumn(['incetare_suspendare_motiv', 'incetare_suspendare_nr_aviz', 'incetare_suspendare_nr_certificat', 'incetare_suspendare_eliberat_aviz', 'incetare_suspendare_data_aviz', 'incetare_suspendare_eliberat_certificat', 'incetare_suspendare_data_certificat']);
        });
    }
};
