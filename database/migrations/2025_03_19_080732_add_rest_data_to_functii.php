<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Valorile pentru tata
        // $tata_values = [6, 17, 20, 23, 26, 29, 47, 54, 63, 68, 89, 103, 129, 205, 224, 229, 238, 241, 244, 247, 250, 256, 301, 308, 314, 321, 327, 333, 339, 346, 352, 355];
        // Incremental pentru tip_aut si pozitie
        // $tip_aut_value = 356;
    
        // $tata_values_bun = [6];
        // $pozitie_value = 356;

        // Valorile pentru domeniu

        // $domeniu_values_acar = [ // 6
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',//
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
        //     'instalaţii de asigurare cu încuietori cu chei şi bloc',
        //     'instalaţii de centralizare electromecanică tip CFR',
        //     'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
        //     'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
        //     'masă de manevră şi coloană de manevră',
        //     'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică -IFTE',
        //     'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
        //     'instalaţie de asigurare cu chei baston',
        //     'instalaţie de asigurare cu încuietori cu chei fără bloc şi tablu cu contacte electrice pe BLA pe linie simplă',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
        //     'manipularea macazului prevăzut cu pârghie şi transmisie mecanică'
        // ];


        // $domeniu_values_impegat_de_miscare = [
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
        //     'instalaţii de asigurare cu încuietori cu chei şi bloc',
        //     'instalaţii de centralizare electromecanică tip CFR',
        //     'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
        //     'instalaţii de centralizare electrodinamică cu interfaţă cu pupitru vertical',
        //     'instalaţii de centralizare electrodinamică cu interfaţă şi pupitru DOMINO',
        //     'instalaţii de centralizare electrodinamică cu interfaţă cu manipulator şi luminoschemă',
        //     'instalaţii de centralizare electronică şi instalaţii de centralizare electrodinamică cu post de comandă computerizat',
        //     'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
        //     'masă de manevră şi coloană de manevră',
        //     'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică -IFTE',
        //     'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
        //     'instalaţie de asigurare cu chei baston',
        //     'instalaţie de asigurare cu încuietori cu chei fără bloc şi tablu cu contacte electrice pe BLA pe linie simplă',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie dublă',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
        //     'instalaţii pentru pornirea /oprirea grupului electrogen',
        //     'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
        //     'Consolă Dispecer GSM-R'
        // ];

        // $domeniu_values_revizor_ace = [
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
        //     'instalaţii de asigurare cu încuietori cu chei şi bloc',
        //     'instalaţii de centralizare electromecanică tip CFR',
        //     'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
        //     'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
        //     'masă de manevră şi coloană de manevră',
        //     'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
        //     'instalaţie de asigurare cu chei baston',
        //     'instalaţie de asigurare cu încuietori cu chei fără bloc şi tablu cu contacte electrice pe BLA pe linie simplă',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
        //     'manipularea macazului prevăzut cu pârghie şi transmisie mecanică'
        // ];

        // $domeniu_values_manevrant_vagoane = [
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
        //     'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
        //     'masă de manevră şi coloană de manevră',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLSAR pe linie simplă',
        //     'manipularea macazului prevăzut cu pârghie şi transmisie mecanică'
        // ];

        // $domeniu_values_sef_manevra = [
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
        //     'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
        //     'masă de manevră şi coloană de manevră',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLSAR pe linie simplă',
        //     'manipularea macazului prevăzut cu pârghie şi transmisie mecanică'
        // ];

        // $domeniu_values_sef_tren = [
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLSAR pe linie simplă',
        //     'manipularea macazului prevăzut cu pârghie şi transmisie mecanică'
        // ];

        // $domeniu_values_sef_statie_trafic = [
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
        //     'instalaţii de asigurare cu încuietori cu chei şi bloc',
        //     'instalaţii de centralizare electromecanică tip CFR',
        //     'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
        //     'instalaţii de centralizare electrodinamică cu interfaţă cu pupitru vertical',
        //     'instalaţii de centralizare electrodinamică cu interfaţă şi pupitru DOMINO',
        //     'instalaţii de centralizare electrodinamică cu interfaţă cu manipulator şi luminoschemă',
        //     'instalaţii de centralizare electronică şi instalaţii de centralizare electrodinamică cu post de comandă computerizat',
        //     'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
        //     'masă de manevră şi coloană de manevră',
        //     'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică -IFTE',
        //     'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
        //     'instalaţie de asigurare cu chei baston',
        //     'instalaţie de asigurare cu încuietori cu chei fără bloc şi tablu cu contacte electrice pe BLA pe linie simplă',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie dublă',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
        //     'instalaţii pentru pornirea /oprirea grupului electrogen',
        //     'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
        //     'Consolă Dispecer GSM-R'
        // ];

        // $domeniu_values_operator_circulatie = [
        //     'Instalaţia de Management a Traficului Feroviar',
        //     'Instalaţia centrului de control radio -RBC',
        //     'Consola Dispecer GSM-R'
        // ];

        // $domeniu_values_sef_statie_otf = [
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
        //     'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
        //     'instalaţii de asigurare cu încuietori cu chei şi bloc',
        //     'instalaţii de centralizare electromecanică tip CFR',
        //     'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
        //     'instalaţii de centralizare electrodinamică cu interfaţă cu pupitru vertical',
        //     'instalaţii de centralizare electrodinamică cu interfaţă şi pupitru DOMINO',
        //     'instalaţii de centralizare electrodinamică cu interfaţă cu manipulator şi luminoschemă',
        //     'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
        //     'masă de manevră şi coloană de manevră',
        //     'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică -IFTE',
        //     'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
        //     'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
        //     'instalaţii pentru pornirea /oprirea grupului electrogen',
        //     'manipularea macazului prevăzut cu pârghie şi transmisie mecanică'
        // ];

        // $domeniu_values_responsabil_sc_lfi = [
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT'
        // ];

        // $domeniu_values_responssabil_sc_din_furnizori = [
        //     'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
        //     'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
        //     'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT'
        // ];

        // // Asigură-te că există suficientă varietate pentru domeniu
        // $domeniu_count = count($domeniu_values_acar);

        // foreach ($tata_values as $index => $tata_value) {
        //     foreach ($domeniu_values_acar as $indey => $domeniu_value_acar) {
        //         DB::table('functii')->insert([
        //             'tata' => $tata_value,
        //             'ceprint' => '',  
        //             'serie_aut' => '',
        //             'domeniu' => $domeniu_value_acar,
        //             'pozitie' => $pozitie_value,
        //         ]);
        //         $pozitie_value++;
        //     }

        //     // Crește valorile pentru tip_aut și pozitie
        //     // $tip_aut_value++;
        // }
    }

    public function down()
    {
        // $start_pozitie = 356;
        // $end_pozitie = DB::table('functii')->max('pozitie'); // Găsește ultima poziție inserată
    
        // if ($end_pozitie) {
        //     DB::table('functii')->whereBetween('pozitie', [$start_pozitie, $end_pozitie])->delete();
        // }
    }
};