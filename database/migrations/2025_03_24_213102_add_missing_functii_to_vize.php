<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            // Step 1: Get all records where tip_comisie = 'VizePeriodice'
            $records = DB::table('personal')->where('tip_comisie', 'VizePeriodice')->where('functie', null)->get();

            foreach ($records as $record) {
                // Log::info('record ids', $record->ids_aut_anterioare);
                // Step 2: Get ids_aut_anterioare and convert it into an array
                $idsAutorizatii = explode(',', $record->ids_aut_anterioare);

                // Step 3: Find the referenced record
                $referencedRecord = DB::table('personal')
                    ->whereIn('oameni', $idsAutorizatii)
                    ->where('cnp', $record->cnp) // Filter only those with matching CNP
                    ->first();
                // Log::info('record ids', $record->ids_aut_anterioare);


                if ($referencedRecord) {
                    // Step 4: Update current record with missing values
                    DB::table('personal')
                        ->where('oameni', $record->oameni)
                        ->update([
                            'functie' => $referencedRecord->functie ?? $record->functie,
                            'domeniu' => $referencedRecord->domeniu ?? $record->domeniu,
                            'autorizatie' => $referencedRecord->autorizatie ?? $record->autorizatie,
                        ]);
                }
            }
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            //
        });
    }
};
