<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;


return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('personal')
            ->where('tip_comisie', 'Incetare suspendare')
            ->update([
                    'tip_comisie' => 'IncetareSuspendare'
                ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
