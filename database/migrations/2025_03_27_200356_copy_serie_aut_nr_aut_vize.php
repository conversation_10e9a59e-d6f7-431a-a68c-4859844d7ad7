<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //
        Schema::table('personal', function (Blueprint $table) {
            // Step 1: Get all records where tip_comisie = 'VizePeriodice'
            $records = DB::table('personal')->where('tip_comisie', 'VizePeriodice')->where('nr_comisie', 94863)->get();

            foreach ($records as $record) {
                // Step 2: Get ids_aut_anterioare and convert it into an array
                $idsAutorizatii = explode(',', $record->ids_aut_anterioare);

                // Step 3: Find the referenced record
                $referencedRecord = DB::table('personal')
                    ->whereIn('oameni', $idsAutorizatii)
                    ->where('cnp', $record->cnp) // Filter only those with matching CNP
                    ->first();

                Log::info('referenced record nr aut' . $referencedRecord->nr_aut);

                if ($referencedRecord) {
                    // Step 4: Update current record with missing values
                    DB::table('personal')
                        ->where('oameni', $record->oameni)
                        ->update([
                            'nr_aut' => $referencedRecord->nr_aut ?? $record->nr_aut,
                            'serie_aut' => $referencedRecord->serie_aut ?? $record->serie_aut,
                        ]);
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
