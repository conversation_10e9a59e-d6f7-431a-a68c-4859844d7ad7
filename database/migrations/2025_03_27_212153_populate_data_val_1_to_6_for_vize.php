<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            // Get all records where tip_comisie is 'VizePeriodice'
            $records = DB::table('personal')->where('tip_comisie', 'VizePeriodice')->get();

            foreach ($records as $record) {
                if (!empty($record->ids_aut_anterioare)) {
                    $idsSolicitari = explode(',', $record->ids_aut_anterioare);

                    // Find the referenced record with the same CNP
                    $referencedRecord = DB::table('personal')
                        ->whereIn('oameni', $idsSolicitari)
                        ->where('cnp', $record->cnp)
                        ->first();

                    if ($referencedRecord) {
                        $updateData = [];

                        if ($referencedRecord->tip_comisie === 'Examinare') {
                            // If previous record was 'Examinare', set data_val1 to valabilitate
                            $updateData['data_val1'] = $referencedRecord->valabilitate;
                        } else {
                            // If 'VizePeriodice', copy all data_val1-6 values
                            for ($i = 1; $i <= 6; $i++) {
                                $column = "data_val$i";
                                if (!empty($referencedRecord->$column)) {
                                    $updateData[$column] = $referencedRecord->$column;
                                }
                            }
                        }

                        // --- NEW LOGIC: Handle the valabilitate case ---
                        // Find the last filled data_val column
                        $lastFilledValue = null;
                        $lastEmptyColumn = null;

                        for ($i = 1; $i <= 6; $i++) {
                            $column = "data_val$i";
                            if (!empty($record->$column)) {
                                $lastFilledValue = $record->$column; // Update last filled value
                            } elseif (!$lastEmptyColumn) {
                                $lastEmptyColumn = $column; // Find first empty column
                            }
                        }

                        // If valabilitate is greater than the last filled date, add it to the next empty column
                        if ($lastEmptyColumn && $record->valabilitate > $lastFilledValue) {
                            $updateData[$lastEmptyColumn] = $record->valabilitate;
                        }
                        // --- END NEW LOGIC ---

                        // Apply the updates if there is data to update
                        if (!empty($updateData)) {
                            DB::table('personal')
                                ->where('oameni', $record->oameni)
                                ->update($updateData);
                        }
                    }
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            //
        });
    }
};
