<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;


return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('personal')
            ->whereNull('id_isf')
            ->join('comisie', 'personal.nr_comisie', '=', 'comisie.nr_comisie')
            ->join('isfuri', 'comisie.ISF', '=', 'isfuri.ISF')
            ->update(['personal.id_isf' => DB::raw('isfuri.contor')]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
