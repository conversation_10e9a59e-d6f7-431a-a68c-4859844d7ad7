<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;


return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // DB::statement("UPDATE comisie SET nr_aprobare = CONVERT(nr_aprobare, CHAR(255))");
        // DB::statement("ALTER TABLE comisie MODIFY nr_aprobare CHAR(255) NULL");

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

        // DB::statement("ALTER TABLE comisie MODIFY nr_aprobare INT(10) UNSIGNED NULL");

    }
};
