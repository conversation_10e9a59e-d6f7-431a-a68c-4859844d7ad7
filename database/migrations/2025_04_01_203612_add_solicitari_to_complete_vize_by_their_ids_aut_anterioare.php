<?php

// use Illuminate\Database\Migrations\Migration;
// use Illuminate\Database\Schema\Blueprint;
// use Illuminate\Support\Facades\Schema;
// use Illuminate\Support\Facades\DB;
// use Illuminate\Support\Facades\Log;

// return new class extends Migration {
//     /**
//      * Run the migrations.
//      */
//     public function up(): void
//     {
//         // Search for all relevant records for specific types like 'VizePeriodice', 'Duplicate', etc.
//         $solicitari = DB::table('personal')
//             ->whereIn('tip_comisie', ['VizePeriodice', 'Duplicate', 'Suspendare', 'IncetareSuspendare', 'SchimbareNume', 'RetragereAutorizatie'])
//             ->where('nr_comisie', 94867)  // or another condition as needed
//             ->get();

//         // Group records by nr_comisie
//         $solicitariGroupedByComisie = $solicitari->groupBy('nr_comisie');

//         foreach ($solicitariGroupedByComisie as $nrComisie => $group) {
//             // For each group (same nr_comisie), iterate through and process the ids_aut_anterioare

//             Log::info('Processing solicitare for nr_comisie: ' . $nrComisie);

//             // If ids_aut_anterioare is not empty, we need to process the authorization data
//             if (!empty($group[0]->ids_aut_anterioare)) {
//                 // Split ids_aut_anterioare into an array
//                 $idsAutorizatii = explode(',', $group[0]->ids_aut_anterioare);
//                 foreach ($group as $solicitare) {
//                     DB::table('personal')->where('oameni', $solicitare->oameni)->delete();

//                 }
//                 // Loop through each authorization id and recreate the record with correct data
//                 foreach ($idsAutorizatii as $index => $idAutorizatie) {
//                     // Fetch the user’s authorization details based on the id
//                     // $autorizatieUserCurent = DB::table('personal')
//                     //     ->where('oameni', $idAutorizatie)
//                     //     ->where('cnp', $solicitare->cnp)
//                     //     ->first();
//                     $autorizatieAnterioara = DB::table('personal')
//                         ->where('oameni', $idAutorizatie)
//                         ->first();

//                     // if ($autorizatieUserCurent) {
//                     // Log the found authorization
//                     Log::info('Found authorization for ' . $idAutorizatie);

//                     // Delete the previous incorrect entry (based on the 'oameni' key)

//                     // Prepare the new data to insert based on the current authorization
//                     // if (count($group) === count($idsAutorizatii)) {

//                     $data = [];

//                     try {
//                         $data = (array) $group[$index];
//                     } catch (Throwable $e) {
//                         $data = (array) $group[$index / 2];
//                         unset($data['oameni']);
//                     }
//                     $data['functie'] = $autorizatieAnterioara->functie;
//                     $data['domeniu'] = $autorizatieAnterioara->domeniu;
//                     $data['cnp'] = $autorizatieAnterioara->cnp;
//                     $data['autorizatie'] = $autorizatieAnterioara->autorizatie;
//                     $data['mat1_nota1'] = $autorizatieAnterioara->mat1_nota1;
//                     $data['mat1_nota2'] = $autorizatieAnterioara->mat1_nota2;
//                     $data['mat1_nota3'] = $autorizatieAnterioara->mat1_nota3;
//                     $data['mat1_nota4'] = $autorizatieAnterioara->mat1_nota4;
//                     $data['mat1_nota5'] = $autorizatieAnterioara->mat1_nota5;
//                     $data['mat2_nota1'] = $autorizatieAnterioara->mat2_nota1;
//                     $data['mat2_nota2'] = $autorizatieAnterioara->mat2_nota2;
//                     $data['mat2_nota3'] = $autorizatieAnterioara->mat2_nota3;
//                     $data['mat2_nota4'] = $autorizatieAnterioara->mat2_nota4;
//                     $data['mat2_nota5'] = $autorizatieAnterioara->mat2_nota5;
//                     $data['mat3_nota1'] = $autorizatieAnterioara->mat3_nota1;
//                     $data['mat3_nota2'] = $autorizatieAnterioara->mat3_nota2;
//                     $data['mat3_nota3'] = $autorizatieAnterioara->mat3_nota3;
//                     $data['mat3_nota4'] = $autorizatieAnterioara->mat3_nota4;
//                     $data['mat3_nota5'] = $autorizatieAnterioara->mat3_nota5;
//                     $data['mat1_medie'] = $autorizatieAnterioara->mat1_medie;
//                     $data['mat2_medie'] = $autorizatieAnterioara->mat2_medie;
//                     $data['mat3_medie'] = $autorizatieAnterioara->mat3_medie;
//                     $data['medie_t'] = $autorizatieAnterioara->medie_t;
//                     $data['calificativ_t'] = $autorizatieAnterioara->calificativ_t;
//                     $data['calificativ_p'] = $autorizatieAnterioara->calificativ_p;
//                     $data['calificativ'] = $autorizatieAnterioara->calificativ;
//                     $data['serie_aut'] = $autorizatieAnterioara->serie_aut;
//                     $data['nr_aut'] = $autorizatieAnterioara->nr_aut;
//                     $data['data_val1'] = $autorizatieAnterioara->data_val1;
//                     $data['data_val2'] = $autorizatieAnterioara->data_val2;
//                     $data['data_val3'] = $autorizatieAnterioara->data_val3;
//                     $data['data_val4'] = $autorizatieAnterioara->data_val4;
//                     $data['data_val5'] = $autorizatieAnterioara->data_val5;
//                     $data['data_val6'] = $autorizatieAnterioara->data_val6;
//                     DB::table('personal')->insert($data);


//                     // } else {
//                     //     Log::info('count neegal');
//                     //     $filteredSolicitari = array_filter(
//                     //         $group->toArray(),
//                     //         function ($item) use ($autorizatieAnterioara) {
//                     //             return $item->cnp === $autorizatieAnterioara->cnp;
//                     //         }
//                     //     );
//                     //     Log::info('filtered solicitari' . print_r($filteredSolicitari));
//                     //     if ($filteredSolicitari) {
//                     //         foreach($filteredSolicitari as $filteredSolicitare){
//                     //         $data = (array) $filteredSolicitari[0];
//                     //         $data['functie'] = $autorizatieAnterioara->functie;
//                     //         $data['domeniu'] = $autorizatieAnterioara->domeniu;
//                     //         $data['autorizatie'] = $autorizatieAnterioara->autorizatie;
//                     //         $data['mat1_nota1'] = $autorizatieAnterioara->mat1_nota1;
//                     //         $data['mat1_nota2'] = $autorizatieAnterioara->mat1_nota2;
//                     //         $data['mat1_nota3'] = $autorizatieAnterioara->mat1_nota3;
//                     //         $data['mat1_nota4'] = $autorizatieAnterioara->mat1_nota4;
//                     //         $data['mat1_nota5'] = $autorizatieAnterioara->mat1_nota5;
//                     //         $data['mat2_nota1'] = $autorizatieAnterioara->mat2_nota1;
//                     //         $data['mat2_nota2'] = $autorizatieAnterioara->mat2_nota2;
//                     //         $data['mat2_nota3'] = $autorizatieAnterioara->mat2_nota3;
//                     //         $data['mat2_nota4'] = $autorizatieAnterioara->mat2_nota4;
//                     //         $data['mat2_nota5'] = $autorizatieAnterioara->mat2_nota5;
//                     //         $data['mat3_nota1'] = $autorizatieAnterioara->mat3_nota1;
//                     //         $data['mat3_nota2'] = $autorizatieAnterioara->mat3_nota2;
//                     //         $data['mat3_nota3'] = $autorizatieAnterioara->mat3_nota3;
//                     //         $data['mat3_nota4'] = $autorizatieAnterioara->mat3_nota4;
//                     //         $data['mat3_nota5'] = $autorizatieAnterioara->mat3_nota5;
//                     //         $data['mat1_medie'] = $autorizatieAnterioara->mat1_medie;
//                     //         $data['mat2_medie'] = $autorizatieAnterioara->mat2_medie;
//                     //         $data['mat3_medie'] = $autorizatieAnterioara->mat3_medie;
//                     //         $data['medie_t'] = $autorizatieAnterioara->medie_t;
//                     //         $data['calificativ_t'] = $autorizatieAnterioara->calificativ_t;
//                     //         $data['calificativ_p'] = $autorizatieAnterioara->calificativ_p;
//                     //         $data['calificativ'] = $autorizatieAnterioara->calificativ;
//                     //         $data['serie_aut'] = $autorizatieAnterioara->serie_aut;
//                     //         $data['nr_aut'] = $autorizatieAnterioara->nr_aut;
//                     //         $data['data_val1'] = $autorizatieAnterioara->data_val1;
//                     //         $data['data_val2'] = $autorizatieAnterioara->data_val2;
//                     //         $data['data_val3'] = $autorizatieAnterioara->data_val3;
//                     //         $data['data_val4'] = $autorizatieAnterioara->data_val4;
//                     //         $data['data_val5'] = $autorizatieAnterioara->data_val5;
//                     //         $data['data_val6'] = $autorizatieAnterioara->data_val6;

//                     //         unset($data['oameni']);  // Ensure we don't overwrite the primary key 'oameni'
//                     //         DB::table('personal')->insert($data);}
//                     //     }

//                     // }

//                     // Insert the new record



//                     // } else {
//                     //     Log::warning('No authorization found for ' . $idAutorizatie);
//                     // }
//                 }
//             } else {
//                 // Log::warning('No ids_aut_anterioare found for solicitare nr_comisie: ' . $solicitare->nr_comisie);
//             }

//         }
//     }

//     /**
//      * Reverse the migrations.
//      */
//     public function down(): void
//     {
//         Schema::table('complete_vize_by_their_ids_aut_anterioare', function (Blueprint $table) {
//             //
//         });
//     }
// };
