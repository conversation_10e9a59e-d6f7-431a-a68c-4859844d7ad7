<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('personal')->where('tip_comisie', 'Duplicat')->update(['tip_comisie' => 'Duplicate',]);
        DB::table('personal')->where('tip_comisie', 'ReProgramare')->update(['tip_comisie' => 'Reprogramare',]);
        DB::table('personal')->where('tip_comisie', 'ReExaminare')->update(['tip_comisie' => 'Reexaminare',]);
        DB::table('personal')->where('tip_comisie', 'Preschimbare.instalatii')->update(['tip_comisie' => 'Preschimbare',]);
        DB::table('personal')->where('tip_comisie', 'Schimbare Nume')->update(['tip_comisie' => 'SchimbareNume',]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('personal')->where('tip_comisie', 'Duplicate')->update(['tip_comisie' => 'Duplicat']);
        DB::table('personal')->where('tip_comisie', 'Reprogramare')->update(['tip_comisie' => 'ReProgramare']);
        DB::table('personal')->where('tip_comisie', 'Reexaminare')->update(['tip_comisie' => 'ReExaminare']);
        DB::table('personal')->where('tip_comisie', 'Preschimbare')->update(['tip_comisie' => 'Preschimbare.instalatii']);
        DB::table('personal')->where('tip_comisie', 'SchimbareNume')->update(['tip_comisie' => 'Schimbare Nume',]);
    }

};
