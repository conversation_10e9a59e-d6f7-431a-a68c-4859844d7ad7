<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('personal')->whereIn('calificativ_t', ['Corespunzător', 'Corespunzãtor'])->update(['calificativ_t' => 'corespunzator',]);
        DB::table('personal')->whereIn('calificativ_p', ['Corespunzător', 'Corespunzãtor'])->update(['calificativ_p' => 'corespunzator',]);
        DB::table('personal')->whereIn('calificativ', ['Corespunzãtor'])->update(['calificativ' => 'Corespunzător',]);

        DB::table('personal')->whereIn('calificativ_t', ['Necorespunzător', 'Necorespunzãtor'])->update(['calificativ_t' => 'necorespunzator',]);
        DB::table('personal')->whereIn('calificativ_p', ['Necorespunzător', 'Necorespunzãtor'])->update(['calificativ_p' => 'necorespunzator',]);
        DB::table('personal')->whereIn('calificativ', ['Necorespunzãtor'])->update(['calificativ' => 'Necorespunzător',]);


        DB::table('personal')->whereIn('calificativ_t', ['Absent'])->update(['calificativ_t' => 'absent',]);
        DB::table('personal')->whereIn('calificativ_p', ['Absent'])->update(['calificativ_p' => 'absent',]);

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
