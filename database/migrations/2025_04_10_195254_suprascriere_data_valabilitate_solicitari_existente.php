<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        


        $rows = DB::table('personal')->get();

        foreach ($rows as $row) {
            $dates = collect([
                $row->data_val6,
                $row->data_val5,
                $row->data_val4,
                $row->data_val3,
                $row->data_val2,
                $row->data_val1,
            ])->filter()->values(); // păstrează doar valorile nenule

            if ($dates->isNotEmpty()) {
                $firstValidDate = $dates->first();

                if ($row->valabilitate === null || $firstValidDate > $row->valabilitate) {
                    DB::table('personal')
                        ->where('oameni', $row->oameni)
                        ->update(['valabilitate' => $firstValidDate]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};



// Masinist lucrari arta - lipseste din excel, exista in baza de date
// Masinist lucrari cale - lipseste din excel, exista in baza de date
// MECANIC AJUTOR DE LOCOMOTIVĂ - lipseste? in baza exista mecanic ajutor, nu si mecanic ajutor de locomotiva 
// MECANIC DE LOCOMOTIVĂ - AUTOMOTOR -lipseste? in excel exista mecanic de locomotiva, in baza este acesta
// MECANIC MASINI GRELE DE CALE AJUTOR - lipseste din excel, exista in baza de date
// MECANIC MAȘ. GRELE SUDURA CĂII AJUTOR - lipseste din excel, exista in baza de date
// RESPONSABIL SC - lipseste din excel, exista in baza de date
// SEF STATIE - lipseste din excel, exista in baza de date
// SEF STATIE ADJUNCT - lipseste din excel, exista in baza de date
// SEF STATIE TRAFIC - lipseste din excel, exista in baza de date presupun ca este sef statie simplu?
// SEF STATIE REZERVA - lipseste din excel, exista in baza de date
// SEF STATIE ADJUNCT OTF - lipseste din excel, exista in baza de date
// SEF STATIE REZERVA OTF - lipseste din excel, exista in baza de date