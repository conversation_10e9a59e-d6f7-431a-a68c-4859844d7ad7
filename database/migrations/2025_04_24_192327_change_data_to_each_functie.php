<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        
        // Valorile pentru tata
        $tata_values = [6, 17, 20, 23, 26, 29, 47, 54, 63, 68, 89, 103, 129, 205, 224, 229, 238, 241, 244, 247, 250, 256, 301, 308, 314, 321, 327, 333, 339, 346, 352, 355];
        // Incremental pentru tip_aut si pozitie
        // $tip_aut_value = 356;
    
        $pozitie_value = 356;

        // Valorile pentru domeniu
        
        $tata_domenii_operatii_specifice = [
            // ACAR
            1 => [
                'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
                'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
                'instalaţii de asigurare cu încuietori cu chei şi bloc',
                'instalaţii de centralizare electromecanică tip CFR',
                'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
                'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'masă de manevră şi coloană de manevră',
                'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică -IFTE',
                'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
                'instalaţie de asigurare cu chei baston',
                'instalaţie de asigurare cu încuietori cu chei fără bloc şi tablu cu contacte electrice pe BLA pe linie simplă',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
                'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
                'efectuarea probelor de frână la trenuri',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'
            ],
            //CONDUCATOR TREN AJUTOR
            7 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            //CONDUCATOR TREN MACARA
            9 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // CONDUCTOR TREN
            11 => ['efectuarea probelor de frână la trenuri',
            'manipularea instalațiilor de încălzit și iluminat de la vagoane',
            'manipularea instalațiilor de siguranța circulației cu care sunt echipate automotoarele dotate cu calculator de bord pentru menținerea pe loc a acestora la manevră și/sau în circulația trenurilor, în cazul conducerii simplificate - fără mecanic ajutor de locomotivă / automotor',
            'manipularea barierei mecanice necentralizate',
            'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // CONDUCATOR VAGON DORMIT SI CUSETA
            // 18 => [],
            // DISPECER CIRCULAȚIE MIȘCARE
            // 21 => [],
            // DISPECER ENERGETIC FEROVIAR
            24 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // DISPECER ENERGETIC FEROVIAR ȘEF
            27 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ELECTROMECANIC IFTE-LC
            30 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ELECTROMECANIC SCB
            34 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ELECTROMECANIC TTR
            38 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // FRÂNAR
            42 => ['efectuarea probelor de frână la trenuri',
            'manipularea barierei mecanice necentralizate',
            'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // IMPEGAT DE MISCARE
            48 => [
            'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
            'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
            'instalaţii de asigurare cu încuietori cu chei şi bloc',
            'instalaţii de centralizare electromecanică tip CFR',
            'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
            'instalaţii de centralizare electrodinamică cu interfaţă cu pupitru vertical',
            'instalaţii de centralizare electrodinamică cu interfaţă şi pupitru DOMINO',
            'instalaţii de centralizare electrodinamică cu interfaţă cu manipulator şi luminoschemă',
            'instalaţii de centralizare electronică şi instalaţii de centralizare electrodinamică cu post de comandă computerizat',
            'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
            'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
            'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
            'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
            'masă de manevră şi coloană de manevră',
            'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică -IFTE',
            'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
            'instalaţie de asigurare cu chei baston',
            'instalaţie de asigurare cu încuietori cu chei fără bloc şi tablu cu contacte electrice pe BLA pe linie simplă',
            'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice',
            'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie dublă',
            'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
            'instalaţii pentru pornirea /oprirea grupului electrogen',
            'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
            'Consolă Dispecer GSM-R',
            'efectuarea probelor de frână la trenuri',
            'manipularea instalațiilor de încărcare/descărcare a vagoanelor și asigurarea părților mobile a vagoanelor',
            'manipularea instalațiilor de siguranța circulației cu care sunt echipate locomotivele pentru menținerea pe loc a acestora la manevră și/sau în circulația trenurilor, în cazul conducerii simplificate - fără mecanic ajutor de locomotivă / automotor',
            'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MACARAGIU TREN AJUTOR
            // 55 => [],
            // MACARAGIU TREN INTERVENȚIE
            57 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MAGAZINER COMERCIAL
            59 => ['manipularea instalațiilor de încărcare/descărcare a vagoanelor și asigurarea părților mobile a vagoanelor',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MAGAZINER TRANZIT
            64 => ['manipularea instalațiilor de încărcare/descărcare a vagoanelor și asigurarea părților mobile a vagoanelor',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MAISTRU CONSTRUCȚII, MODERNIZĂRI-REABILITĂRI, REPARARE ȘI ÎNTREȚINERE LINII
            69 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MAISTRU LUCRĂRI ARTĂ
            73 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MAISTRU CONSTRUCȚII, MODERNIZĂRI-REABILITĂRI, REPARARE ȘI ÎNTREȚINERE LUCRĂRI ARTĂ
            77 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MAISTRU SUDURA CĂII
            81 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // Manevrant vagoane
            83 => [
                'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
                'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'masă de manevră şi coloană de manevră',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLSAR pe linie simplă',
                'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
                'efectuarea probelor de frână la trenuri',
                'manipularea instalațiilor de încărcare/descărcare a vagoanelor și asigurarea părților mobile a vagoanelor',
                'manipularea barierei mecanice necentralizate',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // Masinist lucrari arta - lipseste
            // 90 => [],
            // Masinist lucrari cale - lipseste
            // 94 => [],
            // MECANIC AJUTOR DE LOCOMOTIVĂ - lipseste? in baza este trecut mecanic ajutor, nu dupa toata denumirea functiei asteia. Sunt acelasi lucru?
            98 => ['efectuarea probelor de frână la trenuri',
            'manevrarea macazului necentralizat',
            'manipularea barierei mecanice necentralizate',
            'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MECANIC DREZINA PANTOGRAF
            104 => ['efectuarea probelor de frână la trenuri',
            'manevrarea macazului necentralizat',
            'manipularea barierei mecanice necentralizate',
            'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MECANIC DE LOCOMOTIVĂ - AUTOMOTOR -lipseste? sau e acelasi lucru cu mecanic de locomotiva trecut in sheetul de excel?
            106 => ['efectuarea probelor de frână la trenuri',
            'manevrarea macazului necentralizat',
            'manipularea barierei mecanice necentralizate',
            'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MECANIC MAȘINI GRELE DE CALE
            194 => ['efectuarea probelor de frână la trenuri',
            'manevrarea macazului necentralizat',
            'manipularea barierei mecanice necentralizate',
            'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MECANIC MASINI GRELE DE CALE AJUTOR - lipseste
            // 200 => [],
            // MECANIC MAȘ. GRELE SUDURA CĂII
            206 => ['efectuarea probelor de frână la trenuri',
            'manevrarea macazului necentralizat',
            'manipularea barierei mecanice necentralizate',
            'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MECANIC MAȘ. GRELE SUDURA CĂII AJUTOR - lipseste
            // 208 =>[],
            // MONTATOR IFTE
            210 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MONTATOR SCB
            212 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // MONTATOR SCB
            216 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // OPERATOR CIRCULATIE MISCARE
            220 =>[
                'instalaţii de centralizare electronică şi instalaţii de centralizare electrodinamică cu post de comandă computerizat',
                'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
                'Instalaţia de Management a Traficului Feroviar',
                'Instalaţia centrului de control radio -RBC',
                'Consola Dispecer GSM-R',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // PAZITOR BARIERA
            225 => [ 'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // PICHER
            230 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // RESPONSABIL SC - lipseste
            // 234 => [],
            // Responsabil SC-LFI
            236 => [
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'manevrarea macazului necentralizat',
                'manipularea barierei mecanice necentralizate',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // Responsabil SC-  FF-IFTE
            239 => [
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'manevrarea macazului necentralizat',
                'manipularea barierei mecanice necentralizate',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // Responsabil SC-  FF-LINII
            242 => [
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'manevrarea macazului necentralizat',
                'manipularea barierei mecanice necentralizate',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // Responsabil SC-  FF-SCB
            245 => [
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'manevrarea macazului necentralizat',
                'manipularea barierei mecanice necentralizate',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // Responsabil SC-  FF-TC
            248 => [
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'manevrarea macazului necentralizat',
                'manipularea barierei mecanice necentralizate',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // REVIZOR ACE
            251 => [
                'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
                'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
                'instalaţii de asigurare cu încuietori cu chei şi bloc',
                'instalaţii de centralizare electromecanică tip CFR',
                'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
                'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'masă de manevră şi coloană de manevră',
                'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
                'instalaţie de asigurare cu chei baston',
                'instalaţie de asigurare cu încuietori cu chei fără bloc şi tablu cu contacte electrice pe BLA pe linie simplă',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
                'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
                'efectuarea probelor de frână la trenuri',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // REVIZOR CALE
            257 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // REVIZOR CALE SI PUNCTE PERICULOASE
            261 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // REVIZOR PUNCTE PERICULOASE
            265 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // REVIZOR TEHNIC VAGOANE
            269 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // SUDOR DIN ACTIVITATEA DE ÎNTREȚINERE CALE ȘI APARATE CALE
            273 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ȘEF DISTRICT EXPLOATARE UTILAJE
            275 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ȘEF DISTRICT LC
            277 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ȘEF DISTRICT LINII
            279 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ȘEF DISTRICT PODURI
            281 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ȘEF DISTRICT SCB
            283 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ȘEF DISTRICT TTR
            285 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ȘEF ECHIPĂ ÎNTEȚINERE CALE
            287 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ȘEF ECHIPĂ ÎNTREȚINERE LUCRĂRI ARTĂ
            291 => ['amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // SEF MANEVRA
            295 =>[
                'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
                'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'masă de manevră şi coloană de manevră',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLSAR pe linie simplă',
                'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
                'efectuarea probelor de frână la trenuri',
                'manipularea instalațiilor de încălzit și iluminat de la vagoane',
                'manipularea instalațiilor de încărcare/descărcare a vagoanelor și asigurarea părților mobile a vagoanelor',
                'manipularea instalațiilor de siguranța circulației cu care sunt echipate locomotivele pentru menținerea pe loc a acestora la manevră și/sau în circulația trenurilor, în cazul conducerii simplificate - fără mecanic ajutor de locomotivă / automotor',
                'manipularea barierei mecanice necentralizate',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // SEF STATIE - lipseste? exista in baza de date cu numele acesta, nu este prezent in excel
            302 => [
                'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
                'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
                'instalaţii de asigurare cu încuietori cu chei şi bloc',
                'instalaţii de centralizare electromecanică tip CFR',
                'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
                'instalaţii de centralizare electrodinamică cu interfaţă cu pupitru vertical',
                'instalaţii de centralizare electrodinamică cu interfaţă şi pupitru DOMINO',
                'instalaţii de centralizare electrodinamică cu interfaţă cu manipulator şi luminoschemă',
                'instalaţii de centralizare electronică şi instalaţii de centralizare electrodinamică cu post de comandă computerizat',
                'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'masă de manevră şi coloană de manevră',
                'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică -IFTE',
                'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
                'instalaţie de asigurare cu chei baston',
                'instalaţie de asigurare cu încuietori cu chei fără bloc şi tablu cu contacte electrice pe BLA pe linie simplă',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie dublă',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
                'instalaţii pentru pornirea /oprirea grupului electrogen',
                'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
                'Consolă Dispecer GSM-R',
                'efectuarea probelor de frână la trenuri',
                'manipularea instalațiilor de încărcare/descărcare a vagoanelor și asigurarea părților mobile a vagoanelor',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'
            ],
            // SEF STATIE ADJUNCT - lipseste
            // 309 => [],
            // SEF STATIE TRAFIC - lipseste? este in excel dar  nu in baza de date presupun ca este sef statie simplu?
            // SEF STATIE REZERVA - lipseste? este in baza de date nu este in excel
            // 315 => [],
            // SEF STATIE OTF
            322 => [
                'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
                'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
                'instalaţii de asigurare cu încuietori cu chei şi bloc',
                'instalaţii de centralizare electromecanică tip CFR',
                'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
                'instalaţii de centralizare electrodinamică cu interfaţă cu pupitru vertical',
                'instalaţii de centralizare electrodinamică cu interfaţă şi pupitru DOMINO',
                'instalaţii de centralizare electrodinamică cu interfaţă cu manipulator şi luminoschemă',
                'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'masă de manevră şi coloană de manevră',
                'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică -IFTE',
                'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
                'instalaţii pentru pornirea /oprirea grupului electrogen',
                'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
                'efectuarea probelor de frână la trenuri',
                'manipularea instalațiilor de încărcare/descărcare a vagoanelor și asigurarea părților mobile a vagoanelor',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // SEF STATIE ADJUNCT OTF - lipseste
            // 328 => [],
            // SEF STATIE REZERVA OTF - lipseste
            // 334 => [],
            // SEF TREN
            340 => [
                'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'masă de manevră şi coloană de manevră',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLSAR pe linie simplă',
                'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
                'efectuarea probelor de frână la trenuri',
                'manipularea instalațiilor de încălzit și iluminat de la vagoane',
                'manipularea instalațiilor de încărcare/descărcare a vagoanelor și asigurarea părților mobile a vagoanelor',
                'manipularea instalațiilor de siguranța circulației cu care sunt echipate locomotivele pentru menținerea pe loc a acestora la manevră și/sau în circulația trenurilor, în cazul conducerii simplificate - fără mecanic ajutor de locomotivă / automotor',
                'manipularea barierei mecanice necentralizate',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // SEF TURA MISCARE
            347 => [
                'instalaţii de asigurare cu încuietori cu chei şi tablou cu ştifturi',
                'instalaţii de asigurare cu încuietori cu chei şi tablou mecanic',
                'instalaţii de asigurare cu încuietori cu chei şi bloc',
                'instalaţii de centralizare electromecanică tip CFR',
                'instalaţii de centralizare electromecanică de alte tipuri cu manipulare similară',
                'instalaţii de centralizare electrodinamică cu interfaţă cu pupitru vertical',
                'instalaţii de centralizare electrodinamică cu interfaţă şi pupitru DOMINO',
                'instalaţii de centralizare electrodinamică cu interfaţă cu manipulator şi luminoschemă',
                'instalaţii de centralizare electronică şi instalaţii de centralizare electrodinamică cu post de comandă computerizat',
                'instalaţii de mecanizare şi automatizare a cocoaşelor de triere',
                'încuietori individuale de semnal, macaz şi sabot de deraiere necentralizate',
                'electromecanism de macaz /sabot centralizat acţionat cu manivelă',
                'pupitru local instalaţie de semnalizare automată fără semibariere -SAT, instalaţie de semnalizare automată -BAT',
                'masă de manevră şi coloană de manevră',
                'manipularea instalaţiilor de comandă de la distanţă a separatoarelor de la instalaţiile fixe de tracţiune electrică -IFTE',
                'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
                'instalaţie de asigurare cu chei baston',
                'instalaţie de asigurare cu încuietori cu chei fără bloc şi tablu cu contacte electrice pe BLA pe linie simplă',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie dublă',
                'instalaţie de asigurare cu încuietori cu chei şi tablouri mecanice cu chei şi contacte electrice şi BLASR pe linie simplă',
                'instalaţii pentru pornirea /oprirea grupului electrogen',
                'manipularea macazului prevăzut cu pârghie şi transmisie mecanică',
                'Consolă Dispecer GSM-R',
                'efectuarea probelor de frână la trenuri',
                'manipularea instalațiilor de încărcare/descărcare a vagoanelor și asigurarea părților mobile a vagoanelor',
                'manipularea instalațiilor de siguranța circulației cu care sunt echipate locomotivele pentru menținerea pe loc a acestora la manevră și/sau în circulația trenurilor, în cazul conducerii simplificate - fără mecanic ajutor de locomotivă / automotor',
                'amplasarea, supravegherea și ridicarea semnalelor mobile'],
            // ȘEF TURĂ REGULATOR
            353 => [
                'instalaţii de centralizare electronică şi instalaţii de centralizare electrodinamică cu post de comandă computerizat',
                'manipularea instalaţiilor de telecomunicaţii din staţii, linie curentă, regulator de circulaţie -RC ',
                'Instalaţia de Management a Traficului Feroviar',
                'Instalaţia centrului de control radio -RBC',
                'Consola Dispecer GSM-R',
                'amplasarea, supravegherea și ridicarea semnalelor mobile']
        ];

        

        // $domeniu_count = count($domeniu_values_acar);

        // foreach ($tata_values as $index => $tata_value) {
        //     foreach ($domeniu_values_acar as $indey => $domeniu_value_acar) {
        //         DB::table('functii')->insert([
        //             'tata' => $tata_value,
        //             'ceprint' => '',  
        //             'serie_aut' => '',
        //             'domeniu' => $domeniu_value_acar,
        //             'pozitie' => $pozitie_value,
        //         ]);
        //         $pozitie_value++;
        //     }
        // }
    }

    public function down()
    {
        // $start_pozitie = 356;
        // $end_pozitie = DB::table('functii')->max('pozitie'); // Găsește ultima poziție inserată
    
        // if ($end_pozitie) {
        //     DB::table('functii')->whereBetween('pozitie', [$start_pozitie, $end_pozitie])->delete();
        // }
    }
};