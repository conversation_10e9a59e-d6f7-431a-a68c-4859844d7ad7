<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Adaugă coloana temporară ca string
        Schema::table('comisie', function (Blueprint $table) {
            $table->string('temp_nr_aprobare', 255)->nullable();
        });

        // 2. Copiază datele cu CAST folosind chunking
        DB::table('comisie')
            ->select('nr_comisie', 'nr_aprobare')
            ->whereNotNull('nr_aprobare')
            ->orderBy('nr_comisie')
            ->chunk(1000, function ($rows) {
                foreach ($rows as $row) {
                    $original = $row->nr_aprobare;
                    $converted = (string) $original;

                    // Verifică dacă conversia păstrează valoarea
                    if ((string)(int)$converted !== (string)$original) {
                        throw new \Exception("Conversie eșuată pentru nr_comisie: {$row->nr_comisie}, valoare: {$original}");
                    }

                    DB::table('comisie')
                        ->where('nr_comisie', $row->nr_comisie)
                        ->update(['temp_nr_aprobare' => $converted]);
                }
            });


        // 3. Șterge coloana veche
        Schema::table('comisie', function (Blueprint $table) {
            $table->dropColumn('nr_aprobare');
        });

        // 4. Redenumește coloana temporară
        Schema::table('comisie', function (Blueprint $table) {
            $table->renameColumn('temp_nr_aprobare', 'nr_aprobare');
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
{
    // 1. Creează o coloană temporară pentru conversia inversă
    Schema::table('comisie', function (Blueprint $table) {
        $table->integer('temp_nr_aprobare')->nullable();
    });

    // 2. Transferă datele înapoi cu validare
    DB::table('comisie')
        ->select('nr_comisie', 'nr_aprobare')
        ->whereNotNull('nr_aprobare')
        ->orderBy('nr_comisie')
        ->chunk(1000, function ($rows) {
            foreach ($rows as $row) {
                $original = $row->nr_aprobare;
                // Validare opțională
                if (!is_numeric($original)) {
                    throw new \Exception("Valoare invalidă la rollback pentru nr_comisie: {$row->nr_comisie}, valoare: {$original}");
                }

                DB::table('comisie')
                    ->where('nr_comisie', $row->nr_comisie)
                    ->update(['temp_nr_aprobare' => (int) $original]);
            }
        });

    // 3. Șterge coloana actuală
    Schema::table('comisie', function (Blueprint $table) {
        $table->dropColumn('nr_aprobare');
    });

    // 4. Redenumește coloana temporară în cea originală
    Schema::table('comisie', function (Blueprint $table) {
        $table->renameColumn('temp_nr_aprobare', 'nr_aprobare');
    });
}


};
