<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        // Adăugăm o coloană temporară de tip FLOAT
        Schema::table('tarife', function (Blueprint $table) {
            $table->float('temp_tarif_orar', 8, 2)->nullable();
        });

        // Copiem valorile din `tarif_orar` în `temp_tarif_orar` (și le putem ajusta)
        DB::table('tarife')->update(['temp_tarif_orar' => 36.96]); // sau altă logică

        // Ștergem coloana originală
        Schema::table('tarife', function (Blueprint $table) {
            $table->dropColumn('tarif_orar');
        });

        // Redenumim coloana temporară
        Schema::table('tarife', function (Blueprint $table) {
            $table->renameColumn('temp_tarif_orar', 'tarif_orar');
        });
    }

    public function down(): void
    {
        // Revertim modificarea
        Schema::table('tarife', function (Blueprint $table) {
            $table->integer('tarif_orar')->nullable();
        });

        // Poți readuce o valoare aproximativă
        DB::table('tarife')->update(['tarif_orar' => 35]);

        // Nu mai trebuie să ștergi nimic, pentru că am redenumit înapoi
    }
};
