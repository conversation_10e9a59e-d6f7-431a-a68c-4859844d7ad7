<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('personal')
            ->whereNotNull('valabilitate')
            ->whereNull('data_val1')
            ->update([
                'data_val1' => DB::raw('valabilitate')
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
