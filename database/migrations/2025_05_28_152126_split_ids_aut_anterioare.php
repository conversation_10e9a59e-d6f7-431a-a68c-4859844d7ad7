<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            // Get all records where ids_aut_anterioare is not empty
            $records = DB::table('personal')
                ->whereNotNull('ids_aut_anterioare')
                ->where('ids_aut_anterioare', '!=', '')
                ->get();

            foreach ($records as $record) {
                // Split the ids_aut_anterioare into an array
                $idsAutorizatiiAnterioare = explode(',', $record->ids_aut_anterioare);

                // Find matching records based on CNP, functie, domeniu, and autorizatie
                $matchingRecord = DB::table('personal')
                    ->whereIn('oameni', $idsAutorizatiiAnterioare)
                    ->where('cnp', $record->cnp)
                    ->whereNotNull('functie')
                    ->where('functie', '!=', '')
                    ->where('functie', $record->functie)
                    ->where('domeniu', $record->domeniu)
                    ->where('autorizatie', $record->autorizatie)
                    ->first();

                if ($matchingRecord) {
                    // Update the current record with the matching id_aut_anterioara
                    DB::table('personal')
                        ->where('oameni', $record->oameni)
                        ->update([
                            'id_aut_anterioara' => $matchingRecord->oameni
                        ]);
                } else {
                    // Log if no matching record is found
                    Log::warning('No matching record found for:', [
                        'oameni' => $record->oameni,
                        'cnp' => $record->cnp,
                        'functie' => $record->functie,
                        'domeniu' => $record->domeniu,
                        'autorizatie' => $record->autorizatie
                    ]);
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            // Clear all id_aut_anterioara values
            DB::table('personal')
                ->whereNotNull('id_aut_anterioara')
                ->update(['id_aut_anterioara' => null]);
        });
    }
};
