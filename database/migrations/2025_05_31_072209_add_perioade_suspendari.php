<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('art_8', function (Blueprint $table) {
            $table->integer('perioada_suspendare')->nullable();
        });

        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. a)')->update(['perioada_suspendare' => 30]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. b)')->update(['perioada_suspendare' => 30]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. c)')->update(['perioada_suspendare' => 30]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. d)')->update(['perioada_suspendare' => 30]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. e)')->update(['perioada_suspendare' => 30]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. f)')->update(['perioada_suspendare' => 30]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. g)')->update(['perioada_suspendare' => 30]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. h)')->update(['perioada_suspendare' => 30]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. i)')->update(['perioada_suspendare' => 30]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. j)')->update(['perioada_suspendare' => 60]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. k)')->update(['perioada_suspendare' => 60]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. l)')->update(['perioada_suspendare' => 60]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. m)')->update(['perioada_suspendare' => 60]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. n)')->update(['perioada_suspendare' => 60]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. o)')->update(['perioada_suspendare' => 60]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. p)')->update(['perioada_suspendare' => 60]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. q)')->update(['perioada_suspendare' => 60]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. r)')->update(['perioada_suspendare' => 60]);
        DB::table('art_8')->where('literaArticol', 'Art. 8 alin 1. lit. s)')->update(['perioada_suspendare' => NULL]);


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('art_8', function (Blueprint $table) {
            $table->dropColumn('perioada_suspendare');
        });
    }
};
