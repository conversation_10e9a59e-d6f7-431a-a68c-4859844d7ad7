<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Populăm statusul
        DB::table('personal')
            ->whereIn('calificativ', ['Necorespunzător', 'Absent'])
            ->update(['status' => 'NECORESPUNZATOR']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
