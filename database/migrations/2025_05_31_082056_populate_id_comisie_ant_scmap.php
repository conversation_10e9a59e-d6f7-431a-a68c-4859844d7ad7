<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('personal')
            ->whereIn('tip_comisie', ['Reprogramare', 'Reexaminare'])
            ->orderBy('oameni')
            ->each(function ($item) {
                $comisieItem = DB::table('comisie')
                    ->where('nr_comisie', $item->nr_comisie)
                    ->first();
                //77939
    
                if (!$comisieItem) {
                    return;
                }

                $comisiiNrIsf = DB::table('comisie')
                    ->where('nr_ISF', $comisieItem->nr_aprobare)//344 / 2071
                    ->where('data_ISF', $comisieItem->data_aprobare)//2022-03-28
                    ->orderBy('nr_comisie')
                    ->get();

                foreach ($comisiiNrIsf as $comisieNrIsf) {
                    $solicitareIdIsf = DB::table('personal')
                        ->where('nr_comisie', $comisieNrIsf->nr_comisie)
                        ->first();

                    if ($solicitareIdIsf && $solicitareIdIsf->id_isf === $item->id_isf) {
                        DB::table('personal')
                            ->where('oameni', $item->oameni)
                            ->update(['id_comisie_ant_scmap' => $solicitareIdIsf->nr_comisie]);
                        break;
                    }
                }
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('personal')
            ->update(['id_comisie_ant_scmap' => 0]);
    }
};
