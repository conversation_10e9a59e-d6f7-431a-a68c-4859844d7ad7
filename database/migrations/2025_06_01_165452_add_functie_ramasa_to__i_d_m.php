<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Valorile pentru tata
        $tata_values = [54];
        
        // Incremental pentru tip_aut si pozitie
        // $tip_aut_value = 356;
        $pozitie_value = 1316;

        // Valorile pentru domeniu
        $domeniu_values = [
            'manipularea instalației de asigurare cu încuietori cu chei și tablouri mecanice cu chei și contacte electrice'
        ];

        // Asigură-te că există suficientă varietate pentru domeniu
        $domeniu_count = count($domeniu_values);

        DB::table('functii')->insert([
            'tata' => 54,
            'ceprint' => '',
            'serie_aut' => '',
            'domeniu' => 'manipularea instalației de asigurare cu încuietori cu chei și tablouri mecanice cu chei și contacte electrice',
            'pozitie' => 1316,
        ]);
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::table('_i_d_m', function (Blueprint $table) {
        //     //
        // });
    }
};
