<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('personal_metrorex', function (Blueprint $table) {
            $table->unsignedBigInteger('nr_permis')->nullable();
        });

        DB::table('personal_metrorex')->update([
            'nr_permis' => DB::raw('id')
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('personal_metrorex', function (Blueprint $table) {
            $table->dropColumn('nr_permis');
        });
    }
};
