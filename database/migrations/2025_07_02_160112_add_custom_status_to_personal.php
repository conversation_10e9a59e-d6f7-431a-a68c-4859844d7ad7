<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update cum_e column based on the conditions
        DB::table('personal')
            ->where('tip_comisie', 'Suspendare')
            ->whereNull('cum_e')
            ->update(['cum_e' => 'Suspendată']);

        DB::table('personal')
            ->where('status', 'AVIZE_GENERATE')
            ->where('tip_comisie', '!=', 'Suspendare')
            ->whereNull('cum_e')
            ->update(['cum_e' => 'Autorizat']);

        DB::table('personal')
            ->where('status', 'NECORESPUNZATOR')
            ->where('tip_comisie', '!=', 'Suspendare')
            ->whereNull('cum_e')
            ->update(['cum_e' => 'Neautorizat']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('personal', function (Blueprint $table) {
            //
        });
    }
};
