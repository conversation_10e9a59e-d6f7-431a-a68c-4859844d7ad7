<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        
        Schema::create('personal_special', function (Blueprint $table) {
            $table->id();
            $table->string('tip_autorizatie');
            $table->string('nr_serie');
            $table->string('nume');
            $table->string('prenume');
            $table->string('functie');
            $table->string('unitate');
            $table->string('ministru');
            $table->date('data_emitere');
            $table->date('data_valabilitate');
            $table->string('poza');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('personal_special');
    }
};
