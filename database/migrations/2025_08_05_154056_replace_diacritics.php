<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('personal')->get()->each(function ($row) {
            $corrected = [
                'autorizatie' => self::replaceDiacritics($row->autorizatie),
                'domeniu' => self::replaceDiacritics($row->domeniu),
                'functie' => self::replaceDiacritics($row->functie),
                // adaugă și alte coloane relevante
            ];
    
            DB::table('personal')->where('oameni', $row->oameni)->update($corrected);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }

    private static function replaceDiacritics($text)
{
    return strtr($text, [
        'ţ' => 'ț',
        'ş' => 'ș',
        'ã' => 'ă',
        'Ţ' => 'Ț',
        'Ş' => 'Ș',
        'Ã' => 'Ă'
        
        // adaugă și alte conversii dacă ai nevoie
    ]);
}
};
