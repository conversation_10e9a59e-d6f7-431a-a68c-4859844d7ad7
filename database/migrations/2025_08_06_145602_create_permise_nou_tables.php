<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permise_active', function (Blueprint $table) {
            $table->id();
            $table->string('id_unitate');
            $table->string('cnp');
            $table->string('nume');
            $table->string('prenume');
            $table->string('status');
            $table->string('tara');
            $table->string('identificator_tara');
            $table->string('an');
            $table->string('numar_permis')->nullable();
            $table->date('data_nastere');
            $table->string('loc_nastere');
            $table->string('nationalitate');
            $table->string('limba_materna');
            $table->string('judet_domiciliu');
            $table->string('fotografie')->nullable();
            $table->string('semnatura')->nullable();
            $table->string('informatii_relevante')->nullable();
            $table->integer('numar_atribuit_angajator'); // este numar identificare de pe permis punctul 4d
            $table->boolean('utilizare_ochelari');
            $table->boolean('utilizare_proteza');
            $table->date('data_emitere')->nullable();
            $table->date('data_angajare')->nullable();
            $table->date('data_reemitere')->nullable();
            $table->date('data_plecare')->nullable();
            $table->string(column: 'motiv_suspendare')->nullable();
            $table->date('data_suspendare')->nullable();
            $table->string(column: 'motiv_retragere')->nullable();
            $table->date('data_retragere')->nullable();
            $table->string('cod')->nullable();
            $table->timestamps();

        });


        Schema::create('permise_istoric', function (Blueprint $table) {
            $table->id();
            $table->string('id_unitate');
            $table->string('cnp');
            $table->string('nume');
            $table->string('prenume');
            $table->string('status');
            $table->string('tara');
            $table->string('identificator_tara');
            $table->string('an');
            $table->string('numar_permis')->nullable();
            $table->date('data_nastere');
            $table->string('loc_nastere');
            $table->string('nationalitate');
            $table->string('limba_materna');
            $table->string('judet_domiciliu');
            $table->string('fotografie')->nullable();
            $table->string('semnatura')->nullable();
            $table->string('informatii_relevante')->nullable();
            $table->integer('numar_atribuit_angajator'); // este numar identificare de pe permis punctul 4d
            $table->boolean('utilizare_ochelari');
            $table->boolean('utilizare_proteza');
            $table->date('data_emitere')->nullable();
            $table->date('data_angajare')->nullable();
            $table->date('data_reemitere')->nullable();
            $table->date('data_plecare')->nullable();
            $table->string(column: 'motiv_suspendare')->nullable();
            $table->date('data_suspendare')->nullable();
            $table->string(column: 'motiv_retragere')->nullable();
            $table->date('data_retragere')->nullable();
            $table->string('cod')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permise_active');
        Schema::dropIfExists('permise_istoric');

    }
};
