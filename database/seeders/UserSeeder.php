<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('users')->truncate();
        DB::table('model_has_roles')->truncate();
        // Create Admin and User roles
        $roles = [
            'super_admin' => Role::firstOrCreate(['name' => 'super_admin']),
            'admin' => Role::firstOrCreate(['name' => 'admin']),
            'user' => Role::firstOrCreate(['name' => 'user']),
        ];

        // Array of users to be created
        $users = [
            [
                'name' => 'Marius Admin',
                'email' => '<EMAIL>',
                'role' => 'super_admin',
                'isf' => '0'
            ],
            // CRAIOVA
            [
                'name' => '<PERSON><PERSON><PERSON><PERSON> <PERSON>',
                'email' => 'ion.<PERSON><PERSON><PERSON>@sigurantaferoviara.ro',
                'role' => 'user',
                'isf' => '2022'

            ],
            [
                'name' => '<PERSON><PERSON><PERSON><PERSON><PERSON>ână Florentin',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2022'

            ],
            [
                'name' => 'Gavrilă Puiu',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2022'

            ],
            [
                'name' => 'Crînguș Ionel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2022'
            ],
            [
                'name' => 'Frăsinoi Mihai',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2022'

            ],
            [
                'name' => 'Firică Aura',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2022'
            ],
            [
                'name' => 'Coman Anica',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2022'
            ],
            [
                'name' => 'Popescu Adrian',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2022'
            ],
            [
                'name' => 'Grecu Sorin',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2022'
            ],
            [
                'name' => 'Costache Sorin',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2022'
            ],
            // BRASOV
            [
                'name' => 'Pănoiu Ilie',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2041'
            ],
            [
                'name' => 'Bugheanu Viorel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2041'
            ],
            [
                'name' => 'Călugăru Dan',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2041'
            ],
            [
                'name' => 'Cernat Laurian',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2041'
            ],
            [
                'name' => 'Cojocaru Armand Daniel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2041'
            ],
            [
                'name' => 'Enache Marian',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2041'
            ],
            [
                'name' => 'Muntean Ioan Adrian',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2041'
            ],
            [
                'name' => 'Enache Marian',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2041'
            ],
            // IASI
            [
                'name' => 'Zisu Gabriel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2050'
            ],
            [
                'name' => 'Bîrlădeanu Romica',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2050'
            ],
            [
                'name' => 'Topala Daniel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2050'
            ],
            [
                'name' => 'Pahomi Sorin',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2050'
            ],
            [
                'name' => 'Lupu Victor',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2050'
            ],
            [
                'name' => 'Gheorghita Olariu',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2050'
            ],
            [
                'name' => 'Alexandru Constantin',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2050'
            ],
            [
                'name' => 'Ciocan Sorin',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2050'
            ],
            // CLUJ
            [
                'name' => 'Mircea Florin Biban',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2040'
            ],
            [
                'name' => 'Costea Viorel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2040'
            ],
            [
                'name' => 'Măhălea Matei Gigel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2040'
            ],
            [
                'name' => 'Donea Cristian Ioan',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2040'
            ],
            [
                'name' => 'Mureșan Stelian Viorel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2040'
            ],
            [
                'name' => 'Sîrbu Norin Gabriel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2040'
            ],
            [
                'name' => 'Danciu Aurel Bogdan',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2040'
            ],
            [
                'name' => 'Nicoară Emil',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2040'
            ],
            [
                'name' => 'Vlașin Daniel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2040'
            ],
            //CONSTANTA
            [
                'name' => 'Cihodaru Daniel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2060'
            ],
            [
                'name' => 'Mustafa Tekin',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2060'
            ],
            [
                'name' => 'Laurentiu Toma',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2060'
            ],
            [
                'name' => 'Voiculescu Dan',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2060'
            ],
            [
                'name' => 'Mitescu Georgete',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2060'
            ],
            [
                'name' => 'Dragomir Roxana',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2060'
            ],
            [
                'name' => 'Salahoru Jan',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2060'
            ],
            [
                'name' => 'Serban Marius',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2060'
            ],
            [
                'name' => 'Mihai Daniel Vicențiu',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2060'
            ],
            // GALATI
            [
                'name' => 'Florea Cristinel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2051'
            ],
            [
                'name' => 'Mușat Emanuel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2051'
            ],
            [
                'name' => 'Lăpăduș Danut',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2051'
            ],
            [
                'name' => 'Barbu Nicolae',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2051'
            ],
            [
                'name' => 'Filimon Ovidiu',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2051'
            ],
            [
                'name' => 'Ionașcu Gabriel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2051'
            ],
            [
                'name' => 'Hahui Mihaela',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2051'
            ],
            [
                'name' => 'Andrei Lucian Valeriu',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2051'
            ],
            // TIMISOARA
            [
                'name' => 'Radosav Danut Ioan',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Bobîlcă Marius',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Lupulescu Moise Fănel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Marc Ioan Marian',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Marinescu Liviu',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Moța Nicolae Dan',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Oltenacu Andrei',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Oprea Eugen',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Pui Ovidiu',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Trica Nicolae Mihai',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Nicolae Vasut Silviu',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Nicolae Venter',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            [
                'name' => 'Ișfănescu Otilia',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2030'
            ],
            // BUCURESTI
            // [
            //     'name' => 'Cires Cristian',
            //     'email' => '<EMAIL>',
            //     'role' => 'super_admin',
            //     'isf' => '2021'
            // ],
            [
                'name' => 'Daniel Bulearca',
                'email' => '<EMAIL>',
                'role' => 'super_admin',
                'isf' => '2021'
            ],
            [
                'name' => 'Ionela Gigirtu',
                'email' => '<EMAIL>',
                'role' => 'super_admin',
                'isf' => '2021'
            ],
            [
                'name' => 'Dragos Balint',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'isf' => '2021'
            ],
            [
                'name' => 'Mihai Cristian Rosu',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2021'
            ],
            [
                'name' => 'Apostol Iulian',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2021'
            ],
            // [
            //     'name' => 'Calin Sorin',
            //     'email' => '<EMAIL>',
            //     'role' => 'user',
            //     'isf' => '2021'
            // ],
            [
                'name' => 'Trif Gabriel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2021'
            ],
            [
                'name' => 'Burtescu Marian',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2021'
            ],
            [
                'name' => 'Gherghe Florian',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2021'
            ],
            [
                'name' => 'Ghițu Vasile',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2021'
            ],
            [
                'name' => 'Vlad Viorel',
                'email' => '<EMAIL>',
                'role' => 'user',
                'isf' => '2021'
            ],

            // Add more users here...
        ];

        // Loop through each user and create them
        foreach ($users as $userData) {
            $firstName = explode(' ', $userData['name'])[1]; // presupunând că primul nume e al doilea cuvânt din nume

            // Creează parola în formatul "Nume.1!"
            $password = $firstName . '.1!';
            $user = User::firstOrCreate([
                'email' => $userData['email'],
            ], [
                'name' => $userData['name'],
                'password' => Hash::make($password),
                'isf' => $userData['isf'],
            ]);

            // Assign the role to the user
            $user->assignRole($roles[$userData['role']]);
        }
    }
}
