@props(['solicitare', 'comisie', 'isf'])

<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document de duplicate</title>
    <style>
        body {
            font-family: DejaVu Sans, sans-serif;
            font-size: 10px;
        }

        .bold {
            font-weight: bold;
        }

        .container {
            margin: 20px;
        }

        .header,
        .footer {
            text-align: center;
            margin-bottom: 10px;
        }

        .content {
            margin-top: 10px;
        }

        p {
            margin: 5px 0;
            font-size: 12px;
        }

        .header p {
            font-size: 14px;
        }

        .img-fit {
            width: 100%;
            height: 100px;
            max-width: 100%;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* Align items vertically center */
        }

        .header-left,
        .header-right {
            width: 48%;
            /* Adjust width as necessary */
        }

        .table-container {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }

        .table-container th,
        .table-container td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }

        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .table-title {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }
        td {
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }

        .table-container2 {
            width: 100%; /* Latimea unei pagini A4 */
            margin: 0 auto; /* Centreaza containerul pe pagina */
            padding: 8px; /* Adaugă puțin spațiu între container și marginea paginii */
            box-sizing: border-box;
        }
        td.first-column {
            width: 120px;
            height: 20px;
        }
        .tr-height {
            height: 2px;
        }
        .line-color {
            width: 100%;
            height: 0.5mm;
            margin-bottom: 1mm;
            margin-top: 1mm;
            background-color: black;
        }
        .isf-coordonator{
            text-align: center;
            margin-bottom: 10px;

        }

        .aligned-container{
            margin: 0 auto; /* Center the container horizontally */
            padding: 10px; /* Optional padding inside the container */
            box-sizing: border-box; /* Ensure padding is included in the width/height */
            margin-top: 0; /* Remove any margin from the top */
            margin-bottom: 0; /* Remove any margin from the bottom */
            padding-top: 0; /* Optional: Remove padding from the top */
            padding-bottom: 0; /* Optional: Remove padding from the bottom */
        }

        .styled-paragraph {
            display: block;
            text-align: justify;
            margin:0;
            font-size: 12px;
        }


    </style>
</head>


@php

    $dataISF = $comisie->data_ISF;
    $date1 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataISF);
    $formattedDataISF= $date1->format('d/m/Y');

    $dataCerereUF = $comisie->cerere_uf_data;
    $date2 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataCerereUF);
    $formattedDataCerereUF = $date2->format('d/m/Y');

    if ($solicitariAnterioare){
        foreach ($solicitariAnterioare as $index => $solicitareAnterioara){
            $dataEliberare = $solicitareAnterioara->data_elib;
            $date3 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataEliberare);
            $formattedDataEliberare = $date3->format('d/m/Y');
        
            $dataValabilitate = $solicitareAnterioara->valabilitate;
            $date4 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataValabilitate);
            $formattedDataValabilitate = $date4->format('d/m/Y');
        }
    }

    

    $cenafer_actual = '';

    if ($comisie->CENAFER === '1') {
        $cenafer_actual = 'bucuresti';
    } elseif ($comisie->CENAFER === '2') {
        $cenafer_actual = 'brasov';
    } elseif ($comisie->CENAFER === '3') {
        $cenafer_actual = 'constanta';
    } elseif ($comisie->CENAFER === '4') {
        $cenafer_actual = 'cluj';
    } elseif ($comisie->CENAFER === '5') {
        $cenafer_actual = 'craiova';
    } elseif ($comisie->CENAFER === '6') {
        $cenafer_actual = 'galati';
    } elseif ($comisie->CENAFER === '7') {
        $cenafer_actual = 'iasi';
    } elseif ($comisie->CENAFER === '8') {
        $cenafer_actual = 'timisoara';
    }
    
@endphp

<body>
    <header>
        <div class="table-container2">
            <table>
                <tr>
                    <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                    <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
                </tr>
                <tr>
                    <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
                </tr>
                <tr>
                    <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
                </tr>
                <tr>
                    <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
                </tr>
                <tr>
                    <td class="tr-height ">E-mail:  isf.{{strtr(strtolower($isf->ISF), 
                        ['ă'=>'a', 'â'=>'a', 'ș'=>'s', 'ț'=>'t', 'î'=>'i', 'Ă'=>'A', 'Â'=>'A', 'Ș'=>'S', 'Ț'=>'T', 'Î'=>'I'])}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
                </tr>
            </table>
            <div class="line-color"></div>
        </div>
    </header>
    <div class="container">
        <div>
            <p class="bold">
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitare->id_isf === 1  || $solicitare->id_isf === 2 || $solicitare->id_isf === 3 || $solicitare->id_isf === 7)
                    Compartimentul Inspectorat de Siguranță Feroviară
                @else Inspectoratul de Siguranță Feroviară
                @endif
                {{$isf->ISF}}
            </p> <!-- space-between -->
            <p>Nr. {{ $isf->cod_serviciu }}/<span class="bold">{{ $comisie->nr_ISF }}</span> din <span
                    class="bold">{{ $formattedDataISF }}</span> - <span class="bold">C{{$comisie->nr_comisie}}</span>
            </p>
        </div>
        <div class="aligned-container">
            <p style="margin: 0;">Către: <span class="bold">{{ $solicitare->solicitant_nume }}
                {{ $solicitare->solicitant_prenume }}</span></p>  
            <p></p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Referitor
                la: Emiterea de <span class="bold">DUPLICATE</span> ale autorizațiilor deținute de personalul cu
                responsabilități în
                siguranța
                circulației care desfășoară, pe proprie răspundere, activități specifice transportului
                feroviar,
                solicitată de
                persoana fizică <span class="bold">{{ $solicitare->solicitant_nume }}
                    {{ $solicitare->solicitant_prenume }}</span>, CNP
                <span class="bold">{{ $solicitare->cnp }}</span>, cu cartea de identitate seria <span
                    class="bold">{{ $solicitare->serie_ci }}</span>, nr. <span
                    class="bold">{{ $solicitare->nr_ci }}</span>, cu domiciliul în <span class="bold">
                    @if (!empty($solicitare->adresa_om) && !empty($solicitare->localitate_om))
                        {{ $solicitare->adresa_om }}, {{ $solicitare->localitate_om }}
                    @endif
                </span>, prin actul
                cu numărul <span class="bold">{{ $comisie->cerere_uf_nr }}</span> din
                <span class="bold">{{ $formattedDataCerereUF }}</span>.
            </p>
            <p style="text-align: justify;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Vă
                comunicăm că, în conformitate cu prevederile OMTCT nr. 2262/2005 cu modificările și
                completările ulterioare, ASFR aprobă
                emiterea de duplicate ale autorizațiilor precizate în tabelul anexat.
            </p>
            <p style="text-align: justify;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tariful pentru emiterea duplicatelor autorizațiilor menționate, stabilit conform prevederilor OMT nr. 1471
                din 07.08.2023, cu modificările și completările ulterioare, este de <span class="bold">{{ $tarifCalculat }} euro</span>
                la care NU se adaugă TVA, cf art. 269, al. 5 din 227/2015-codul fiscal şi se achită în lei, la
                cursul valutar al BNR valabil în data efectuării plăţii, în contul IBAN ASFR
                <span class="bold">************************, CIF: 48008564</span>, deschis la Trezoreria Operativă
                a
                sectorului 1,
                București, prin ordin de plată pe care se va menționa numărul și data prezentului act.</p>
            <p style="text-align: justify;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tariful se va achita în maxim 5 zile lucrătoare de la data primirii prezentului act.</p>
            <p style="text-align: justify;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Autorizațiile se vor ridica de la sediul AFER de către delegatul ISF și vor fi înmânate delegatului solicitantului, la sediul ISF, pe baza documentului de plată a sumei precizate mai sus.</p>
        </div>
        <div class="isf-coordonator">
            <p></p>
            <p>
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitare->id_isf === 1  || $solicitare->id_isf === 2 || $solicitare->id_isf === 3 || $solicitare->id_isf === 7)
                    <span class="bold">Coordonator CISF</span>
                @else
                    <span class="bold">Inspector Șef Teritorial</span>
                @endif
            </p>
            <p><span class="bold">{{ $isf->sef_ISF }}</span></p>
        </div>
    </div>
    <div class="table-title">Autorizații pentru care vor fi emise DUPLICATE</div>
    <table class="table-container">
        <thead>
            <tr>
                <th>Nr.</th>
                <th>Nume, prenume</th>
                <th>CNP</th>
                <th>Număr</th>
                <th>Data emiterii</th>
                <th>Valabilitate</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td colspan='2'>Funcție</td>
                <td colspan='4'>Tip Autorizație</td>
            </tr>
            <tr>
                <td colspan='6'>Domeniu</td>
            </tr>
        </tbody>
    </table>
    <br>
    @if ($solicitariAnterioare)
        @foreach ($solicitariAnterioare as $index => $solicitareAnterioara)
            <table class="table-container">
                <thead>
                    <tr>
                        <th>{{ $index + 1 }}</th>
                        <th>{{ $solicitareAnterioara->solicitant_nume }}
                            {{ $solicitareAnterioara->solicitant_prenume }}
                        </th>
                        <th>{{ $solicitareAnterioara->cnp }}</th>
                        <th>{{ $solicitareAnterioara->nr_aut }}</th>
                        <th>{{ \Carbon\Carbon::createFromFormat('Y-m-d', $solicitareAnterioara->data_elib)->format('d/m/Y') }}</th>
                        <th>{{ \Carbon\Carbon::createFromFormat('Y-m-d', $solicitareAnterioara->valabilitate)->format('d/m/Y') }}</th>
                        
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan='2'>{{ $solicitareAnterioara->functie }}</td>
                        <td colspan='4'>{{ $solicitareAnterioara->autorizatie }}</td>
                    </tr>
                    <tr>
                        @if (isset($solicitareAnterioara['domeniu']))
                            <td colspan='6'>{{ $solicitareAnterioara->domeniu }}</td>
                        @else
                            <td colspan='6'></td>
                        @endif
                    </tr>
                </tbody>
            </table>
        @endforeach
    @endif
    <p style="text-align: center">Întocmit de</p>
    <p style="text-align: center; font-weight: bold">{{ $comisie->redactat }}</p>
</body>

</html>
