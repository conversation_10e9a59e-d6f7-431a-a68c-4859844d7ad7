@props(['solicitare', 'comisie', 'isf'])

<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document de retragere autorizație</title>
    <style>
        body {
            font-family: DejaVu Sans, sans-serif;
            font-size: 11px;
        }

        .bold {
            font-weight: bold;
        }

        .container {
            margin: 20px;
        }

        .header,
        .footer {
            text-align: center;
            margin-bottom: 10px;
        }
        .table-container {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }

        .table-container th,
        .table-container td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }

        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .content {
            margin-top: 10px;
        }

        p {
            margin: 5px 0;
            font-size: 12px;
        }

        .header p {
            font-size: 14px;
        }

        .img-fit {
            width: 100%;
            height: 100px;
            max-width: 100%;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* Align items vertically center */
        }

        .header-left,
        .header-right {
            width: 48%;
            /* Adjust width as necessary */
        }
        .aligned-container{
            margin: 0 auto; /* Center the container horizontally */
            padding: 10px; /* Optional padding inside the container */
            box-sizing: border-box; /* Ensure padding is included in the width/height */
            margin-top: 0; /* Remove any margin from the top */
            margin-bottom: 0; /* Remove any margin from the bottom */
            padding-top: 0; /* Optional: Remove padding from the top */
            padding-bottom: 0; /* Optional: Remove padding from the bottom */
        }

        .styled-paragraph {
            display: block;
            text-align: justify;
            margin:0;
            font-size: 12px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }
        td {
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }

        .table-container2 {
            width: 100%; /* Latimea unei pagini A4 */
            margin: 0 auto; /* Centreaza containerul pe pagina */
            padding: 8px; /* Adaugă puțin spațiu între container și marginea paginii */
            box-sizing: border-box;
        }
        td.first-column {
            width: 120px;
            height: 20px;
        }
        .tr-height {
            height: 2px;
        }
        .line-color {
            width: 100%;
            height: 0.5mm;
            margin-bottom: 1mm;
            margin-top: 1mm;
            background-color: black;
        }
        .isf-coordonator{
            text-align: center;
            margin-bottom: 10px;

        }
    </style>
</head>

@php

    if ($solicitariAnterioare){
        foreach ($solicitariAnterioare as $index => $solicitareAnterioara){
            $dataValabilitate = $solicitareAnterioara->valabilitate;
            $date4 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataValabilitate);
            $formattedDataValabilitate = $date4->format('d/m/Y');
        }
    }
    //format data EXAMEN TEORETIC
    $dataExamenT1 = $comisie->data_exam_t1;
    $date1 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataExamenT1);
    $formattedDataExamenT1 = $date1->format('d/m/Y');
    //format data EXAMEN PRACTIC
    $dataExamenP1 = $comisie->data_exam_p1;
    $date2 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataExamenP1);
    $formattedDataExamenP1 = $date2->format('d/m/Y');
    //format data COMISIE ISF
    $dataCerereUF = $comisie->cerere_uf_data;
    $date3 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataCerereUF);
    $formattedDataCerereUF = $date3->format('d/m/Y');
    //format data ISF
    $originalDate = $comisie->data_ISF; // Data din variabila ta, format "aaaa-ll-zz"
    $date = \Carbon\Carbon::createFromFormat('Y-m-d', $originalDate);
    $formattedDate = $date->format('d/m/Y'); // Formatează data în "zz/ll/aaaa"
    $cenafer_actual = '';

    if ($comisie->CENAFER === '1') {
        $cenafer_actual = 'bucuresti';
    } elseif ($comisie->CENAFER === '2') {
        $cenafer_actual = 'brasov';
    } elseif ($comisie->CENAFER === '3') {
        $cenafer_actual = 'constanta';
    } elseif ($comisie->CENAFER === '4') {
        $cenafer_actual = 'cluj';
    } elseif ($comisie->CENAFER === '5') {
        $cenafer_actual = 'craiova';
    } elseif ($comisie->CENAFER === '6') {
        $cenafer_actual = 'galati';
    } elseif ($comisie->CENAFER === '7') {
        $cenafer_actual = 'iasi';
    } elseif ($comisie->CENAFER === '8') {
        $cenafer_actual = 'timisoara';
    }
    
@endphp
<body>
    <header>
        <div class="table-container2">
            <table>
                <tr>
                    <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                    <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
                </tr>
                <tr>
                    <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
                </tr>
                <tr>
                    <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
                </tr>
                <tr>
                    <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
                </tr>
                <tr>
                    <td class="tr-height ">E-mail:  isf.{{strtr(strtolower($isf->ISF), 
                        ['ă'=>'a', 'â'=>'a', 'ș'=>'s', 'ț'=>'t', 'î'=>'i', 'Ă'=>'A', 'Â'=>'A', 'Ș'=>'S', 'Ț'=>'T', 'Î'=>'I'])}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
                </tr>
            </table>
            <div class="line-color"></div>
        </div>
    </header>
    <div class="container">
        <div>
            <p class="bold">
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitare->id_isf === 1  || $solicitare->id_isf === 2 || $solicitare->id_isf === 3 || $solicitare->id_isf === 7)
                    Compartimentul Inspectorat de Siguranță Feroviară
                @else Inspectoratul de Siguranță Feroviară
                @endif
                {{$isf->ISF}}
            </p> <!-- space-between -->
            <p>Nr. {{ $isf->cod_serviciu }}/{{ $comisie->nr_ISF }}</> din {{ $formattedDate }}</span> - <span class="bold">C{{$comisie->nr_comisie}}</span></p>
                    
            <p><span style="display: block; text-align: right; margin: 0; margin-right: 60px; font-weight: bold;">APROBAT,</span></p>
            <p><span style="display: block; text-align: right; margin: 0; margin-right: 40px">Director General,</span></p>
            <p><span style="display: block; text-align: right; margin: 0; margin-right: 50px">{{ $solicitare->directorASFR }}</span></p>
        </div>
        <div class="aligned-container">
            <p style="margin: 0;">Către: <span class="bold">{{ $solicitare->solicitant_nume }}
                {{ $solicitare->solicitant_prenume }}</span></p>            
            <p></p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Referitor
                la: <span class="bold">SUSPENDAREA</span> autorizațiilor/permiselor deținute de personalul cu
                responsabilități în
                siguranța
                circulației care desfășoară, pe proprie răspundere, activități specifice transportului
                feroviar,
                solicitată de
                persoana fizică <span class="bold">{{ $solicitare->solicitant_nume }}
                    {{ $solicitare->solicitant_prenume }}</span>, CNP
                <span class="bold">{{ $solicitare->cnp }}</span>, cu cartea de identitate seria <span
                    class="bold">{{ $solicitare->serie_ci }}</span>, nr. <span
                    class="bold">{{ $solicitare->nr_ci }}</span>, cu domiciliul în <span class="bold">
                    @if (!empty($solicitare->adresa_om) && !empty($solicitare->localitate_om))
                        {{ $solicitare->adresa_om }}, {{ $solicitare->localitate_om }}
                    @endif
                </span>, prin actul
                cu numărul <span class="bold">{{ $comisie->cerere_uf_nr }}</span> din
                <span class="bold">{{ $formattedDataCerereUF }}</span>.
            </p>
        </div>
        <div>
                
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Vă
                comunicăm că, în conformitate cu prevederile art. nr. 9 alin. (3) din Anexa nr. 1 la OMTCT nr.
                2262/2005, art. 28, alin. (1) din Anexa nr. 1 la OMT nr. 615/2015 și art. 42 din Anexa nr. 2 la OMT nr.
                615/2015, se dispune retragerea autorizațiilor/permiselor de conducere precizate în tabelul anexat.</p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Autorizațiile/permisele
                de conducere menționate își încetează definitiv valabilitatea începând cu data de <span
                    class="bold">{{ $formattedDate }}</span>.</p>
        </div>
        <div class="isf-coordonator">
            <p></p>
            <p>
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitare->id_isf === 1  || $solicitare->id_isf === 2 || $solicitare->id_isf === 3 || $solicitare->id_isf === 7)
                        <span class="bold">Coordonator CISF</span>
                    @else
                        <span class="bold">Inspector Șef Teritorial</span>
                    @endif
            </p>
            <p><span class="bold">{{ $isf->sef_ISF }}</span></p>
        </div>
    </div>
    <div class="table-title">Autorizații RETRASE începând cu data de {{ $formattedDate }}</div>
    <table class="table-container">
        <thead>
            <tr>
                <th>Nr.</th>
                <th>Nume, prenume</th>
                <th>CNP</th>
                <th>Număr</th>
                <th>Valabilitate</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td colspan='2'>Funcție</td>
                <td colspan='3'>Tip Autorizație</td>
            </tr>
            <tr>
                <td colspan='5'>Domeniu</td>
            </tr>
            <tr>
                <td colspan='5'>Conformitatea cu normele în vigoare</td>
            </tr>
            <tr>
                <td colspan='5'>Cauza retragerii</td>
            </tr>
        </tbody>
    </table>
    <br>
    @if ($solicitariAnterioare)
        @foreach ($solicitariAnterioare as $index => $solicitareAnterioara)
            <table class="table-container">
                <thead>
                    <tr>
                        <th>{{ $index + 1 }}</th>
                        <th>{{ $solicitareAnterioara->solicitant_nume }}
                            {{ $solicitareAnterioara->solicitant_prenume }}
                        </th>
                        <th>{{ $solicitareAnterioara->cnp }}</th>
                        <th>{{ $solicitareAnterioara->nr_aut }}</th>
                        <th>{{ $formattedDataValabilitate }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan='2'>{{ $solicitareAnterioara->functie }}</td>
                        <td colspan='3'>{{ $solicitareAnterioara->autorizatie }}</td>
                    </tr>
                    <tr>
                        @if (isset($solicitareAnterioara['domeniu']))
                            <td colspan='5'>{{ $solicitareAnterioara->domeniu }}</td>
                        @else
                            <td colspan='5'></td>
                        @endif
                    </tr>
                    <tr>
                        <td colspan='5'>Anexa nr. 1 al OMTCT nr. 2262/2005, art.8 alin.(2), lit. DECEDAT</td>
                    </tr>
                    <tr>
                        <td colspan='5'>DECEDAT</td>
                    </tr>
                </tbody>
            </table>
        @endforeach
    @endif
</body>

</html>
