@props(['solicitare', 'comisie', 'isf', 'cenaferuri'])

<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document de autorizare</title>
    <style>

        @page {
                size: A4;
                margin: 15mm;
                page-break-after: always;
                @top-center {
                    content: element(header);
                }
        }
        body {
            font-family: DejaVu Sans, sans-serif;
            font-size: 10px;
        }

        header {
            position: running(header);
        }

        .bold {
            font-weight: bold;
        }

        .header{}
        .footer:after {
            content: "pag." counter(page);
        }
        .footer{
            position: fixed;
            bottom: 0;
            width: 100%;
            display: flex; /* Folosește flexbox */
            justify-content: flex-end; /* Alină la dreapta */
            padding-right: 10px; /* Opțional: adaugă un spațiu la dreapta */
        }

        .content {
            margin-top: 10px;
        }

        p {
            margin: 5px 0;
            font-size: 12px;
        }

        .header p {
            font-size: 14px;
        }

        .img-fit {
            width: 100%;
            height: 100px;
            max-width: 100%;
        }

        .header-left,
        .header-right {
            width: 48%;
            /* Adjust width as necessary */
        }

        .aligned-container{
            margin: 0 auto; /* Center the container horizontally */
            padding: 10px; /* Optional padding inside the container */
            box-sizing: border-box; /* Ensure padding is included in the width/height */
            margin-top: 0; /* Remove any margin from the top */
            margin-bottom: 0; /* Remove any margin from the bottom */
            padding-top: 0; /* Optional: Remove padding from the top */
            padding-bottom: 0; /* Optional: Remove padding from the bottom */
        }

        .styled-paragraph {
            display: block;
            text-align: justify;
            margin:0;
            font-size: 12px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }
        td {
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }

        .table-container2 {
            width: 100%; /* Latimea unei pagini A4 */
            margin: 0 auto; /* Centreaza containerul pe pagina */
            padding: 8px; /* Adaugă puțin spațiu între container și marginea paginii */
            box-sizing: border-box;
        }
        td.first-column {
            width: 120px;
            height: 20px;
        }
        .tr-height {
            height: 2px;
        }
        .line-color {
            width: 100%;
            height: 0.5mm;
            margin-bottom: 1mm;
            margin-top: 1mm;
            background-color: black;
        }
        .isf-coordonator{
            text-align: center;
            margin-bottom: 10px;

        }

    </style>
</head>

@php
    //format data EXAMEN TEORETIC
    $dataExamenT1 = $comisie->data_exam_t1;
    $date1 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataExamenT1);
    $formattedDataExamenT1 = $date1->format('d/m/Y');
    //format data EXAMEN PRACTIC
    $dataExamenP1 = $comisie->data_exam_p1;
    $date2 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataExamenP1);
    $formattedDataExamenP1 = $date2->format('d/m/Y');
    //format data COMISIE ISF
    $dataCerereUF = $comisie->cerere_uf_data;
    $date3 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataCerereUF);
    $formattedDataCerereUF = $date3->format('d/m/Y');
    //format data ISF
    $originalDate = $comisie->data_ISF; // Data din variabila ta, format "aaaa-ll-zz"
    $date = \Carbon\Carbon::createFromFormat('Y-m-d', $originalDate);
    $formattedDate = $date->format('d/m/Y'); // Formatează data în "zz/ll/aaaa"
    $cenafer_actual = '';

    if ($comisie->CENAFER === '1') {
        $cenafer_actual = 'bucuresti';
    } elseif ($comisie->CENAFER === '2') {
        $cenafer_actual = 'brasov';
    } elseif ($comisie->CENAFER === '3') {
        $cenafer_actual = 'constanta';
    } elseif ($comisie->CENAFER === '4') {
        $cenafer_actual = 'cluj';
    } elseif ($comisie->CENAFER === '5') {
        $cenafer_actual = 'craiova';
    } elseif ($comisie->CENAFER === '6') {
        $cenafer_actual = 'galati';
    } elseif ($comisie->CENAFER === '7') {
        $cenafer_actual = 'iasi';
    } elseif ($comisie->CENAFER === '8') {
        $cenafer_actual = 'timisoara';
    }
    
@endphp

<body>
    <header>
        <div class="table-container2">
            <table>
                <tr>
                    <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                    <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
                </tr>
                <tr>
                    <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
                </tr>
                <tr>
                    <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
                </tr>
                <tr>
                    <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
                </tr>
                <tr>
                    <td class="tr-height ">E-mail:  isf.{{$cenafer_actual}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
                </tr>
            </table>
            <div class="line-color"></div>
        </div>
    </header>
    <div>
        <div>
            <p class="bold">Inspectoratul de Siguranță Feroviară
                @if ($comisie->CENAFER === '1')
                    București
                @elseif ($comisie->CENAFER === '2')
                    Brașov
                @elseif ($comisie->CENAFER === '3')
                    Constanța
            
                @elseif ($comisie->CENAFER === '4')
                    Cluj
        
                @elseif ($comisie->CENAFER === '5')
                    Craiova
        
                @elseif ($comisie->CENAFER === '6')
                    Galați
        
                @elseif ($comisie->CENAFER === '7')
                    Iași
        
                @elseif ($comisie->CENAFER === '8')
                    Timișoara
                                    
                @else 

                @endif
            </p> <!-- space-between -->
            <p>Nr. {{ $isf->cod_serviciu }}/{{ $comisie->nr_ISF }}</> din {{ $formattedDate }}</span></p>
                    
            <p><span style="display: block; text-align: right; margin: 0; margin-right: 60px; font-weight: bold;">APROBAT,</span></p>
            <p><span style="display: block; text-align: right; margin: 0; margin-right: 40px">Director General,</span></p>
            <p><span style="display: block; text-align: right; margin: 0; margin-right: 50px">{{ $solicitare->directorASFR }}</span></p>
        </div>
        <div class="aligned-container">
            <p style="margin: 0;">Către: <span class="bold">{{ $solicitare->solicitant_nume }}
                {{ $solicitare->solicitant_prenume }}</span></p>            
            <p  style="margin-top: 2px;">Spre știință: <span class="bold">CENAFER</span></p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Referitor la: <span class="bold">{{ $solicitare->tip_comisie }}a</span> personalului cu responsabilități în siguranța circulației care urmează să
                desfășoare, pe proprie răspundere, activități specifice transportului feroviar, la propunerea
                CENAFER - Centrul Teritorial {{ $comisie->cenafer }} prin actul nr. 
                    <span class="bold">
                    {{ $comisie->cerere_uf_nr }}</span></span> din <span
                    class="bold">{{ $formattedDataCerereUF }}</span> și ca urmare a
                solicitării
                persoanei
                 fizice <span class="bold">{{ $solicitare->solicitant_nume }}
                    {{ $solicitare->solicitant_prenume }}</span>, CNP
                <span class="bold">{{ $solicitare->cnp }}</span>, cu cartea de identitate</span> seria <span
                    class="bold">{{ $solicitare->serie_ci }}, nr. <span
                    class="bold">{{ $solicitare->nr_ci }}</span>, cu domiciliul în <span class="bold">
                    @if (!empty($solicitare->adresa_om) && !empty($solicitare->localitate_om))
                        {{ $solicitare->adresa_om }}, {{ $solicitare->localitate_om }}
                    @endif
                    , prin actul
                cu numărul <span class="bold">{{ $comisie->cerere_uf_nr }} </span>din 
                <span class="bold">{{ $formattedDataCerereUF }}</span></span>.
            </p>
        </div>
        <div>  
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Vă
                comunicăm că, în conformitate cu prevederile OMTCT nr. 2262/2005 cu modificările și
                completările ulterioare, ASFR stabilește
                comisia de examinare în vederea autorizării pentru funcția: 
                <p><span class="bold">{{ $solicitare->functie }}</span></p>
                @if (!empty($solicitare->autorizatie))
                    <span class="bold">{{ $solicitare->autorizatie }}</span>
                @endif
            </p>
            <div>
                <p class="styled-paragraph">în următoarea componență:</p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Președinte: <span class="bold">{{ $solicitare->nume }} {{ $solicitare->prenume }}</p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Membri:&nbsp;&nbsp;&nbsp;&nbsp;
                    @if (!empty($comisie->nume_cenafer1) && !empty($comisie->prenume_cenafer1))
                        <span class="bold">{{ $comisie->nume_cenafer1 }}
                            {{ $comisie->prenume_cenafer1 }}</span> din partea CENAFER.
                        {{-- {{ $comisie->serie_cenafer1 }}, nr. {{ $comisie->numar_cenafer1 }}</br> --}}
                    @endif </br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <span style="margin-left: 55px;">@if (!empty($comisie->nume_cenafer2) && !empty($comisie->prenume_cenafer2))
                                <span class="bold">{{ $comisie->nume_cenafer2 }}
                                    {{ $comisie->prenume_cenafer2 }}</span> din partea CENAFER.
                            @endif
                    </span></p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Examinarea <span class="bold">
                    @if ($comisie->urgent === 'Da')
                        <span class="bold">SE</span>
                    @else
                        <span class="bold">NU SE</span>
                    @endif
                </span> va efectua în regim de urgență</p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Examinarea teoretică se va desfășura la <span class="bold">{{ $comisie->loc_exam_t }}</span>, în
                    data de <span class="bold">{{ $formattedDataExamenT1 }}</span>, cu începere de la ora <span
                        class="bold">{{ $comisie->ora_exam_t1 }}.</span></p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Examinarea practică se va desfășura la <span class="bold">{{ $comisie->loc_exam_p }}</span>, în
                    data de <span class="bold">{{ $formattedDataExamenP1 }}</span>, cu începere de la ora <span
                        class="bold">{{ $comisie->ora_exam_p1 }}.</span></p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tariful pentru examinarea persoanei precizate mai sus, stabilit conform prevederilor OMT nr. 1471
                    din 07.08.2023, cu modificările și completările ulterioare, coroborate cu prevederile art. 1, alin.
                    (6) din OG nr. 14/2023, este de <span class="bold">{{ $tarifCalculat }} euro</span>
                    la care NU se adaugă TVA, cf art. 269, al. 5 din 227/2015-codul fiscal şi se achită în lei, la cursul valutar al BNR valabil în data efectuării plăţii, în contul IBAN ASFR 
                    <span class="bold">************************, CIF: 48008564</span>, deschis la Trezoreria Operativă a
                    sectorului 1,
                    București, prin ordin de plată pe care se va menționa numărul și data prezentului act.</p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tariful se va achita în maxim 5 zile lucrătoare de la data primirii facturii, fără a se depăși data desfășurării examinării teoretice.</p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Susținerea examenului va fi permisă candidaților după prezentarea unui act de identitate ( CI, pașaport) şi a dovezii achitării tarifului.</p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Bibliografia şi tematica de examinare sunt cele aprobate prin decizia nr. 64/2024 a Directorului General al Autorității de Siguranță Feroviară -ASFR.</p>
            </div>
        </div>
        <div class="isf-coordonator">
            <p></p>
            <p>
                @if ($comisie->CENAFER === 'București' || $comisie->CENAFER === 'Brașov'  || $comisie->CENAFER === '1'  || $comisie->CENAFER === '2' )
                        <span class="bold">Coordonator CISF</span>
                    @else
                        <span class="bold">Inspector Șef Teritorial</span>
                    @endif
            </p>
            <p><span class="bold">{{ $isf->sef_ISF }}</span></p>
        </div>
    </div>
    <footer class="footer">
        <p style="display: block; text-align: right;">act Nr. {{ $isf->cod_serviciu }}/<span class="bold">{{ $comisie->nr_ISF }}</span> din <span class="bold">{{ $formattedDate }}</span></p>
    </footer>
</body>

</html>
