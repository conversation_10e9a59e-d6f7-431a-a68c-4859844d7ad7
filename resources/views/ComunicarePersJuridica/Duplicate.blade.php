@props(['solicitari', 'comisie', 'isf'])

<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document de duplicate</title>
    <style>
        body {
            font-family: Deja<PERSON><PERSON>, sans-serif;
            font-size: 11px;
        }

        .bold {
            font-weight: bold;
        }

        .container {
            margin: 20px;
        }

        .header{}
        .footer:after {
            content: "pag." counter(page);
        }
        .footer{
            position: fixed;
            bottom: 0;
            width: 100%;
            display: flex; /* Folosește flexbox */
            justify-content: flex-end; /* Alină la dreapta */
            padding-right: 10px; /* Opțional: adaugă un spațiu la dreapta */
        }

        .content {
            margin-top: 10px;
        }

        p {
            margin: 5px 0;
            font-size: 12px;
        }

        .header p {
            font-size: 14px;
        }

        .img-fit {
            width: 100%;
            height: 100px;
            max-width: 100%;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* Align items vertically center */
        }

        .header-left,
        .header-right {
            width: 48%;
            /* Adjust width as necessary */
        }

        .table-container {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }

        .table-container th,
        .table-container td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }

        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .table-title {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
        }

        .page-break {
            page-break-before: always;
        }

        .table-antet {
            width: 100%;
            border-collapse: collapse;
        }
        .table-container2 {
            width: 100%; /* Latimea unei pagini A4 */
            margin: 0 auto; /* Centreaza containerul pe pagina */
            padding: 8px; /* Adaugă puțin spațiu între container și marginea paginii */
            box-sizing: border-box;
        }
        td.first-column {
            width: 120px;
            height: 20px;
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }
        .tr-height {
            height: 2px;
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }
        .line-color {
            width: 100%;
            height: 0.5mm;
            margin-bottom: 1mm;
            margin-top: 1mm;
            background-color: black;
        }
        .isf-coordonator{
            text-align: center;
            margin-bottom: 10px;

        }

        .aligned-container{
            margin: 0 auto; /* Center the container horizontally */
            padding: 10px; /* Optional padding inside the container */
            box-sizing: border-box; /* Ensure padding is included in the width/height */
            margin-top: 0; /* Remove any margin from the top */
            margin-bottom: 0; /* Remove any margin from the bottom */
            padding-top: 0; /* Optional: Remove padding from the top */
            padding-bottom: 0; /* Optional: Remove padding from the bottom */
        }

        .styled-paragraph {
            display: block;
            text-align: justify;
            margin:0;
            font-size: 11px;
        }

        .table-rows{
            border: 1px solid black;
        }

        .table-div{
            background-color: purple;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>

@php
    //format data EXAMEN TEORETIC
    // $dataExamenT1 = $comisie->data_exam_t1;
    // $date1 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataExamenT1);
    // $formattedDataExamenT1 = $date1->format('d/m/Y');
    // //format data EXAMEN PRACTIC
    // $dataExamenP1 = $comisie->data_exam_p1;
    // $date2 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataExamenP1);
    // $formattedDataExamenP1 = $date2->format('d/m/Y');
    //format data COMISIE ISF
    $dataCerereUF = $comisie->cerere_uf_data;
    $date3 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataCerereUF);
    $formattedDataCerereUF = $date3->format('d/m/Y');
    //format data ISF
    $originalDate = $comisie->data_ISF; // Data din variabila ta, format "aaaa-ll-zz"
    $date = \Carbon\Carbon::createFromFormat('Y-m-d', $originalDate);
    $formattedDate = $date->format('d/m/Y'); // Formatează data în "zz/ll/aaaa"
    $cenafer_actual = '';

    if ($comisie->CENAFER === '1') {
        $cenafer_actual = 'bucuresti';
    } elseif ($comisie->CENAFER === '2') {
        $cenafer_actual = 'brasov';
    } elseif ($comisie->CENAFER === '3') {
        $cenafer_actual = 'constanta';
    } elseif ($comisie->CENAFER === '4') {
        $cenafer_actual = 'cluj';
    } elseif ($comisie->CENAFER === '5') {
        $cenafer_actual = 'craiova';
    } elseif ($comisie->CENAFER === '6') {
        $cenafer_actual = 'galati';
    } elseif ($comisie->CENAFER === '7') {
        $cenafer_actual = 'iasi';
    } elseif ($comisie->CENAFER === '8') {
        $cenafer_actual = 'timisoara';
    }
    
@endphp

<body>
    <header>
        <div class="table-container2">
            <table class="table-antet">
                <tr>
                    <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                    <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
                </tr>
                <tr>
                    <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
                </tr>
                <tr>
                    <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
                </tr>
                <tr>
                    <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
                </tr>
                <tr>
                    <td class="tr-height ">E-mail:  isf.{{strtr(strtolower($isf->ISF), 
                        ['ă'=>'a', 'â'=>'a', 'ș'=>'s', 'ț'=>'t', 'î'=>'i', 'Ă'=>'A', 'Â'=>'A', 'Ș'=>'S', 'Ț'=>'T', 'Î'=>'I'])}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
                </tr>
            </table>
            <div class="line-color"></div>
        </div>
    </header>
    <div>
        <div>
            <p class="bold">
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitari[0]->id_isf === 1  || $solicitari[0]->id_isf === 2  || $solicitari[0]->id_isf === 3  || $solicitari[0]->id_isf === 7)
                    Compartimentul Inspectorat de Siguranță Feroviară
                @else Inspectoratul de Siguranță Feroviară
                @endif
                {{$isf->ISF}}
            </p> <!-- space-between -->
            <p>Nr. {{ $isf->cod_serviciu }}/{{ $comisie->nr_ISF }}</> din {{ $formattedDate }}</span> - <span class="bold">C{{$comisie->nr_comisie}}</span></p>
        </div>
        <div class="aligned-container">
            <p>Către: <span class="bold">{{ $solicitari[0]->unitate }}</span></p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Referitor
                la: Emiterea de <span class="bold">DUPLICATE</span> ale autorizațiilor deținute de personalul cu
                responsabilități în
                siguranța
                circulației care urmează să desfășoare, pe proprie răspundere, activități specifice transportului
                feroviar, solicitată de
                persoana juridică <span class="bold">{{ $solicitari[0]->unitate }}</span> CUI/CIF <span
                    class="bold">{{ $unitate->cod_fiscal }}</span>, cu sediul în localitatea
                {{ $unitate->localitate_uf }} la adresa {{ $unitate->adresa_uf }}, prin actul
                cu numărul <span class="bold">{{ $comisie->nr_CENAFER }}</span> din
                <span class="bold">{{ $formattedDataCerereUF }}</span>.
            </p>
        </div>



        <div>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Vă
                comunicăm că, în conformitate cu prevederile HG nr. 309/2023 și OMTCT nr. 2262/2005 cu modificările și
                completările ulterioare, ASFR aprobă emiterea de duplicate ale autorizațiilor precizate în tabelul
                anexat.</p>
            <div>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tariful pentru emiterea duplicatelor autorizațiilor precizate în tabelul anexat, stabilit conform
                    prevederilor OMT nr. 1471
                    din 07.08.2023, cu modificările și completările ulterioare, este de <span class="bold">{{ $tarifCalculat }}</span>
                    euro la care NU se adaugă TVA, cf art. 269, al. 5 din Legea nr. 227/2015-Codul Fiscal, cu completările și modificările ulterioare. Plata se va efectua în contul de virament 
                    IBAN ASFR <span class="bold">************************</span>, deschis la Trezoreria Operativă a
                    sectorului 1,
                    București, cod <span  class="bold">BIC TREZROBU, CIF 48008564</span>, conform facturii întocmite de ASFR iar Ordinul de Plată va avea menționat numărul facturii și numărul prezentului document.</p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Autorizațiile se vor ridica de la sediul ISF de către delegatul dvs. pe baza copiei documentului de
                    plată a sumei precizate mai sus.</p>
                <p class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tariful se va achita în maxim 5 zile lucrătoare de la data primirii facturii, fără a se depăși data desfășurării examinării teoretice.</p>
            </div>
        </div>
        <div class="isf-coordonator">
            <p></p>
            <p>
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitari[0]->id_isf === 1  || $solicitari[0]->id_isf === 2  || $solicitari[0]->id_isf === 3  || $solicitari[0]->id_isf === 7)
                    <span class="bold">Coordonator CISF</span>
                @else
                    <span class="bold">Inspector Șef Teritorial</span>
                @endif
            </p>
            <p><span class="bold">{{ $isf->sef_ISF }}</span></p>
        </div>
        <footer class="footer">
            <p></p>
        </footer>
    </div>
    <div class="page-break"></div>
    <div class="table-container2">
        <table class="table-antet">
            <tr>
                <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
            </tr>
            <tr>
                <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
            </tr>
            <tr>
                <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
            </tr>
            <tr>
                <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
            </tr>
            <tr>
                <td class="tr-height ">E-mail:  isf.{{$cenafer_actual}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
            </tr>
        </table>
    </div>
    <div class="line-color"></div>
    <div class="table-title">Autorizații pentru care vor fi emise DUPLICATE</div>
    <table class="table-container">
        <thead>
            <tr>
                <th>Nr.</th>
                <th>Nume, prenume</th>
                <th>CNP</th>
                <th>Număr</th>
                <th>Data emiterii</th>
                <th>Valabilitate</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td colspan='2'>Funcție</td>
                <td colspan='4'>Tip Autorizație</td>
            </tr>
            <tr>
                <td colspan='6'>Domeniu</td>
            </tr>
        </tbody>
    </table>
    <br>
    @if ($solicitariAnterioare)
        @foreach ($solicitariAnterioare as $index => $solicitareAnterioara)
            <table class="table-container">
                <thead>
                    <tr>
                        <th>{{ $index + 1 }}</th>
                        <th>
                            {{ $solicitareAnterioara->solicitant_nume ? $solicitareAnterioara->solicitant_nume : $solicitareAnterioara->nume }}
                            {{ $solicitareAnterioara->solicitant_prenume ? $solicitareAnterioara->solicitant_prenume : $solicitareAnterioara->prenume }}
                        </th>
                        <th>{{ $solicitareAnterioara->cnp }}</th>
                        <th>{{ $solicitareAnterioara->nr_aut }}</th>
                        <th>{{ $solicitareAnterioara->data_elib }}</th>
                        <th>{{ $solicitareAnterioara->valabilitate }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan='2'>{{ $solicitareAnterioara->functie }}</td>
                        <td colspan='4'>{{ $solicitareAnterioara->autorizatie }}</td>
                    </tr>
                    <tr>
                        @if (isset($solicitareAnterioara['domeniu']))
                            <td colspan='6'>{{ $solicitareAnterioara->domeniu }}</td>
                        @else
                            <td colspan='6'></td>
                        @endif
                    </tr>
                </tbody>
            </table>
        @endforeach
    @endif
    <footer class="footer">
        <p style="display: block; text-align: right;">act Nr. {{ $isf->cod_serviciu }}/<span class="bold">{{ $comisie->nr_ISF }}</span> din <span class="bold">{{ $formattedDate }}</span></p>
    </footer>
</body>

</html>
