@props(['solicitari', 'comisie', 'isf'])

<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document de încetare suspendare</title>
    <style>
        body {
            font-family: DejaVu Sans, sans-serif;
            font-size: 10px;
        }

        .bold {
            font-weight: bold;
        }

        .container {
            margin: 20px;
        }

        .header,
        .footer {
            content: "pag." counter(page);
        }

        .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            display: flex; /* Folosește flexbox */
            justify-content: flex-end; /* Alină la dreapta */
            padding-right: 10px; /* Opțional: adaugă un spațiu la dreapta */
        }

        .content {
            margin-top: 10px;
        }

        p {
            margin: 5px 0;
            font-size: 12px;
        }

        .header p {
            font-size: 14px;
        }

        .img-fit {
            width: 100%;
            height: 100px;
            max-width: 100%;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* Align items vertically center */
        }

        .header-left,
        .header-right {
            width: 48%;
            /* Adjust width as necessary */
        }


        .table-container {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }

        .table-container {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }

        .table-container th,
        .table-container td {
            border: 1px solid #000;
            padding: 4px;
            text-align: left;
        }

        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .table-title {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
        }

        .page-break {
            page-break-before: always;
        }
        .table-antet {
            width: 100%;
            border-collapse: collapse;
        }

        .table-container2 {
            width: 100%; /* Latimea unei pagini A4 */
            margin: 0 auto; /* Centreaza containerul pe pagina */
            padding: 8px; /* Adaugă puțin spațiu între container și marginea paginii */
            box-sizing: border-box;
        }
        td.first-column {
            width: 120px;
            height: 20px;
        }
        .tr-height {
            height: 2px;
        }
        .line-color {
            width: 100%;
            height: 0.5mm;
            margin-bottom: 1mm;
            margin-top: 1mm;
            background-color: black;
        }
        .isf-coordonator{
            text-align: center;
            margin-bottom: 10px;

        }
        th,
        td {
            /* Cell borders */
            padding: 1px;
            text-align: center;
        }

        th {
            background-color: #f2f2f2;
            font-size: 11px;
            font-weight: bold;
        }

        td {
            font-size: 11px;
        }

        .table-rows{
            border: 1px solid black;
        }
        .table-specific {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border: 1px solid black;
            /* Table border */
        }
    </style>
</head>

@php
    //format data COMISIE ISF
    $dataCerereUF = $comisie->cerere_uf_data;
    $date3 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataCerereUF);
    $formattedDataCerereUF = $date3->format('d/m/Y');
    //format data ISF
    $originalDate = $comisie->data_ISF; // Data din variabila ta, format "aaaa-ll-zz"
    $date = \Carbon\Carbon::createFromFormat('Y-m-d', $originalDate);
    $formattedDate = $date->format('d/m/Y'); // Formatează data în "zz/ll/aaaa"
    $cenafer_actual = '';

    $data_incetare = $comisie->cerere_uf_data;
    $dataIncetare = \Carbon\Carbon::createFromFormat('Y-m-d', $data_incetare);

    if ($comisie->CENAFER === '1') {
        $cenafer_actual = 'bucuresti';
    } elseif ($comisie->CENAFER === '2') {
        $cenafer_actual = 'brasov';
    } elseif ($comisie->CENAFER === '3') {
        $cenafer_actual = 'constanta';
    } elseif ($comisie->CENAFER === '4') {
        $cenafer_actual = 'cluj';
    } elseif ($comisie->CENAFER === '5') {
        $cenafer_actual = 'craiova';
    } elseif ($comisie->CENAFER === '6') {
        $cenafer_actual = 'galati';
    } elseif ($comisie->CENAFER === '7') {
        $cenafer_actual = 'iasi';
    } elseif ($comisie->CENAFER === '8') {
        $cenafer_actual = 'timisoara';
    }
    
    $litera = null;
    if (preg_match('/Art\. 8 alin 1\. lit\. ([g-s])\)/i', $solicitari[0]->motiv_suspendare, $match)) {
        $litera = $match[1];
    }

    $literaaf = null;
    if (preg_match('/Art\. 8 alin 1\. lit\. ([a-f])\)/i', $solicitari[0]->motiv_suspendare, $match)) {
        $literaaf = $match[1];
    }

@endphp

<body>
    <header>
        <div class="table-container2">
            <table class="table-antet">
                <tr>
                    <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                    <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
                </tr>
                <tr>
                    <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
                </tr>
                <tr>
                    <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
                </tr>
                <tr>
                    <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
                </tr>
                <tr>
                    <td class="tr-height ">E-mail:  isf.{{strtr(strtolower($isf->ISF), 
                        ['ă'=>'a', 'â'=>'a', 'ș'=>'s', 'ț'=>'t', 'î'=>'i', 'Ă'=>'A', 'Â'=>'A', 'Ș'=>'S', 'Ț'=>'T', 'Î'=>'I'])}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
                </tr>
            </table>
            <div class="line-color"></div>
        </div>
    </header>
    <div class="container">
        <div>
            <p class="bold">
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitari[0]->id_isf === 1  || $solicitari[0]->id_isf === 2  || $solicitari[0]->id_isf === 3  || $solicitari[0]->id_isf === 7)
                    Compartimentul Inspectorat de Siguranță Feroviară
                @else Inspectoratul de Siguranță Feroviară
                @endif
                {{$isf->ISF}}
            </p> <!-- space-between -->
            <p>Nr. {{ $isf->cod_serviciu }}/{{ $comisie->nr_ISF }}</> din {{ $formattedDate }}</span> - <span class="bold">C{{$comisie->nr_comisie}}</span></p>
            @if ($litera)
                <p><span style="display: block; text-align: right; margin: 0; margin-right: 60px; font-weight: bold;">APROBAT,</span></p>
                <p><span style="display: block; text-align: right; margin: 0; margin-right: 40px">Director General,</span></p>
                <p><span style="display: block; text-align: right; margin: 0; margin-right: 50px">Petru BOGDAN</span></p>
            @endif
            <p></p>
        </div>
        <div class="content">
            <p>Către: <span class="bold">{{ $solicitari[0]->unitate }}</span></p>
            <p></p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Referitor
                la: <span class="bold">ÎNCETAREA SUSPENDĂRII</span> autorizațiilor deținute de personalul cu
                responsabilități în siguranța circulației care desfășoară, pe proprie răspundere, activități specifice
                transportului feroviar, solicitată de persoana juridică <span
                    class="bold">{{ $solicitari[0]->unitate }}</span> CUI/CIF <span
                    class="bold">{{ $unitate->cod_fiscal }}</span>, cu sediul în localitatea
                {{ $unitate->localitate_uf }} la adresa {{ $unitate->adresa_uf }}, prin actul
                cu numărul <span class="bold">{{ $comisie->cerere_uf_nr }}</span> din
                <span class="bold">{{ $formattedDataCerereUF }}</span>.
            </p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Vă
                comunicăm că, în conformitate cu prevederile art. nr. 13 din Anexa nr. 1 al OMTCT nr. 2262/2005,
                articolului 30, alin(3) din Anexa nr. 1 al OMT nr. 615 din 2015 și articolului 42 din Anexa nr. 2 al OMT
                nr. 615 din 2015, se dispune încetarea suspendării autorizațiilor permiselor de conducere precizate în
                tabelul anexat.</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Autorizațiile menționate își reiau valabilitatea începând cu data de <span
                    class="bold">{{ $formattedDate }}</span>.</p>
        </div>
        <div class="isf-coordonator">
            <p></p>
            <p>
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitari[0]->id_isf === 1  || $solicitari[0]->id_isf === 2  || $solicitari[0]->id_isf === 3  || $solicitari[0]->id_isf === 7)
                        <span class="bold">Coordonator CISF</span>
                    @else
                        <span class="bold">Inspector Șef Teritorial</span>
                    @endif
            </p>
            <p><span class="bold">{{ $isf->sef_ISF }}</span></p>
            <p></p>
        </div>
    </div>
    <footer class="footer">
        <p></p>
    </footer>
    <div class="page-break"></div>
    <div class="table-container2">
        <table class="table-antet">
            <tr>
                <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
            </tr>
            <tr>
                <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
            </tr>
            <tr>
                <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
            </tr>
            <tr>
                <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
            </tr>
            <tr>
                <td class="tr-height ">E-mail:  isf.{{strtr(strtolower($isf->ISF), 
                    ['ă'=>'a', 'â'=>'a', 'ș'=>'s', 'ț'=>'t', 'î'=>'i', 'Ă'=>'A', 'Â'=>'A', 'Ș'=>'S', 'Ț'=>'T', 'Î'=>'I'])}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
            </tr>
        </table>
        <div class="line-color"></div>
    </div>
    <div class="table-title">Autorizații cărora le ÎNCETEAZĂ SUSPENDAREA începând cu data de {{ $formattedDate }}
    </div>
    <table class="table-container">
        <thead>
            <tr>
                <th>Nr.</th>
                <th>Nume, prenume</th>
                <th>CNP</th>
                <th>Număr</th>
                <th colspan='2'>Încetare suspendare de la</th>
                <th colspan='2'>Conformitate cu normele în vigoare</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td colspan='2'>Funcție</td>
                <td colspan='6'>Tip Autorizație</td>
            </tr>
            <tr>
                <td colspan='8'>Domeniu</td>
            </tr>
            <tr>
                <td colspan='8'>Documente doveditoare</td>
            </tr>
        </tbody>
    </table>
    <br>
    @if ($solicitariAnterioare)
        @foreach ($solicitariAnterioare as $index => $solicitareAnterioara)
            @php
                $dataCertificat = NULL;
                $dataAviz = NULL;
                if (isset($solicitareAnterioara->incetare_suspendare_data_certificat)){
                    $date = \Carbon\Carbon::createFromFormat('Y-m-d', $solicitareAnterioara->incetare_suspendare_data_certificat);
                    $dataCertificat = $date->format('d/m/Y'); // Formatează data în "zz/ll/aaaa"
                }else {
                    $date = \Carbon\Carbon::createFromFormat('Y-m-d', $solicitareAnterioara->incetare_suspendare_data_aviz);
                    $dataAviz = $date->format('d/m/Y'); // Formatează data în "zz/ll/aaaa"
                }
                Log::info('nr comisie aici' . $solicitareAnterioara->nr_comisie);
                Log::info('motiv suspendare aici' . $solicitareAnterioara->motiv_suspendare);
                Log::info('suspendara toata aici' . $solicitareAnterioara);
            @endphp    
            <table class="table-container">
                <thead>
                    <tr>
                        <th>{{ $index + 1 }}</th>
                        @if (isset($solicitareAnterioara['solicitant_nume']))
                            <th>{{ $solicitareAnterioara->solicitant_nume }} {{ $solicitareAnterioara->solicitant_prenume }}</th>
                        @else
                            <th>{{ $solicitareAnterioara->nume }} {{ $solicitareAnterioara->prenume }}</th>
                        @endif
                        <th>{{ $solicitareAnterioara->cnp }}</th>
                        <th>{{ $solicitareAnterioara->nr_aut }}</th>
                        <th colspan='2'>
                            {{ $formattedDate}}</th>
                        <th colspan='2'>Anexa nr. 1 al OMTCT nr. 2262/2005, {{ $solicitareAnterioara->motiv_suspendare }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan='2'>{{ $solicitareAnterioara->functie }}</td>
                        <td colspan='6'>{{ $solicitareAnterioara->autorizatie }}</td>
                    </tr>
                    <tr>
                        @if (isset($solicitareAnterioara['domeniu']))
                            <td colspan='8'>{{ $solicitareAnterioara->domeniu }}</td>
                        @else
                            <td colspan='8'></td>
                        @endif
                    </tr>
                    <tr>
                        @if (isset($solicitareAnterioara['incetare_suspendare_nr_certificat']))
                        <td colspan='8'>- Certificat competențe generale nr.{{ $solicitareAnterioara->incetare_suspendare_nr_certificat }} / {{ $dataCertificat }} emis de {{ $solicitareAnterioara->incetare_suspendare_eliberat_certificat }}</td>
                        @else
                        <td colspan='8'>- Aviz Medical/Psihologic nr.{{ $solicitareAnterioara->incetare_suspendare_nr_aviz }} / {{ $dataAviz }} emis de {{ $solicitareAnterioara->incetare_suspendare_eliberat_aviz }}</td>
                        @endif
                    </tr>
                </tbody>
            </table>
        @endforeach
    @endif
    <p></p>
    <p></p>
    <p style="text-align: center">Întocmit de</p>
    <p style="text-align: center; font-weight: bold">{{ $comisie->redactat }}</p>

    <footer class="footer">
        <p style="display: block; text-align: right;">act Nr. {{ $isf->cod_serviciu }}/<span class="bold">{{ $comisie->nr_ISF }}</span> din <span class="bold">{{ $formattedDate }}</span></p>
    </footer>
</body>

</html>
