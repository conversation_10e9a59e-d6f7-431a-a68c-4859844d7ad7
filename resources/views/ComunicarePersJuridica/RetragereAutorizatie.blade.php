@props(['solicitari', 'comisie', 'isf'])

<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document de retragere autorizație</title>
    <style>
        body {
            font-family: DejaVu Sans, sans-serif;
            font-size: 10px;
        }

        .bold {
            font-weight: bold;
        }

        .container {
            margin: 20px;
        }

        .header,
        .footer {
            text-align: center;
            margin-bottom: 10px;
        }

        .content {
            margin-top: 10px;
        }

        p {
            margin: 5px 0;
            font-size: 12px;
        }

        .header p {
            font-size: 14px;
        }

        .img-fit {
            width: 100%;
            height: 100px;
            max-width: 100%;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* Align items vertically center */
        }

        .header-left,
        .header-right {
            width: 48%;
            /* Adjust width as necessary */
        }

        .table-container {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }

        .table-container th,
        .table-container td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }

        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .table-title {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
        }

        .page-break {
            page-break-before: always;
        }

        .isf-coordonator{
            text-align: center;
            margin-bottom: 10px;

        }
    </style>
</head>

<body>
    {{-- 
    Catre nume, prenume - personal-solicitant_nume + solicitant_prenume
    Referitor la - personal-tip_comisie
    numar act - comisie-cerere_uf_nr
    persoanie fizice - personal-solicitant_nume + solicitant_prenume, 
    cnp
    domiciliu - localitate om, adresa om
    nume presedinte comisie - personal -nume,prenume
    membri comisie-comisie-
    urgenta-comisie-urgent
    loc,data,ora exam teoretica
    loc,data,ora exam practica
    ISF-personal-id_isf-sef_ISF
    redactat
    --}}
    <div class="container">
        <div class="header">
            <img class="img-fit" src="{{ public_path('/antet_tiparireComunicare.png') }}" alt="Image">
        </div>
        <div>
            <p>
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitari[0]->id_isf === 1  || $solicitari[0]->id_isf === 2  || $solicitari[0]->id_isf === 3  || $solicitari[0]->id_isf === 7)
                    Compartimentul Inspectorat de Siguranță Feroviară
                @else Inspectoratul de Siguranță Feroviară
                @endif
                {{$isf->ISF}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;APROBAT,
            </p> <!-- space-between -->
            <p>Nr. {{ $isf->cod_serviciu }}/<span class="bold">{{ $comisie->nr_ISF }}</span> din <span
                    class="bold">{{ $comisie->data_ISF }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Director General,</p>
            <p><span
                    class="bold">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ $solicitari[0]->directorASFR }}</span>
            </p>
        </div>
        <div class="content">
            <p>Către: <span class="bold">{{ $solicitari[0]->unitate }}</span></p>
            <p></p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Referitor
                la: <span class="bold">RETRAGEREA</span> autorizațiilor/permiselor de conducere deținute de personalul
                cu responsabilități în
                siguranța circulației care urmează să desfășoare, pe proprie răspundere, activități specifice
                transportului
                feroviar, solicitată de persoana juridică <span class="bold">{{ $solicitari[0]->unitate }}</span>
                CUI/CIF <span class="bold">{{ $unitate->cod_fiscal }}</span>, cu sediul în localitatea
                {{ $unitate->localitate_uf }} la adresa {{ $unitate->adresa_uf }}, prin actul
                cu numărul <span class="bold">{{ $comisie->cerere_uf_nr }}</span> din
                <span class="bold">{{ $comisie->cerere_uf_data }}</span>.
            </p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Vă
                comunicăm că, în conformitate cu prevederile art. nr. 9 alin. (3) din Anexa nr. 1 la OMTCT nr.
                2262/2005, art. 28, alin. (1) din Anexa nr. 1 la OMT nr. 615/2015 și art. 42 din Anexa nr. 2 la OMT nr.
                615/2015, se dispune retragerea autorizațiilor/permiselor de conducere precizate în tabelul anexat.</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Autorizațiile/permisele
                de conducere menționate își încetează definitiv valabilitatea începând cu data de <span
                    class="bold">{{ $comisie->data_ISF }}</span>.</p>
                    
        </div>
        <div class="isf-coordonator">
            <p></p>
            <p>
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitari[0]->id_isf === 1  || $solicitari[0]->id_isf === 2  || $solicitari[0]->id_isf === 3  || $solicitari[0]->id_isf === 7)
                        <span class="bold">Coordonator CISF</span>
                    @else
                        <span class="bold">Inspector Șef Teritorial</span>
                    @endif
            </p>
            <p><span class="bold">{{ $isf->sef_ISF }}</span></p>
        </div>
        <div class="footer">
            <p>Întocmit,</p>
            <p><span class="bold">{{ $comisie->redactat }}</span></p>
            <p>act Nr. {{ $isf->cod_serviciu }}/<span class="bold">{{ $comisie->nr_ISF }}</span> din <span
                    class="bold">{{ $comisie->data_ISF }}</span>
            </p>
        </div>
    </div>
    <div class="page-break"></div>
    <div class="table-title">Autorizații RETRASE începând cu data de {{ $comisie->data_ISF }}</div>
    <table class="table-container">
        <thead>
            <tr>
                <th>Nr.</th>
                <th>Nume, prenume</th>
                <th>CNP</th>
                <th>Număr</th>
                <th>Valabilitate</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td colspan='2'>Funcție</td>
                <td colspan='3'>Tip Autorizație</td>
            </tr>
            <tr>
                <td colspan='5'>Domeniu</td>
            </tr>
            <tr>
                <td colspan='5'>Conformitatea cu normele în vigoare</td>
            </tr>
            <tr>
                <td colspan='5'>Cauza retragerii</td>
            </tr>
        </tbody>
    </table>
    <br>
    @if ($solicitariAnterioare)
        @foreach ($solicitariAnterioare as $index => $solicitareAnterioara)
            <table class="table-container">
                <thead>
                    <tr>
                        <th>{{ $index + 1 }}</th>
                        <th>{{ $solicitareAnterioara->solicitant_nume }}
                            {{ $solicitareAnterioara->solicitant_prenume }}
                        </th>
                        <th>{{ $solicitareAnterioara->cnp }}</th>
                        <th>{{ $solicitareAnterioara->nr_aut }}</th>
                        <th>{{ $solicitareAnterioara->valabilitate }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan='2'>{{ $solicitareAnterioara->functie }}</td>
                        <td colspan='3'>{{ $solicitareAnterioara->autorizatie }}</td>
                    </tr>
                    <tr>
                        @if (isset($solicitareAnterioara['domeniu']))
                            <td colspan='5'>{{ $solicitareAnterioara->domeniu }}</td>
                        @else
                            <td colspan='5'></td>
                        @endif
                    </tr>
                    <tr>
                        <td colspan='5'>Anexa nr. 1 al OMTCT nr. 2262/2005, art.8 alin.(2), lit. DECEDAT</td>
                    </tr>
                    <tr>
                        <td colspan='5'>DECEDAT</td>
                    </tr>
                </tbody>
            </table>
        @endforeach
    @endif
</body>

</html>
