@props(['solicitari', 'comisie', 'isf'])

<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document de schimbare nume</title>
    <style>
        body {
            font-family: DejaVu Sans, sans-serif;
            font-size: 10px;
        }

        .bold {
            font-weight: bold;
        }

        .container {
            margin: 20px;
        }

        .header,
        .footer {
            text-align: center;
            margin-bottom: 10px;
        }

        .content {
            margin-top: 10px;
        }

        p {
            margin: 5px 0;
            font-size: 12px;
        }

        .header p {
            font-size: 14px;
        }

        .img-fit {
            width: 100%;
            height: 100px;
            max-width: 100%;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* Align items vertically center */
        }

        .table-title {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
        }

        .header-left,
        .header-right {
            width: 48%;
            /* Adjust width as necessary */
        }
        .aligned-container{
            margin: 0 auto; /* Center the container horizontally */
            padding: 10px; /* Optional padding inside the container */
            box-sizing: border-box; /* Ensure padding is included in the width/height */
            margin-top: 0; /* Remove any margin from the top */
            margin-bottom: 0; /* Remove any margin from the bottom */
            padding-top: 0; /* Optional: Remove padding from the top */
            padding-bottom: 0; /* Optional: Remove padding from the bottom */
        }

        .styled-paragraph {
            display: block;
            text-align: justify;
            margin:0;
            font-size: 12px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }
        td {
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }
        td.first-column {
            width: 120px;
            height: 20px;
        }
        .tr-height {
            height: 2px;
        }
        .line-color {
            width: 100%;
            height: 0.5mm;
            margin-bottom: 1mm;
            margin-top: 1mm;
            background-color: black;
        }
        .isf-coordonator{
            text-align: center;
            margin-bottom: 10px;

        }

        .page-break {
            page-break-before: always;
        }
        .td-persoane {
            border: 0.75px solid #000000;
            padding: 9px;
            height: auto;
            text-align: center;
        }

        .th-persoane {
            border: 0.75px solid #000000;
            padding: 9px;
            height: auto;
            text-align: center;
        }
        .paragraph {
            margin-bottom: 5px;
        }
        .table-antet {
            width: 100%;
            border-collapse: collapse;
        }
        .table-container2 {
            width: 100%; /* Latimea unei pagini A4 */
            margin: 0 auto; /* Centreaza containerul pe pagina */
            padding: 8px; /* Adaugă puțin spațiu între container și marginea paginii */
            box-sizing: border-box;
        }
        td.first-column {
            width: 120px;
            height: 20px;
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }
        .tr-height {
            height: 2px;
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }
        .line-color {
            width: 100%;
            height: 0.5mm;
            margin-bottom: 1mm;
            margin-top: 1mm;
            background-color: black;
        }
        p {
            margin: 2px;
            padding: 0px;
        }
    </style>
</head>

@php

    if ($solicitariAnterioare){
        foreach ($solicitariAnterioare as $index => $solicitareAnterioara){
            $dataEliberare = $solicitareAnterioara->data_elib;
            $date3 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataEliberare);
            $formattedDataEliberare = $date3->format('d/m/Y');
        
            $dataValabilitate = $solicitareAnterioara->valabilitate;
            $date4 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataValabilitate);
            $formattedDataValabilitate = $date4->format('d/m/Y');
        }
    }
    //format data COMISIE ISF
    $dataCerereUF = $comisie->cerere_uf_data;
    $date3 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataCerereUF);
    $formattedDataCerereUF = $date3->format('d/m/Y');
    //format data ISF
    $originalDate = $comisie->data_ISF; // Data din variabila ta, format "aaaa-ll-zz"
    $date = \Carbon\Carbon::createFromFormat('Y-m-d', $originalDate);
    $formattedDate = $date->format('d/m/Y'); // Formatează data în "zz/ll/aaaa"
    $cenafer_actual = '';

    if ($comisie->CENAFER === '1') {
        $cenafer_actual = 'bucuresti';
    } elseif ($comisie->CENAFER === '2') {
        $cenafer_actual = 'brasov';
    } elseif ($comisie->CENAFER === '3') {
        $cenafer_actual = 'constanta';
    } elseif ($comisie->CENAFER === '4') {
        $cenafer_actual = 'cluj';
    } elseif ($comisie->CENAFER === '5') {
        $cenafer_actual = 'craiova';
    } elseif ($comisie->CENAFER === '6') {
        $cenafer_actual = 'galati';
    } elseif ($comisie->CENAFER === '7') {
        $cenafer_actual = 'iasi';
    } elseif ($comisie->CENAFER === '8') {
        $cenafer_actual = 'timisoara';
    }
    
@endphp
<body>
    <header>
        <div class="table-container2">
            <table>
                <tr>
                    <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                    <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
                </tr>
                <tr>
                    <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
                </tr>
                <tr>
                    <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
                </tr>
                <tr>
                    <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
                </tr>
                <tr>
                    <td class="tr-height ">E-mail:  isf.{{strtr(strtolower($isf->ISF), 
                        ['ă'=>'a', 'â'=>'a', 'ș'=>'s', 'ț'=>'t', 'î'=>'i', 'Ă'=>'A', 'Â'=>'A', 'Ș'=>'S', 'Ț'=>'T', 'Î'=>'I'])}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
                </tr>
            </table>
            <div class="line-color"></div>
        </div>
    </header>
    <div class="container">
        <div>
            <p class="bold">
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitari[0]->id_isf === 1  || $solicitari[0]->id_isf === 2  || $solicitari[0]->id_isf === 3  || $solicitari[0]->id_isf === 7)
                    Compartimentul Inspectorat de Siguranță Feroviară
                @else Inspectoratul de Siguranță Feroviară
                @endif
                {{$isf->ISF}}
            </p> <!-- space-between -->
            <p>Nr. {{ $isf->cod_serviciu }}/{{ $comisie->nr_ISF }}</> din {{ $formattedDate }}</span> - <span class="bold">C{{$comisie->nr_comisie}}</span></p>
        </div>
        </br>
        <div class="content">
            <p>Către: <span class="bold">{{ $solicitari[0]->unitate }}</span></p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Referitor
                la: Reemiterea autorizațiilor deținute, ca urmare a <span class="bold">SCHIMBĂRII NUMELUI</span>
                personalului cu responsabilități în siguranța
                circulației care urmează să desfășoare, pe proprie răspundere, activități specifice transportului
                feroviar, solicitată de persoana juridică <span class="bold">{{ $solicitari[0]->unitate }}</span>
                CUI/CIF
                <span class="bold">{{ $unitate->cod_fiscal }}</span>, cu sediul în localitatea
                {{ $unitate->localitate_uf }} la adresa {{ $unitate->adresa_uf }}, prin actul
                cu numărul <span class="bold">{{ $comisie->cerere_uf_nr }}</span> din
                <span class="bold">{{ $formattedDataCerereUF }}</span>.
            </p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Vă
                comunicăm că, în conformitate cu prevederile OMTCT nr. 2262/2005 cu modificările și
                completările ulterioare, ASFR aprobă reemiterea autorizațiilor deținute de persoana cu responsabilități
                în siguranța circulației care desfășoară, pe propria răspundere, activități specifice transportului
                feroviar:</p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Nume:
                {{ $solicitariAnterioare[0]->solicitant_nume ? $solicitariAnterioare[0]->solicitant_nume : $solicitariAnterioare[0]->nume }}</p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prenume:
                {{ $solicitariAnterioare[0]->solicitant_prenume ? $solicitariAnterioare[0]->solicitant_prenume : $solicitariAnterioare[0]->prenume }}</p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CNP: {{ $solicitariAnterioare[0]->cnp }}</p>
            </br>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;pe actuala identitate:</p>
            </br>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Nume:
                {{ $solicitari[0]->solicitant_nume ? $solicitari[0]->solicitant_nume : $solicitari[0]->nume }}</p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prenume:
                {{ $solicitari[0]->solicitant_prenume ? $solicitari[0]->solicitant_prenume : $solicitari[0]->prenume }}</p>
            </br>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tariful pentru reemiterea autorizațiilor precizate în tabelul anexat, stabilit conform prevederilor
                OMT nr. 1471
                din 07.08.2023, cu modificările și completările ulterioare, este de <span class="bold">{{ $tarifCalculat }}</span>
                euro la care NU se adaugă TVA, cf art. 269, al. 5 din Legea nr. 227/2015-Codul Fiscal, cu completările și modificările ulterioare. Plata se va efectua în contul de virament 
                IBAN ASFR <span class="bold">************************</span>, deschis la Trezoreria Operativă a
                sectorului 1,
                București, cod <span  class="bold">BIC TREZROBU, CIF 48008564</span>, conform facturii întocmite de ASFR iar Ordinul de Plată va avea menționat numărul facturii și numărul prezentului document.</p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Autorizațiile se vor ridica de la sediul ASFR de către delegatul ISF și vor fi înmânate delegatului
                solicitantului la sediul ISF, pe baza copiei documentului de plată a sumei precizate mai sus.</p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
            <p  class="styled-paragraph">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
        </div>
        <div class="isf-coordonator">
            <p></p>
            <p>
                @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați' || $solicitari[0]->id_isf === 1  || $solicitari[0]->id_isf === 2  || $solicitari[0]->id_isf === 3  || $solicitari[0]->id_isf === 7)
                    <span class="bold">Coordonator CISF</span>
                @else
                    <span class="bold">Inspector Șef Teritorial</span>
                @endif
            </p>
            <p><span class="bold">{{ $isf->sef_ISF }}</span></p>
        </div>
    </div>
    <div class="page-break"></div>
    <div class="table-container2">
        <table class="table-antet">
            <tr>
                <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
            </tr>
            <tr>
                <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
            </tr>
            <tr>
                <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
            </tr>
            <tr>
                <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
            </tr>
            <tr>
                <td class="tr-height ">E-mail:  isf.{{$cenafer_actual}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
            </tr>
        </table>
    </div>
    <div class="line-color"></div>
    <div class="table-title">Autorizații care vor fi reemise ca urmare a SCHIMBĂRII NUMELUI posesorului</div>
    <table class="table-container">
        <thead>
            <tr>
                <th class="th-persoane">Nr.</th>
                <th class="th-persoane">Număr</th>
                <th class="th-persoane">Data emiterii</th>
                <th class="th-persoane">Valabilitate</th>
                {{-- <th class="th-persoane"></th> --}}
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="td-persoane" colspan='2'>Funcție</td>
                <td class="td-persoane" colspan='3'>Tip Autorizație</td>
            </tr>
            <tr>
                <td class="td-persoane" colspan='5'>Domeniu</td>
            </tr>
        </tbody>
    </table>
    <br>
    @if ($solicitariAnterioare)
        @foreach ($solicitariAnterioare as $index => $solicitareAnterioara)
            <table class="table-container">
                <thead>
                    <tr>
                        <th class="th-persoane">{{ $index + 1 }}</th>
                        <th class="th-persoane">{{ $solicitareAnterioara->nr_aut }}</th>
                        <th class="th-persoane">{{ $formattedDataEliberare }}</th>
                        <th class="th-persoane">{{ $formattedDataValabilitate }}</th>
                        {{-- <th class="th-persoane"></th> --}}
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="td-persoane" colspan='2'>{{ $solicitareAnterioara->functie }}</td>
                        <td class="td-persoane" colspan='3'>{{ $solicitareAnterioara->autorizatie }}</td>
                    </tr>
                    <tr>
                        @if (isset($solicitareAnterioara['domeniu']))
                            <td class="td-persoane" colspan='5'>{{ $solicitareAnterioara->domeniu }}</td>
                        @else
                            <td class="td-persoane" colspan='5'></td>
                        @endif
                    </tr>
                </tbody>
            </table>
        @endforeach
    @endif
    <p></p>
    <p></p>
    <p style="text-align: center">Întocmit de</p>
    <p style="text-align: center">{{ $comisie->redactat }}</p>
</body>

</html>
