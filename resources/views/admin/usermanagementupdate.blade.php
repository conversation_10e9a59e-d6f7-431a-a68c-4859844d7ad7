@props(['menus', 'dateTabel', 'columns'])

<!-- deprecated, vom folosi view-uri individuale precum grouped.blade.php -->
<x-app-layout>
    <x-slot name="header">
        <nav
            class="flex justify-center items-center flex-wrap gap-1 bg-gray-200 dark:bg-gray-800 p-4 font-semibold text-xl dark:text-white text-black dark:text-gray-200 leading-tight">
            @foreach ($menus as $menu)
                <x-custom-dropdown align="left" width="48" contentClasses="py-1 bg-white dark:bg-gray-800" :menuObj="$menu">
                </x-custom-dropdown>
            @endforeach
        </nav>
    </x-slot>
    <div style="text-align: center;">
        
    <table border="1" cellspacing="0" cellpadding="10" style="margin: 0 auto; text-align: left;">
    <div style="text-align: left;">
    <form method="GET" action="{{ route('users.index') }}">
        <select name="sort" id="sort">
            <option value="name_asc">Name (A-Z)</option>
            <option value="name_desc">Name (Z-A)</option>
            <option value="isf_asc">ISF (Ascending)</option>
            <option value="isf_desc">ISF (Descending)</option>
        </select>
        <button type="submit" style="background-color: white;">Sorteaza</button>
    </form>
    </form>

    <table border="3" cellspacing="40" cellpadding="10" style="margin: 0 auto; text-align: center;">
        <thead>
            <tr>
            <th>Name</th>
            <th>Email</th>
            <th>ISF</th>
            <th>Administrator</th>
            </tr>
        </thead>
      
        <tbody>
            @foreach ($users as $user)
                <tr>
                    <td>{{ $user->name }}</td>
                    <td>{{ $user->email }}</td>
                    <td>{{ $user->isf }}</td>
                    <td>
                        <select name="ISF" id="ISF">
                            <option value="0" {{ $user->ISF == 0 ? 'selected' : '' }}>Bucuresti</option>
                            <option value="1" {{ $user->ISF == 1 ? 'selected' : '' }}>Constanta</option>
                            <option value="2" {{ $user->ISF == 2 ? 'selected' : '' }}>Cluj</option>
                            <option value="3" {{ $user->ISF == 3 ? 'selected' : '' }}>Iasi</option>
                            <option value="4" {{ $user->ISF == 4 ? 'selected' : '' }}>Timisoara</option>
                            <option value="5" {{ $user->ISF == 5 ? 'selected' : '' }}>Brasov</option>
                            <option value="6" {{ $user->ISF == 6 ? 'selected' : '' }}>Craiova</option>
                        </select>
                    </td>
                    <td>
                        <select name="is_admin" id="is_admin">
                            <option value="0" {{ $user->is_admin == 0 ? 'selected' : '' }}>No</option>
                            <option value="1" {{ $user->is_admin == 1 ? 'selected' : '' }}>Yes</option>
                        </select>
                    </td>
                </tr>
                <tr style="border-top: 1px solid black;"></tr>
            @endforeach
            <form action="{{ route('users.update') }}" method="POST">
                @csrf
                <table>
                    <!-- Your table content -->
                </table>
                <button type="submit" class="btn btn-primary">Post</button>
            </form>
        </tbody>
    </table>
</div>



</x-app-layout>