<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-size: 6px;
            font-family: Deja<PERSON>u <PERSON>s, sans-serif;
            /* box-sizing: border-box; */
        }

        @page {
            margin: 0;
            size: 92mm 60mm;
        }

        .container {
            width: 91.8mm;
            margin: 0.1mm;
        }

        .header {
            text-align: center;
            font-size: 7px;
            font-weight: bold;
            margin-bottom: 0.1mm;
        }

        .title {
            text-align: center;
            font-size: 7px;
            font-weight: bold;
            margin-bottom: 3mm;
        }

        .big-title {
            font-size: 9px;
        }

        .details {
            text-align: center;
            font-size: 8px;
            margin-bottom: 0.1mm;
        }

        .line {
            margin-bottom: 0.1mm;
        }

        .line-bolded {
            font-weight: bold;
        }

        .lines {
            width: 100%;
            height: 1.5mm;
            margin-bottom: 1mm;
        }

        .line-color {
            width: 100%;
            height: 0.5mm;
        }

        .red {
            background-color: red;
        }

        .yellow {
            background-color: yellow;
        }

        .blue {
            background-color: blue;
        }

        .table-container th,
        .table-container td {
            font-size: 5px;
            border: 1px solid #000;
            /* padding: 8px; */
            text-align: center;
        }

        .centered {
            text-align: center;
        }

        .centered-img {
            margin-left: auto;
            margin-right: auto;
            vertical-align: middle;
        }

        .content-cell {
            text-align: center;
            vertical-align: middle;
        }

        .image-overlay-wrapper {
            width: 100%;
            height: auto;
            text-align: center;
            position: relative;
            padding-top: 5mm;
            margin-bottom: 2mm;
        }

        .image-overlay-inner {
            position: relative;
            width: 100%;
            /* extinde pe toată pagina */
            max-width: 100%;
        }

        .overlay-image {
            width: 100%;
            /* imaginea va acoperi întreaga lățime */
            opacity: 0.9;
        }

        .overlay-text {
            position: absolute;
            top: 25%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            font-size: 9px;
            font-weight: bold;
            color: black;
            text-align: center;
            padding-top: 5mm;
        }

        .under-text {
            text-align: center;
            font-weight: bold;
            font-size: 9px;
            margin-top: 1mm;
        }

        .valabilitati-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1mm;
            /* spațiu între chenare */
            margin-top: 2mm;
        }

        .valabilitate-box {
            border: 1px solid #000;
            font-size: 6px;
            padding: 2mm;
            width: 40mm;
            text-align: center;
            font-weight: bold;
        }


        .vize-container {
            width: 100%;
            border-collapse: collapse;
            margin-top: 2mm;
        }

        .vize-container th {
            font-size: 6px;
            border: 1px solid #000;
            padding: 2mm;
            text-align: center;
            width: 16%;
            /* 6 coloane => aprox. 100/6 */
            box-sizing: border-box;
        }
    </style>
</head>

@php
    use Carbon\Carbon;

    Log::info('comisie' . $comisie);

    $dataLimita = Carbon::createFromFormat('Y-m-d H:i:s', '2025-06-03 00:00:00');
    $dataEmitere = NULL;
    $afiseazaSemnatura = NULL;

    if (in_array(strtolower($solicitareCurenta->tip_comisie), ['vizeperiodice', 'duplicate'])) {
        $dataEmitere = Carbon::parse($solicitareCurenta->data_reemiterii);
        if($dataEmitere->greaterThan($dataLimita)){
            $afiseazaSemnatura = true;
        }else
            $afiseazaSemnatura = false;
    } elseif (strtolower($solicitareCurenta->tip_comisie) === 'examinare' || strtolower($solicitareCurenta->tip_comisie) === 'reprogramare') {
        $dataEmitere = Carbon::parse($comisie->data_exam_t1);
        if($dataEmitere->greaterThan($dataLimita)){
            $afiseazaSemnatura = true;
        }else
            $afiseazaSemnatura = false;
    } else {
        $dataEmitere = Carbon::parse($solicitareCurenta->data_elib);
        if($dataEmitere->greaterThan($dataLimita)){
            $afiseazaSemnatura = true;
        }else
            $afiseazaSemnatura = false;
    }

    Log::info('data emitere' . $dataEmitere);
    // Format the date directly from the Carbon object
    $formattedDataEmitere = $dataEmitere->format('d/m/Y');

    // Collect all valid dates in order (including nulls)
    $valabilitati = [
        $solicitareCurenta->data_val1 ?? null,
        $solicitareCurenta->data_val2 ?? null,
        $solicitareCurenta->data_val3 ?? null,
        $solicitareCurenta->data_val4 ?? null,
        $solicitareCurenta->data_val5 ?? null,
        $solicitareCurenta->data_val6 ?? null,
    ];
    if (empty(array_filter($valabilitati))) {
        $valabilitati[0] = $solicitareCurenta->valabilitate ?? null;
    }
    // Ensure exactly 6 elements (fill missing ones with empty strings)
    $valabilitati = array_slice(array_pad($valabilitati, 6, null), 0, 6);

    // Format dates, leave empty boxes for missing values
    $valabilitatiFormatted = array_map(
        function ($date, $index) {
            if (!$date) {
                return '';
            }
            return $index > 0
                ? Carbon::parse($date)
                    ->subDays(0)
                    ->format('d/m/Y')
                : Carbon::parse($date)->format('d/m/Y');
        },
        $valabilitati,
        array_keys($valabilitati),
    );

@endphp

<body>
    <table class="container">
        <tr>
            <td class="header">
                MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII<br>
                Autoritatea de Siguranță Feroviară Română - ASFR
            </td>
        </tr>
        <tr>
            <td class="lines">
                <div class="line-color red"></div>
                <div class="line-color yellow"></div>
                <div class="line-color blue"></div>
            </td>
        </tr>
        <tr>
            <td class="title">
                <span class="big-title">AUTORIZAȚIE</span><br>
                @if (isset($solicitareCurenta->domeniu))
                    pentru {{ $solicitareCurenta->domeniu }}<br>
                    {{ $solicitareCurenta->functie }}
                @else
                    PENTRU EXERCITAREA FUNCȚIEI<br>
                    {{ $solicitareCurenta->functie }}
                @endif
            </td>
        </tr>
        <tr>
            <td class="details">
                <div class="line">Serie {{ $solicitareCurenta->serie_aut }} &nbsp;&nbsp;&nbsp;&nbsp; Număr
                    {{ $solicitareCurenta->nr_aut }} &nbsp;&nbsp;&nbsp;&nbsp; Data
                    emiterii {{ $formattedDataEmitere }}</div>
                <div class="line-bolded">{{ $solicitareCurenta->solicitant_nume }}
                    {{ $solicitareCurenta->solicitant_prenume }}</div>
                <div class="line">CNP {{ $solicitareCurenta->cnp }}</div>
            </td>
        </tr>
        <tr>
            <td>
                <table style="width:100%; border:none; table-layout: fixed;">
                    <tr>
                        <td class="content-cell">
                            <img class='centered' width="60px" src="{{ asset('ASFR-logo.png') }}" />
                        </td>
                        <td class="content-cell">
                            <div class="line-bolded centered">DIRECTOR GENERAL,</div>
                            <div class="line centered">Petru BOGDAN</div>
                            @if ($afiseazaSemnatura)
                                <img class='centered' width="80px" src="{{ asset('Semnatura-PetruBogdan.png') }}" />
                            @else
                                <img class='centered' width="40px" src="{{ asset('Semnatura-PetruBogdan2.png') }}" />
                            @endif
                        </td>
                        <td class="content-cell">
                            <img class='centered' width="60px" src="{{ asset('ASFR-Stampila.png') }}" />
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <div class="image-overlay-wrapper">
        <div class="image-overlay-inner"><img src="{{ asset('ASFR-logo.png') }}" alt="logo" class="overlay-image"
                style="width: 30.6mm;">
        </div>
        <div class="overlay-text">
            @if (isset($solicitareCurenta->domeniu))
                Autorizatia dă dreptul titularului să efectueze pe propria raspundere operațiile specifice altor
                funcții/să manipuleze tipurile de instalații de siguranța circulației/să efectueze activitățile
                specifice în activitatea de manevră și/sau de conducere a trenurilor, înscrise în autorizație.
                Prezenta autorizație este valabilă pe perioadă nelimitată, în condițiile respectării prevederilor care
                au stat la baza eliberării acesteia și a vizării sale la 5 ani.
            @else
                Autorizația dă dreptul titularului să efectueze pe propria
                răspundere activitățile specifice funcției.
                Prezenta autorizație este valabilă pe perioadă nelimitatä, în condițiile respectärii prevederilor care
                au stat la baza eliberării acesteia si a vizării sale la 5 ani.
            @endif
        </div>
    </div>
    @if (in_array(strtolower($solicitareCurenta->tip_comisie), ['duplicate']))
        <div class="under-text">DUPLICAT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;VIZE PERIODICE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ $solicitareCurenta->nr_aut }}</div>
    @else
        <div class="under-text">VIZE PERIODICE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ $solicitareCurenta->nr_aut }}</div>
    @endif
    <table class="vize-container">
        <thead>
            <tr>
                @foreach ($valabilitatiFormatted as $date)
                    <th>Valabilă până la:<br />{{ $date }}</th>
                @endforeach
            </tr>
        </thead>
    </table>


</body>

</html>
