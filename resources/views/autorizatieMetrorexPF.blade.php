<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <style>
        
        body {
            font-size: 7px;
            font-family: Deja<PERSON>u Sans, sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @page {
            margin: 0;
            size: 92mm 60mm;
        }



        .metrorex-fata-container {
            position: relative;
            border: 1px solid #ccc;
        }

        .metrorex-fata {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .metrorex-fata img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .metrorex-verso-container {
            position: relative;
            border: 1px solid #ccc;
            overflow: hidden;
        }

        .metrorex-verso {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .metrorex-verso img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .serie-overlay {
            position: absolute;
            bottom: 135px;
            left: 0;
            width: 100%;
            color: black;
            padding: 5px;
            padding-left: 68px;
            font-size: 10px;

        }

        .numar-overlay {
            position: absolute;
            bottom: 135px;
            left: 0;
            width: 100%;
            color: black;
            padding: 5px;
            padding-left: 135px;
            font-size: 10px;
        }

        .nume-overlay {
            position: absolute;
            bottom: 112px;
            left: 0;
            width: 100%;
            color: black;
            padding: 5px;
            padding-left: 25px;
            font-size: 10px;

        }
        

        .functie-overlay {
            position: absolute;
            bottom: 75px;
            left: 0;
            width: 100%;
            color: black;
            padding: 5px;
            padding-left: 25px;
            font-size: 9px;
        }

        .data-overlay {
            position: absolute;
            bottom: 54px;
            left: 0;
            width: 100%;
            color: black;
            padding: 5px;
            padding-left: 95px;
            font-size: 8px;

        }

        .poza-overlay {
            position: absolute;
            bottom: 66px;
            left: 0;
            color: black;
            padding: 5px;
            padding-left: 268px;
            font-size: 10px;
        }

        .semnatura-overlay {
            position: absolute;
            bottom: 0px;
            left: 0;
            color: black;
            padding: 1px;
            padding-left: 143px;
            font-size: 10px;
        }

        .semnatura-overlay2 {
            position: absolute;
            bottom: 0px;
            left: 0;
            color: black;
            padding: 1px;
            padding-left: 153px;
            font-size: 10px;
        }
    </style>
</head>

<?php
$text = $personal_metrorex->functie;
$limit = 50; // lungimea maximă pe linie

// Împărțim textul la cel mai apropiat spațiu înainte de limită
$wrappedText = wordwrap($text, $limit, "\n", true);
$lines = explode("\n", $wrappedText);

// Verificăm dacă avem două linii
$firstLine = $lines[0] ?? '';
$secondLine = $lines[1] ?? '';


$formattedNrPermis = str_pad($personal_metrorex->nr_permis, 4, '0', STR_PAD_LEFT);
?>

<body>
    <div class="metrorex-fata-container">
        <div class="metrorex-fata">
            <img src="{{ asset('metrorexFata.png') }}" alt="Fata" />
            <div class="serie-overlay">{{ $functie_metrorex->tip_aut }}</div>
            <div class="numar-overlay">{{ $formattedNrPermis }}</div>
            <div class="nume-overlay">{{ $personal_metrorex->nume }} {{ $personal_metrorex->prenume }}</div>
            <div class="functie-overlay">
                {{ $firstLine }}<br>
                <span>{{ $secondLine }}</span>
            </div>
            <div class="data-overlay">
                {{ \Carbon\Carbon::parse($personal_metrorex->data_pv)->addYears(10)->format('d-m-Y') }}</div>
            <img class="poza-overlay" src="{{ asset($imagePath) }}" alt="Photo" style="width: 61px; height: 83px;" />

            <img class="semnatura-overlay" src="{{ asset('Semnatura-PetruBogdan2.png') }}" alt="Semnatura"
                style="width: 35px; height: 35px;" />

        </div>
    </div>

    <div class="metrorex-verso-container">
        <div class="metrorex-verso">
            <img src="{{ asset('metrorexVerso.png') }}" alt="Verso" />
        </div>
    </div>
</body>
</html>
