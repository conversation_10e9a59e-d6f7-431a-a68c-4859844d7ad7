<!DOCTYPE html>
<html lang="ro">


<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-size: 12px;
            font-family: <PERSON><PERSON><PERSON><PERSON>, sans-serif;
            margin-left: 70px;
            margin-right: 70px;
        }

        .container {
            display: flex;
        }

        .text {
            flex: 1;
        }

        .image {
            margin-right: 10px
        }

        .bold {
            font-weight: bold;
        }

        .head {
            text-align: center;
            align-content: center;
            font-weight: bold;
            font-size: 14px;
            line-height: 17px;
        }

        .centered {
            text-align: center;
            align-content: center;
            font-size: 12px;
            margin-top: -9px;
        }

        .text {
            text-align: justify;
            font-size: 12px;
            width: 8in;
        }

        .ticket {
            width: 8in;
        }

        .logo {
            width: 8in;
        }

        img {
            max-width: inherit;
            width: inherit;
        }


        .center {
            margin-left: auto;
            margin-right: auto;
        }



        .bottom-item {
            width: 30%;
        }

        .img-fit {
            width: 100%;
            height: 100px;
            max-width: 100%;
        }

        @page {
            margin: 0.1in;
            size: 16.54in 11.69in;
        }


        .table-solicitari {
            border: 0.75px solid #000000;
            border-collapse: collapse;
            word-wrap: break-word;
            /* Împarte cuvintele prea lungi */
            overflow: hidden;
            /* Ascunde orice text care depășește */
        }

        .table-antet {
            width: 100%;
            border-collapse: collapse;
        }



        .th-solicitari {
            border: 0.75px solid #000000;
            padding: 3px;
            height: auto;
            text-align: center;

        }

        .td-solicitari {
            border: 0.75px solid #000000;
            padding: 3px;
            height: auto;
            text-align: center;

        }

        .td-antet {
            padding: 0;
            text-align: center;
            line-height: 1;

        }

        .paragraph {
            margin-bottom: 5px;
        }

        .bottom-items-table {
            margin-top: 20px;
            width: 100%;
            table-layout: fixed;
            border: none;
        }

        .bottom-item {
            display: table-cell;
            vertical-align: top;
            padding: 10px;
            border: none;
        }

        .bottom-item p {
            margin: 0;
        }

        .table-container2 {
            width: 100%;
            /* Latimea unei pagini A4 */
            margin: 0 auto;
            /* Centreaza containerul pe pagina */
            padding: 8px;
            /* Adaugă puțin spațiu între container și marginea paginii */
            box-sizing: border-box;
        }

        td.first-column {
            width: 120px;
            height: 20px;
            padding: 0;
            /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1;
            /* Reducem line-height pentru a apropia textul */
        }

        .tr-height {
            height: 2px;
            padding: 0;
            /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1;
            /* Reducem line-height pentru a apropia textul */
        }


        .line-color {
            width: 100%;
            height: 0.5mm;
            margin-bottom: 1mm;
            margin-top: 1mm;
            background-color: black;
        }

    </style>
</head>

<body>

    @foreach ($solicitari as $key => $solicitant)
        @php
            $tipuriReemitere = ['vizeperiodice', 'duplicate', 'reprogramare', 'schimbarenume'];

            $dataElibUsed1 = (
                strtolower($solicitant->tip_comisie) === 'reprogramare' && !empty($comisie->data_exam_t1)
            ) ? $comisie->data_exam_t1
            : (in_array(strtolower($solicitant->tip_comisie), $tipuriReemitere)
                ? $solicitant->data_reemiterii
                : $comisie->data_exam_t1);
        
            
            Log::info('da1: ' . $solicitari);
            $dataEliberare1 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataElibUsed1)->format('d/m/Y');
            Log::info('data stocata: ' . $dataEliberare1);

            $dataValabilitate1 = \Carbon\Carbon::createFromFormat('Y-m-d', $solicitant->valabilitate)->format('d/m/Y');

        @endphp
    @endforeach
    @php
        $dataRedactat = \Carbon\Carbon::parse($comisie->data_ora_redactat)->format('d-m-Y');
        $data2 = \Carbon\Carbon::parse($solicitant->data_reemiterii)->format('d-m-Y');
        Log::info('comisie: ' . $comisie->nr_ISF . ' si nr_comisie: ' . $comisie->nr_comisie);
    @endphp
    <header>
        <div class="table-container2">
            <table class="table-antet">
                <tr>
                    <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}"
                            alt="Image"></td>
                    <td class="tr-height td-antet ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
                </tr>
                <tr>
                    <td class="tr-height td-antet ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
                </tr>
                <tr>
                    <td class="tr-height td-antet ">București, Calea Griviței, nr. 393, sector 1</td>
                </tr>
                <tr>
                    <td class="tr-height td-antet ">Cod Poștal: 010719, Cod Fiscal: 48008564,</td>
                </tr>
                <tr>
                    <td class="tr-height td-antet ">E-mail:
                        isf.{{ strtr(strtolower($isf->ISF), [
                            'ă' => 'a',
                            'â' => 'a',
                            'ș' => 's',
                            'ț' => 't',
                            'î' => 'i',
                            'Ă' => 'A',
                            'Â' => 'A',
                            'Ș' => 'S',
                            'Ț' => 'T',
                            'Î' => 'I',
                        ]) }}@sigurantaferoviara.ro
                        www.sigurantaferoviara.ro</td>
                </tr>
            </table>
            <div class="line-color"></div>
        </div>
    </header>

    <p class="bold paragraph">
        <span class="bold">
            @if ($isf->ISF === 'București' || $isf->ISF === 'Brașov')
                Compartimentul Inspectorat de Siguranță Feroviară
            @else
                Inspectoratul de Siguranță Feroviară
            @endif
            {{ $isf->ISF }}
        </span> - <span class="bold">C{{$comisie->nr_comisie}}</span>
    </p>
    <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        @if ($isf->ISF === 'Brașov' || $isf->ISF === 'București' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați')
            <span class="bold">Coordonator CISF</span>
        @else
            <span class="bold">Inspector Șef Teritorial</span>
        @endif
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
            class="bold">{{ $isf->sef_ISF }}</span>
    </p>
    <p></p>
    <p></p>
    <p></p>
    <p class="centered">
        @if ($solicitari[0]->tip_comisie === 'VizePeriodice')
            Vize acordate de @if ($isf->ISF === 'Brașov' || $isf->ISF === 'București' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați')
                CISF
            @else
                ISF
            @endif {{ $isf->ISF }} cu act nr. {{ $comisie->nr_ISF }} din data
            {{ $dataRedactat }}

            la solicitarea {{ $solicitari[0]->unitate }}
        @else
            Autorizaţii emise ca urmare a examinării stabilite de
            @if ($isf->ISF === 'Brașov' || $isf->ISF === 'București' || $isf->ISF === 'Craiova' || $isf->ISF === 'Galați')
                CISF
            @else
                ISF
            @endif {{ $isf->ISF }} - {{ $comisie->nr_ISF }}/{{ $dataRedactat }}
            <p class="centered">în conformitate cu rezultatele consemnate în PV {{ $comisie->pv_nr_iesire }} din data
                {{ $dataEliberare1 }}</p>
            <p class="centered">solicitant {{ $solicitant->unitate }}</p>
        @endif
    </p>
    <p></p>
    <table cellspacing="0" cellpadding="0" class="center table-solicitari">
        <thead>
            <tr colspan='22'>
                <th class="th-solicitari" colspan='1' rowspan="2">Nr.Crt</th>
                <th class="th-solicitari" colspan='2' rowspan="2">Nume</th>
                <th class="th-solicitari" colspan='3' rowspan="2">CNP</th>
                <th class="th-solicitari" colspan='4' rowspan="2">Funcție</th>
                <th class="th-solicitari" colspan='12'>Autorizație</th>
            </tr>
            <tr>
                <th class="th-solicitari" colspan='4'>Tip</th>
                <th class="th-solicitari" colspan='4'>Domeniu</th>
                <th class="th-solicitari" colspan='1'>Serie</th>
                <th class="th-solicitari" colspan='1'>Numar</th>
                <th class="th-solicitari" colspan='1'>Data emiterii</th>
                <th class="th-solicitari" colspan='1'>Valabilitate</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($solicitari as $key => $solicitant)
                @php
                    $tip = strtolower($solicitant->tip_comisie);
                    $dataElibUsed = in_array($tip, ['vizeperiodice', 'schimbarenume'])
                        ? $solicitant->data_reemiterii
                        : $comisie->data_exam_t1;
                        
                    $dataEliberare =
                        strtolower($solicitant->tip_comisie) === 'duplicate' ? \Carbon\Carbon::createFromFormat('Y-m-d', $solicitant->data_reemiterii)->format('d/m/Y') : \Carbon\Carbon::createFromFormat('Y-m-d', $dataElibUsed)->format('d/m/Y');
                    // $dataEliberare = \Carbon\Carbon::createFromFormat('Y-m-d', $dataElibUsed)->format('d/m/Y');
                    $dataValabilitate = \Carbon\Carbon::createFromFormat('Y-m-d', $solicitant->valabilitate)->format('d/m/Y');

                @endphp
                <tr>
                    <td class="bold td-solicitari" colspan='1'>{{ (int) $key + 1 }}</td>
                    <td class="bold td-solicitari" colspan='2'>{{ $solicitari[$key]->solicitant_nume }}
                        {{ $solicitari[$key]->solicitant_prenume }}</td>
                    <td class="bold td-solicitari" colspan='3'>{{ $solicitari[$key]->cnp }}</td>
                    <td class="td-solicitari" colspan='4'>{{ $solicitari[$key]->functie }}</td>
                    <td class="td-solicitari" colspan='4'>{{ $solicitari[$key]->autorizatie }}</td>
                    <td class="td-solicitari" colspan='4'>{{ $solicitari[$key]->domeniu }}</td>
                    <td class="td-solicitari" colspan='1'>{{ $solicitari[$key]->serie_aut }}</td>
                    <td class="td-solicitari" colspan='1'>{{ $solicitari[$key]->nr_aut }}</td>
                    <td class="td-solicitari" colspan='1'>{{ $dataEliberare }}</td>
                    <td class="td-solicitari" colspan='1'>{{ $dataValabilitate }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
    @if ($solicitari[0]->tip_comisie === 'VizePeriodice')
        <p><span class="bold">NOTĂ</span><br>1. Sunt îndeplinite cerințele privind acordarea vizei periodice.<br>2.
            Până
            la eliberarea autorizației vizate prezentul document dă dreptul posesorului autorizației să exercite pe
            propria
            raspundere funcția/activitățile pentru care a fost autorizat.</p>
    @else
        <p><span class="bold">NOTĂ</span> 1. Până la data emiterii de către ASFR a autorizației/autorizațiilor, care au făcut obiectul examinării teoretice și practice personalul declarat “Corespunzător” poate exercita funcția, sau, după caz, să manipuleze tipurile de instalații SC și/sau să desfășoare activitățile specifice pentru care a fost autorizat conform prezentului proces verbal.</p>
    @endif


    <p></p>
    <p></p>
    <p></p>
    <p></p>
    <p></p>
    <p></p>
    <p class="centered">Întocmit de</p>
    <p class="centered">{{ $comisie->redactat }}</p>

</body>

</html>
