@props(['menus', 'dateTabel', 'columns'])

<x-app-layout>
    <x-slot name="header">
        <nav
            class="flex justify-center items-center flex-wrap gap-1 bg-gray-200 dark:bg-gray-800 p-4 font-semibold text-xl dark:text-white text-black dark:text-gray-200 leading-tight">
            @foreach ($menus as $menu)
                <x-custom-dropdown align="left" width="48" contentClasses="py-1 bg-white dark:bg-gray-800"
                    :menuObj="$menu">
                </x-custom-dropdown>
            @endforeach
        </nav>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <div id='fields-nr-iesire' class='flex flex-row align-center'>
                <button type="button" id="btnEmitereAutorizatie"
                    class="hidden text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Emitere
                    autorizație</button>
                <button type="button" id="btnEditareAutorizatiiSpeciale"
                    class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Editează Autorizație</button>
            </div>
        </div>
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            @if (isset($columns) && isset($dateTabel))
                <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3 sticky">Action</th>
                                @foreach ($columns as $colKey => $colLabel)
                                    @if($colKey !== 'id')
                                        <th scope="col" class="px-6 py-3">{{ $colLabel }}</th>
                                    @endif
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($dateTabel as $dataRow)
                                @if($colLabel !== 'id')
                                    <tr
                                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                        <td class="px-6 py-4 sticky">
                                            <input type="radio" name="row-radio" class="row-radio"
                                                data-row-id="{{ $dataRow->id }}"
                                                data-tip-autorizatie="{{ $dataRow->tip_autorizatie }}" 
                                                data-nr-serie="{{ $dataRow->nr_serie }}"
                                                data-nume="{{ $dataRow->nume }}"
                                                data-prenume="{{ $dataRow->prenume }}"
                                                data-functie="{{ $dataRow->functie }}"
                                                data-unitate="{{ $dataRow->unitate }}"
                                                data-ministru="{{ $dataRow->ministru }}"
                                                data-data-emitere="{{ $dataRow->data_emitere }}"
                                                data-data-valabilitate="{{ $dataRow->data_valabilitate }}"
                                                data-poza="{{ $dataRow->poza }}">
                                        </td>
                                        @foreach ($columns as $colKey => $colLabel)
                                            @if($colKey !== 'id')
                                                <td class="px-6 py-4" data-column="{{ $colKey }}">
                                                    {{ $dataRow->$colKey }}
                                                </td>
                                            @endif
                                        @endforeach
                                    </tr>
                                @endif
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $dateTabel->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dataRadios = document.querySelectorAll('.row-radio');

        const btnEmitereAutorizatie = document.getElementById('btnEmitereAutorizatie');
        const btnEditareAutorizatiiSpeciale = document.getElementById('btnEditareAutorizatiiSpeciale');


        let selectedRowIdAutorizatiiSpeciale = null;
        let selectedRowTipAutorizatiiSpeciale = null;
        let selectedRowNrSerieAutorizatiiSpeciale = null;
        let selectedRowNumeAutorizatiiSpeciale = null;
        let selectedRowPrenumeAutorizatiiSpeciale = null;
        let selectedRowFunctieAutorizatiiSpeciale = null;
        let selectedRowUnitateAutorizatiiSpeciale = null;
        let selectedRowMinistruAutorizatiiSpeciale = null;
        let selectedRowDataEmitereAutorizatiiSpeciale = null;
        let selectedRowDataValabilitateAutorizatiiSpeciale = null;
        let selectedRowPozaAutorizatiiSpeciale = null;


        const updateButtonVisibility = () => {
            const anyChecked = Array.from(dataRadios).some(radio => radio.checked);


            if(anyChecked) {
                btnEmitereAutorizatie.classList.remove('hidden');
                btnEditareAutorizatiiSpeciale.classList.remove('hidden');
                const radio = document.querySelector('.row-radio:checked');
                selectedRowIdAutorizatiiSpeciale = radio.getAttribute('data-row-id');
                selectedRowTipAutorizatiiSpeciale = radio.getAttribute('data-tip-autorizatie');
                selectedRowNrSerieAutorizatiiSpeciale = radio.getAttribute('data-nr-serie');
                selectedRowNumeAutorizatiiSpeciale = radio.getAttribute('data-nume');
                selectedRowPrenumeAutorizatiiSpeciale = radio.getAttribute('data-prenume');
                selectedRowFunctieAutorizatiiSpeciale = radio.getAttribute('data-functie');
                selectedRowMinistruAutorizatiiSpeciale = radio.getAttribute('data-ministru');
                selectedRowDataEmitereAutorizatiiSpeciale = radio.getAttribute('data-data-emitere');
                selectedRowDataValabilitateAutorizatiiSpeciale = radio.getAttribute('data-data-valabilitate');
                selectedRowPozaAutorizatiiSpeciale = radio.getAttribute('data-poza');
            } else {
                btnEmitereAutorizatie.classList.add('hidden');
                btnEditareAutorizatiiSpeciale.classList.add('hidden');

            }

        }
        dataRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                updateButtonVisibility();
            });
        });

        btnEditareAutorizatiiSpeciale.addEventListener('click', function() {
            if (selectedRowIdAutorizatiiSpeciale !== null){
                window.location.href = `/autorizatiiSpeciale/edit/${selectedRowIdAutorizatiiSpeciale}`;
            }
        });

        btnEmitereAutorizatie.addEventListener('click', () => {
            const radio = document.querySelector('.row-radio:checked');

            selectedRowId = radio.getAttribute('data-row-id');

            if (selectedRowId) {
                fetch(`/generateAutorizatieSpecial/${selectedRowId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Success:', data);
                        // Decode the base64 PDF content
                        const pdfContent = atob(data.pdf);
                        // Convert the decoded content to an array buffer
                        const arrayBuffer = new Uint8Array([...pdfContent].map(char => char
                            .charCodeAt(0))).buffer;
                        // Create a Blob from the array buffer
                        const pdfBlob = new Blob([arrayBuffer], {
                            type: 'application/pdf'
                        });
                        // Create a URL for the Blob
                        const pdfUrl = URL.createObjectURL(pdfBlob);
                        // Create a link element to trigger the download
                        const link = document.createElement('a');
                        link.href = pdfUrl;
                        link.download = 'autorizatieSpecial.pdf';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);


                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            }
        })
    })
</script>
