@props([
    'align' => 'right',
    'width' => '48',
    'contentClasses' => ' bg-white dark:bg-gray-700',
    'menuObj',
])

@php

    switch ($align) {
        case 'left':
            $alignmentClasses = 'ltr:origin-top-left rtl:origin-top-right start-0';
            break;
        case 'top':
            $alignmentClasses = 'origin-top';
            break;
        case 'right':
        default:
            $alignmentClasses = 'ltr:origin-top-right rtl:origin-top-left end-0';
            break;
    }

    switch ($width) {
        case '48':
            $width = 'w-48';
            break;
    }
@endphp

<div class="flex items-left justify-center bg-gray-100 rounded-md mx-1 z-40">
    <div x-data="{ open: false }" class="relative inline-block text-left">
        <button @click="open = !open"
            class="inline-flex justify-center w-full px-1 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-gray-100 focus:ring-blue-500 break-all">
            {{ $menuObj['title'] }}
            @if (isset($menuObj['submenu']))
                <svg class="h-5 w-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            @endif
        </button>

        <div x-show="open" @click.away="open = false"
            class="{{ $alignmentClasses }} {{ $width }}  bg-white origin-top-left absolute left-0 mt-1 rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
            @if (isset($menuObj['submenu']))
                @foreach ($menuObj['submenu'] as $submenu)
                    <div x-data="{ subMenuOpen: false }" class="relative ">
                        @php
                            $hasSubMenu = isset($submenu['submenu']);
                        @endphp
                        @if ($hasSubMenu)
                            <button @click="subMenuOpen = !subMenuOpen"
                                class="flex justify-between items-center w-full px-1 py-1 text-sm text-gray-700 bg-white hover:bg-gray-100 relative rounded-md break-all">
                                {{ $submenu['title'] }}
                                <svg class="h-5 w-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7">
                                    </path>
                                </svg>
                            </button>
                        @else
                            <a href="{{ $submenu['url'] }}"
                                class="flex justify-between items-center w-full px-1 py-1 text-sm text-gray-700 bg-white hover:bg-gray-100 relative rounded-md break-all">
                                {{ $submenu['title'] }}
                            </a>
                        @endif

                        @if ($hasSubMenu)
                            <div x-show="subMenuOpen" @click.away="subMenuOpen = false"
                                class="absolute top-0 left-full w-48 mt-0 rounded-md shadow-lg  bg-white ring-1 ring-black ring-opacity-5">
                                @foreach ($submenu['submenu'] as $subsubmenu)
                                    <div x-data="{ subSubMenuOpen: false }" class="relative">
                                        @php
                                            $hasSubSubMenu = isset($subsubmenu['submenu']);
                                        @endphp
                                        @if ($hasSubSubMenu)
                                            <button @click="subSubMenuOpen = !subSubMenuOpen"
                                                class="flex justify-between items-center w-full px-1 py-1 text-sm text-gray-700 bg-white hover:bg-gray-100 relative rounded-md break-all">
                                                {{ $subsubmenu['title'] }}
                                                <svg class="h-5 w-5 ml-2" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M9 5l7 7-7 7">
                                                    </path>
                                                </svg>
                                            </button>
                                        @else
                                            <a href="{{ $subsubmenu['url'] }}"
                                                class="block px-1 py-1 text-sm text-gray-700 hover:bg-gray-100 break-all">
                                                {{ $subsubmenu['title'] }}
                                            </a>
                                        @endif

                                        @if ($hasSubSubMenu)
                                            <div x-show="subSubMenuOpen" @click.away="subSubMenuOpen = false"
                                                class="absolute top-0 left-full w-48 mt-0 rounded-md shadow-lg  bg-white ring-1 ring-black ring-opacity-5">
                                                @foreach ($subsubmenu['submenu'] as $subsubsubmenu)
                                                    <a href="{{ $subsubsubmenu['url'] }}"
                                                        class="block px-1 py-1 text-sm text-gray-700 hover:bg-gray-100 break-all">
                                                        {{ $subsubsubmenu['title'] }}
                                                    </a>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @endforeach
            @endif
        </div>
    </div>
</div>
