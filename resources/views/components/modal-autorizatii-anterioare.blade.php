<!-- Modal toggle -->
<button data-modal-target="modal-autorizatii" data-modal-toggle="modal-autorizatii"
    class="type3Hidden type3Reautorizare block text-white bg-blue-700 mb-6 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
    id='buton-afisare-modal-autorizatii' type="button">
    Alege autorizații
</button>

<!-- Main modal -->
<div id="modal-autorizatii" tabindex="-1" aria-hidden="true"
    class="modal-overlay hidden fixed top-0 right-0 left-0 z-50 justify-center items-center">
    <div class="relative p-4 w-full max-w-[80%]">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700 flex flex-col h-[90vh]">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Alegere autorizații
                </h3>
                <button type="button" id="autorizatii-modal-close"
                    class="modal-close-button text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-toggle="modal-autorizatii">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="modal-body-autorizatii p-4 md:p-5 overflow-auto flex-grow">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <!-- Columns will be dynamically inserted here -->
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Rows will be dynamically inserted here -->
                    </tbody>
                </table>
            </div>
            <button type="button" id="autorizatii-confirm-button"
                class="text-white w-[30%] m-6 self-end inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 mt-4 disabled:bg-gray-400 disabled:cursor-not-allowed"
                disabled>
                Confirmă alegerea
            </button>
        </div>
    </div>
</div>

<!-- Table that will be shown after Confirm button is clicked -->
<div id="autorizatii-table" class="hidden mt-4 mb-8 overflow-x-auto">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <!-- Columns will be dynamically inserted here -->
            </tr>
        </thead>
        <tbody>
            <!-- The confirmed rows will be inserted here -->
        </tbody>
    </table>
</div>



<script>
    document.addEventListener('DOMContentLoaded', function() {

    });
</script>

<style>
    .text-white {
        color: #fff;
    }



    .selected-row {
        background-color: #10B981;
        /* Tailwind green-500 */
        color: #FFFFFF;
        /* White text */
    }
</style>
