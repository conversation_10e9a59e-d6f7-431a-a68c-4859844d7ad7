<!-- Modal toggle -->
<button data-modal-target="modal-autorizatie" data-modal-toggle="modal-autorizatie"
    class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
    type="button">
    Alegere tip de autorizatie
</button>

<!-- Main modal -->
<div id="modal-autorizatie" tabindex="-1" aria-hidden="true"
    class="modal-overlay hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-[80%] max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700 h-[80vh] flex flex-col">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Alegere tip autorizatie
                </h3>
                <button type="button"
                    class="modal-close-button text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-toggle="modal-autorizatie">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <form class="p-4 md:p-5 w-full overflow-y-auto flex-grow">
                <x-tree-view :functions='$functions' />
            </form>
            <button type="submit" id="submit-button"
                class="text-white w-[30%] m-6 self-end inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 mt-4">
                Confirmă alegerea
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const submitButton = document.getElementById('submit-button');
        const treeItems = document.querySelectorAll('.tree-item');
        let dataIds = []; // Variable to store the selected data-ids

        // Function to reset the dataIds variable and clear the highlight class
        function resetDataIds() {
            dataIds = [];
            treeItems.forEach(item => item.classList.remove('highlight'));
        }

        // Function to update the submit button status
        function updateSubmitButtonStatus() {
            // Check if any tree item has the highlight class
            const hasHighlight = Array.from(treeItems).some(item => item.classList.contains('highlight'));
            // Enable/disable the submit button accordingly
            submitButton.disabled = !hasHighlight;
        }

        // Function to traverse the DOM tree upwards and collect data-id attributes
        function collectDataIds(element) {
            const collectedDataIds = [];
            while (element) {
                const dataId = element.getAttribute('data-id');
                if (dataId) {
                    collectedDataIds.push(dataId);
                }
                element = element.parentElement.closest('li');
            }
            return collectedDataIds.reverse(); // Reverse the array to maintain the order from root to leaf
        }

        // Listen for click events on tree items to update the submit button status and collect data-ids
        treeItems.forEach(function(item) {
            item.addEventListener('click', function() {
                // Check if the clicked item has an arrow-placeholder or a caret
                const arrowPlaceholder = this.querySelector('.arrow-placeholder');
                const isEndItem = !!arrowPlaceholder;

                if (isEndItem) {
                    // Clear the highlight class from previously highlighted items
                    treeItems.forEach(item => item.classList.remove('highlight'));

                    // Toggle highlight class for the clicked item
                    this.classList.toggle('highlight');

                    // Collect data-ids of the selected item and its parents
                    dataIds = collectDataIds(this);

                    // Update submit button status
                    updateSubmitButtonStatus();

                    console.log('Data IDs:', dataIds);
                }
            });
        });

        submitButton.addEventListener('click', function() {
            const selectedDataId = document.querySelector('.tree-item.highlight').getAttribute(
                'data-id');
            console.log('selectedDarta', selectedDataId);
            fetch('/solicitari/getTipAutorizatii', {
                    method: 'POST',
                    body: JSON.stringify({
                        selectedDataId
                    }),
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    // Update the HTML content with the returned values
                    const domeniuValues = data.functiiSelectedNodes;
                    console.log('domeniuValues', domeniuValues);
                    const domeniuValuesContainer = document.getElementById(
                        'tip-autorizatie-solicitat');
                    if (domeniuValuesContainer) {
                        domeniuValuesContainer.innerHTML = ''; // Clear previous content
                        domeniuValues.forEach((value, index) => {
                            const pElement = document.createElement('p');
                            pElement.textContent = value.domeniu;
                            pElement.id = `tip-autorizatie-${index}`
                            domeniuValuesContainer.appendChild(pElement);
                        });
                    }

                    const modalCloseButton = document.querySelector(
                        '[data-modal-toggle="modal-autorizatie"]');
                    modalCloseButton.click();
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        });


        // Update the submit button status initially
        updateSubmitButtonStatus();
    });
</script>

<style>
    /* Style for disabled state */
    #submit-button:disabled {
        background-color: #ccc;
        /* Set the background color */
        cursor: not-allowed;
        /* Set cursor to not-allowed */
    }
</style>
