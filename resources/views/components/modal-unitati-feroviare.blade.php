<!-- Modal toggle -->
<button data-modal-target="modal-unitati" data-modal-toggle="modal-unitati"
    class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
    type="button">
    Alegere persoană juridică
</button>

<!-- Main modal -->
<div id="modal-unitati" tabindex="-1" aria-hidden="true"
    class="modal-overlay hidden fixed top-0 right-0 left-0 z-50 justify-center items-center">
    <div class="relative p-4 w-full max-w-[80%]">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700 flex flex-col h-[90vh]">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Alegere unitate feroviara
                </h3>
                <button type="button"
                    class="modal-close-button text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-toggle="modal-unitati">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="modal-body p-4 md:p-5 overflow-scroll flex-grow">

                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            @foreach ($columns as $column)
                                <th scope="col" class="px-6 py-3">
                                    {{ $column }}
                                </th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody>

                        @foreach ($unitati as $unitate)
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700"
                                data-id="{{ $unitate->firma }}">
                                <td class="px-6 py-4 unitate-column">
                                    {{ $unitate->unitate }}
                                </td>
                                <td class="px-6 py-4 cod-fiscal-column">
                                    {{ $unitate->cod_fiscal }}
                                </td>
                            </tr>
                        @endforeach

                    </tbody>
                </table>
            </div>

            <!-- Pagination links -->
            {{-- <div class="p-4 md:p-5 w-full">
                {{ $unitati->links() }}
            </div> --}}
            <!-- End pagination links -->
            <button type="button" id="unitati-confirm-button"
                class="text-white w-[30%] m-6 self-end inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 mt-4 disabled:bg-gray-400 disabled:cursor-not-allowed"
                disabled>
                Confirma alegerea
            </button>
        </div>
    </div>
</div>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        const modalToggleButton = document.querySelector('[data-modal-toggle="modal-unitati"]');
        const modalContent = document.querySelector('#modal-unitati .modal-body');
        const confirmButton = document.getElementById('unitati-confirm-button');
        const modalCloseButton = document.querySelector('[data-modal-toggle="modal-unitati"]');
        let selectedRowId = null;

        function updateConfirmButtonStatus() {
            confirmButton.disabled = !selectedRowId;
        }




        modalContent.addEventListener('click', function(event) {
            const selectedRow = event.target.closest('tr');
            if (selectedRow) {
                // Remove highlight from all rows
                modalContent.querySelectorAll('tr').forEach(row => row.classList.remove('bg-green-500',
                    'text-white'));
                // Highlight the clicked row
                selectedRow.classList.add('bg-green-500', 'text-white');
                // Store the selected row id
                selectedRowId = selectedRow.getAttribute('data-id');
                // Update the confirm button status
                updateConfirmButtonStatus();
            }
        });

        confirmButton.addEventListener('click', function() {
            if (selectedRowId) {
                console.log('Confirmed Row ID:', selectedRowId);
                // Get unitate and cod_fiscal values from the selected row
                const currentSelectedRow = modalContent.querySelector(`tr[data-id="${selectedRowId}"]`);

                const unitateValue = currentSelectedRow.querySelector('.unitate-column').textContent
                    .trim();
                const codFiscalValue = currentSelectedRow.querySelector('.cod-fiscal-column')
                    .textContent
                    .trim();

                // Update input values
                document.getElementById('solicitant-pers-juridica-denumire').value = unitateValue;
                document.getElementById('solicitant-pers-juridica-cif').value = codFiscalValue;
                // Close the modal
                modalCloseButton.click();
            } else {
                alert('Please select a row before confirming.');
            }
        });
    });
</script>

<style>
    .text-white {
        color: #fff;
    }
</style>
