@foreach ($functions as $function)
    <li>
        <div class="tree-item" data-id="{{ $function->tip_aut }}">
            @if ($function->children->isNotEmpty())
                <span class="caret flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                </span>
            @else
                <span class="arrow-placeholder"></span>
            @endif
            <label class='label-domeniu' for="{{ $function->tip_aut }}">{{ $function->domeniu }}</label>
        </div>
        @if ($function->children)
            <ul class="nested ml-12">
                @include('components.tree-item', ['functions' => $function->children])
            </ul>
        @endif
    </li>
@endforeach
