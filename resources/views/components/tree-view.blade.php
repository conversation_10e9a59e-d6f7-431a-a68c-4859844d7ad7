<style>
    ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
    }

    .caret {
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        width: 16px;
        height: 16px;
        margin-right: 3px;
    }

    /* .caret::before {
        content: "\25B6";
        color: black;
        display: inline-block;
    } */

    .caret-down {
        -ms-transform: rotate(90deg);
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
    }

    .nested {
        display: none;
    }

    .active {
        display: block;
    }

    .arrow-placeholder {
        display: inline-block;
        width: 16px;
        height: 16px;
    }

    .highlight {
        background-color: #31C48D;
        font-weight: 600;
    }

    .tree-item {
        display: flex;
        align-items: center;
        padding: 5px 0;
        cursor: pointer;
    }

    .tree-item:not(.highlight):hover {
        background-color: rgba(0, 0, 0, 0.3);
    }

    .label-domeniu {
        cursor: pointer;
    }
</style>


@if ($functions)

    <ul class="border border-gray-300 rounded-md">
        @foreach ($functions as $function)
            <li>
                <div class="tree-item" data-id="{{ $function->tip_aut }}">
                    @if (!is_null($function->children) && $function->children->isNotEmpty())
                        <span id="{{ $function->tip_aut }}" class="caret flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>

                        </span>
                    @else
                        <span class="arrow-placeholder"></span>
                    @endif
                    <label class='label-domeniu' for="{{ $function->tip_aut }}">{{ $function->domeniu }}</label>
                </div>
                @if (!is_null($function->children) && $function->children)
                    <ul class="nested ml-12">
                        @include('components.tree-item', ['functions' => $function->children])
                    </ul>
                @endif
            </li>
        @endforeach
    </ul>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const toggler = document.querySelectorAll('.tree-item');
            toggler.forEach(function(item) {
                item.addEventListener('click', function() {
                    // Check if the clicked item has an arrow-placeholder or a caret
                    const arrowPlaceholder = this.querySelector('.arrow-placeholder');
                    const caret = this.querySelector('.caret');
                    const isEndItem = !!arrowPlaceholder;

                    // If it's an end item (contains arrow-placeholder), remove highlight from all non-end items
                    if (isEndItem) {
                        const allItems = document.querySelectorAll(
                            '.tree-item:not(.arrow-placeholder)');
                        allItems.forEach(function(element) {
                            if (element.classList.contains('highlight')) {
                                element.classList.remove('highlight');
                            }
                        });
                    }

                    // Toggle highlight class for the clicked item if it has arrow-placeholder
                    if (isEndItem) {
                        this.classList.toggle('highlight');
                    }

                    // Toggle caret-down class for the clicked item if it has a caret
                    if (caret) {
                        caret.classList.toggle('caret-down');
                    }

                    const nestedList = this.parentElement.querySelector('.nested');
                    if (nestedList) {
                        nestedList.classList.toggle('active');
                    }
                });
            });
        });
    </script>
@endif
