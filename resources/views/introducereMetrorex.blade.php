@php

    function renderOptions($functii)
    {
        $html = '';
        foreach ($functii as $functie) {
            $html .= '<option value="' . $functie->id . '">' . $functie->domeniu . '</option>';
            if (isset($functie->children)) {
                $html .= renderOptions($functie->children);
            }
        }
        return $html;
    }
    $selectedDataId = '';
@endphp

<x-app-layout>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autorizare Personal SC</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <style>
        .big-container {
            display: flex;
        }
    </style>

    <div class="max-w-7xl mx-auto bg-white shadow-md rounded p-5">
        <h1 class="text-2xl font-bold mb-5">INTRODUCERE PERSONAL METROREX</h1>
        <div class='big-container'>
            <div class='w-4/5'>
                <input id='personal-metrorex-nume' type="text" placeholder="Nume"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='personal-metrorex-prenume' type="text" placeholder="Prenume"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='personal-metrorex-cnp' type="text" placeholder="CNP"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='data-pv-metrorex' type="date" placeholder="Dată PV ZZ//LL/YYYY"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='numar-pv-metrorex' type="text" placeholder="Număr PV"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <label for="fotografie-permis"
                    class="block mb-2 text-sm font-medium text-gray-900  dark:text-white">Fotografie permis</label>
                <input type="file" id="fotografie-permis" name="fotografie-permis" accept="image/*"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <div class="mb-5 w-1/5">
                <!-- Image preview container -->
                <div class="mt-4 flex justify-center items-center">
                    <img id="preview-image" src="" alt="Preview" class="hidden"
                        style="width: 132px; height: 170px;">
                </div>

            </div>
        </div>
        <div class="mt-2">
            <div class='type3'>
                <div class="mt-2">
                    {{-- la apasare se deschid optiunile din tabelul functii --}}
                    <x-modal-tip-autorizatie-metrorex :functions='$functions' :selectedDataId='$selectedDataId' />
                </div>
            </div>
            <div id ='alegere-pers-juridica'class="mt-2 hidden">
                {{-- la apasare se deschid optiunile din tabelul functii --}}
                <x-modal-unitati-feroviare :unitati='$unitatiFeroviare' :columns='$unitatiColumns' />

            </div>
        </div>
        <div>
            <label class="mt-2 type3 block text-sm font-medium text-gray-700">Tip de autorizatie solicitat</label>
            <div class="type3 mt-1 p-2 border border-gray-300 bg-gray-50 rounded-md flex flex-row">
                <div class="flex flex-col gap-2 mr-2">
                    <p>Funcție:</p>
                    <p id='tip-autorizatie-metrorex'></p>
                </div>
                <div class="flex flex-col gap-2" id='tip-autorizatie-solicitat'>

                </div>
            </div>
        </div>
        <div class="flex justify-between">
            <button id='submitBtn'
                class="mt-2 bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">Continuare</button>
            <button class=" mt-2 bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded"
                onclick="window.location.href='/dashboard'">Abandonare</button>

        </div>
    </div>

</x-app-layout>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const formData = {};
        const onSubmit = async () => {
            const mapErrorsToFields = {
                'numeMetrorex': 'Solicitant Pers Fizica Nume',
                'prenumeMetrorex': 'Solicitant Pers Fizica Prenume',
                'cnpMetrorex': 'Solicitant Pers Fizica CNP',
                'numarPvMetrorex': 'Numar PV',
                'dataPvMetrorex': 'Data PV',
                'functieMetrorex': 'Functie',
                'fotografieAutorizatieMetrorex': 'Fotografie permis'
            };


            formData.numeMetrorex = document.getElementById(
                'personal-metrorex-nume')?.value;
            formData.prenumeMetrorex = document.getElementById(
                'personal-metrorex-prenume')?.value;
            formData.cnpMetrorex = document.getElementById(
                    'personal-metrorex-cnp')
                ?.value;
            formData.dataPvMetrorex = document.getElementById(
                    'data-pv-metrorex')
                ?.value;
            formData.numarPvMetrorex = document.getElementById(
                    'numar-pv-metrorex')
                ?.value;
            formData.functieMetrorex = document.getElementById('tip-autorizatie-metrorex')?.textContent;
            console.log('formdata', formData);

            if (!formData.numeMetrorex || !formData.prenumeMetrorex || !formData
                .cnpMetrorex || !formData.dataPvMetrorex || !formData
                .numarPvMetrorex || !formData.functieMetrorex || !formData.fotografieAutorizatieMetrorex
            ) {
                alert('Eroare de validare: Introduceti toate datele');
                return;
            }

            try {

                const response = await fetch(
                    `/solicitariMetrorex/store`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector(
                                    'meta[name="csrf-token"]')
                                .getAttribute('content')
                        },
                        body: JSON.stringify(formData),
                        redirect: 'manual'
                    });
                console.log('response', response);
                if (response.status === 200) {
                    alert('Solicitarea a fost inregistrata cu succes!');

                    window.location.href = '/permisMetrorex/vizualizare';
                } else if (response.status === 422) {
                    const errorData = await response.json();
                    let fieldErrors = '';

                    errorData.errors.forEach(errorField => {

                        fieldErrors.length > 0 ? fieldErrors +=
                            `, ${mapErrorsToFields[errorField]}` : fieldErrors +=
                            `${mapErrorsToFields[errorField]}`
                    })

                    alert(`${errorData.message}: ${fieldErrors}`);
                } else {
                    const errorData = await response.json();
                    alert('Eroare: ' + errorData.message);
                }
            } catch (error) {
                console.error('Eroare:', error);
                alert('A avut loc o eroare. Verifica toate campurile si incearca din nou.');
            }

        };


        document.getElementById('submitBtn').addEventListener('click', function(event) {
            event.preventDefault();
            onSubmit();
        });

        document.getElementById('fotografie-permis').addEventListener('change', function(event) {
            const file = event.target.files[0];
            const previewImage = document.getElementById('preview-image');

            if (file) {
                const reader = new FileReader();

                // When the image is loaded, set the src of the preview image element
                reader.onload = function(e) {
                    formData.fotografieAutorizatieMetrorex = e.target.result;
                    previewImage.src = e.target.result;
                    previewImage.classList.remove('hidden'); // Show the preview
                };

                reader.readAsDataURL(file); // Read the file as a data URL
            } else {
                previewImage.classList.add('hidden'); // Hide the preview if no file is selected
            }
        });
    });
</script>
