
<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style>
        body {
            font-size: 12px;
            font-family: 'Times New Roman';
            margin: 0;
            padding: 0;
        }

        .page {
            page-break-after: always;
        }

        .field {
            position: absolute;
        }

        /* Fața cardului */
        .prenume {
            top: 9mm;
            left: 26mm;
        }

        .nume {
            top: 12.1mm;
            left: 26mm;
        }

        .data-loc-nastere {
            top: 15.2mm;
            left: 26mm;
        }

        .data-eliberare-permis {
            top: 18.3mm;
            left: 27mm;
        }

        .data-expirare-permis {
            top: 18.3mm;
            left: 51mm;
        }

        .nume-autoritate-emitenta {
            top: 21.4mm;
            left: 27mm;
        }

        .numar-identificare-angajat {
            top: 27.6mm;
            left: 27mm;
        }

        .numar-permis {
            top: 30.7mm;
            left: 26mm;
        }

        .semnatura-titular {
            top: 33.8mm;
            left: 26mm;
        }

        .limba-materna {
            top: 7mm;
            left: 30mm
        }

        .informatii-suplimentare {
            top: 12mm;
            left: 30mm;
        }

        .restrictii-medicale{
            top: 17mm;
            left: 30mm;
        }

        @page {
            margin: 0;
            size: 85.60mm 53.98mm;
        }
    </style>
</head>
<body>
    <!-- Fața cardului -->
    <div class="page">
        <div class="field prenume">{{ $prenume_titular }}</div>
        <div class="field nume">{{ $nume_titular }}</div>
        <div class="field data-loc-nastere">{{ $data_nasterii }} {{ $locul_nasterii }}</div>
        <div class="field data-eliberare-permis">{{ $data_emitere }}</div>
        <div class="field data-expirare-permis">{{ $data_expirare }}</div>
        <div class="field nume-autoritate-emitenta">{{ $denumire_autoritate_emitenta }}</div>
        <div class="field numar-identificare-angajat">{{ $numar_atribuit_angajator }}</div>
        <div class="field numar-permis">{{ $nr_permis }}</div>
        {{-- <div class="field semnatura-titular">{{ $semnatura_titular }}</div> --}}
    </div>

    <!-- Spatele cardului -->
    <div>
        <div class="field limba-materna">Romana</div>
        <div class="field informatii-suplimentare">informatii-suplimentare</div>
        <div class="field restrictii-medicale">Restrictii-medicale</div>
    </div>
</body>
</html>


