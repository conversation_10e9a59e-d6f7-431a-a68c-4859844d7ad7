@props(['menus', 'dateTabelPermise', 'columns', 'unitatiFeroviare'])


<x-app-layout>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permise ASFR</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">



    <div class="max-w-7xl mx-auto bg-white shadow-md rounded p-5">
        <h1 class="text-2xl font-bold mb-5">Actualizează Permis ASFR</h1>

        <div class="mt-5 border-2 border-gray-300 p-4 rounded-md overflow-hidden w-full mx-auto">
            <div class="grid grid-cols-12 items-center gap-4">
                <label class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">Introduce număr permis:</label>

                <input type="text" class="col-span-2 bg-gray-50 border border-gray-300 text-gray-900 rounded-md p-2"
                    placeholder="RO" value="RO" />
                <input type="text" class="col-span-2 bg-gray-50 border border-gray-300 text-gray-900 rounded-md p-2"
                    placeholder="71" value="71" />
                <select id="an" name="an"
                    class="col-span-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="2016">2016</option>
                    <option value="2017">2017</option>
                    <option value="2018">2018</option>
                    <option value="2019">2019</option>
                    <option value="2020">2020</option>
                    <option value="2021">2021</option>
                    <option value="2022">2022</option>
                    <option value="2023">2023</option>
                    <option value="2024">2024</option>
                    <option value="2025">2025</option>
                    <option value="2026">2026</option>

                </select>
                <input type="number"
                class="col-span-2 bg-gray-50 border border-gray-300 text-gray-900 rounded-md p-2 no-spinner"
                min="0"
                max="9999"
                placeholder="0-9999" />
            </div>
        </div>
    </div>


    <div class="max-w-7xl mx-auto bg-white shadow-md rounded p-5">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <div class="mb-5 mt-5">
                    <label for="fotografie-permis"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Fotografie permis</label>
                    <input type="file" id="fotografie-permis" name="fotografie-permis" accept="image/*"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
    
                <div class="mb-5 mt-5">
                    <label for="fotografie-semnatura"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Semnătura</label>
                    <input type="file" id="fotografie-semnatura" name="fotografie-semnatura" accept="image/*"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
    
                <div class="mb-5 mt-5">
                    <label for="info-relev"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Informații relevante
                        național</label>
                <input type="text" id="info-relev" name='info-relev' value='--'
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    
                    <div class="mb-5 mt-5">
                        <label class="block text-sm font-medium">Selectare Dată Emitere Permis</label>
                        <input id='data-emitere' type="date"
                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                </div>

            </div>
    
            <div>
                {{-- Aici date actuale permis --}}
                <div>
                    <label for="date-permis-selectat"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white text-align:center">DATE PERMIS SELECTAT</label>
                </div>


                        
                        
                <div class="mb-2">
                    <label for="permis-nume"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">NUME</label>
                    <input type="text" id="permis-nume" name="permis-nume" value="Nume"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="mb-2">
                    <label for="permis-prenume"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">PRENUME</label>
                    <input type="text" id="permis-prenume" name="permis-prenume" value="Prenume"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>

                <div class="mb-2">
                    <label for="permis-cnp"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">CNP</label>
                    <input type="text" id="permis-cnp" name="permis-cnp" value="CNP"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>

                <div class="mb-2">
                    <label for="permis-unitate"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Unitate Feroviară</label>
                    <input type="text" id="permis-unitate" name="permis-unitate" value="Unitate Feroviară"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>

                <div class="mb-2">
                    <label for="permis-loc-nastere"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Locul nașterii</label>
                    <input type="text" id="permis-loc-nastere" name="permis-loc-nastere" value="Locul nașterii"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>

                <div class="mb-2">
                    <label for="permis-limba-materna"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Limbă maternă</label>
                    <input type="text" id="permis-limba-materna" name="permis-limba-materna" value="Limbă maternă"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>

                <div class="mb-2">
                    <label for="permis-nr-angajator"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numărul atribuit de angajator</label>
                    <input type="text" id="permis-nr-angajator" name="permis-nr-angajator" value="Numărul atribuit de angajator"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>

                <div class="mb-2">
                    <label for="permis-judet-domiciliu"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Județ domiciliu</label>
                    <input type="text" id="permis-judet-domiciliu" name="permis-judet-domiciliu" value="Județ domiciliu"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
            </div>
        </div>
    </div>
    


</x-app-layout>



<script>
</script>
