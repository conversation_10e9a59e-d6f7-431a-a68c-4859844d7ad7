<x-app-layout>
    @if(isset($permis))
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
        {{-- <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Editează Permis NR. {{$permis->$nr_permis}}</label> --}}

        <form action="{{ route('permise.search') }}" method="GET">
            @csrf
            <div class="mb-2 flex space-x-4 gap-5">
                <div class="mb-2">
                    <label for="permis-nume"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">NUME</label>
                    <input type="text" id="permis-nume" name="permis-nume" value="{{ $permis->nume_titular }}"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="mb-2">
                    <label for="permis-prenume"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">PRENUME</label>
                    <input type="text" id="permis-prenume" name="permis-prenume" value="{{ $permis->prenume_titular }}"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
            </div>
            <div class="mb-2 flex space-x-4 gap-5">
                <div class="mb-2">
                    <label for="permis-cnp"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">CNP</label>
                    <input type="text" id="permis-cnp" name='permis-cnp' value="{{ $permis->cnp }}"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="mb-2">
                    <label for="nr-permis"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">NUMĂR PERMIS</label>
                    <input type="text" id="permis-cnp" name='nr-permis' value="{{ $permis->nr_permis }}"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
            </div>
            <div class="mb-2 flex space-x-4 gap-5">
                <div class="mb-2">
                    <label for="data-emitere"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">DATĂ EMITERE</label>
                    <input type="text" id="permis-cnp" name='permis-data-emitere' value="{{ $permis->data_emitere }}"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="flex mb-10 inner-div flex-col">
                    <label for="loc-nastere" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">LOCUL NAȘTERII</label>
                    <input type="text" id="loc-nastere" name='loc-nastere' value="{{ $permis->locul_nasterii }}"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
            </div>
            <div class="mb-2">
                <label for="limba-materna"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">LIMBA MATERNĂ</label>
                <ul
                    class="items-center w-full text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg sm:flex dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <li class="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                        <div class="flex items-center ps-3">
                            <input id="limba-materna-1" type="radio" value="română" name="limba-materna"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"
                                @if($permis->limba_materna == 1) checked @endif>
                            <label for="limba-materna-1"
                                class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Română</label>
                        </div>
                    </li>
                    <li class="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                        <div class="flex items-center ps-3">
                            <input id="limba-materna-2" type="radio" value="maghiară" name="limba-materna"  @if($permis->limba_materna == 2) checked @endif
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                            <label for="limba-materna-2"
                                class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Maghiară</label>
                        </div>
                    </li>
                    <li class="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                        <div class="flex items-center ps-3">
                            <input id="limba-materna-3" type="radio" value="germană" name="limba-materna"  @if($permis->limba_materna == 3) checked @endif
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                            <label for="limba-materna-3"
                                class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Germană</label>
                        </div>
                    </li>
                    <li class="w-full dark:border-gray-600">
                        <div class="flex items-center ps-3">
                            <input id="limba-materna-4" type="radio" value="alta" name="limba-materna"  @if($permis->limba_materna == 4) checked @endif
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                            <label for="limba-materna-4"
                                class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Alta</label>
                        </div>
                    </li>
                </ul>
            </div>
        </form>
        <form action="{{ route('permise.update', $permis->id) }}" method="POST">
            @csrf
            @method('PUT')
                <div class="mb-2 flex flex-col items-start">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Unitate
                        feroviară</label>
                    <select id="unitati-feroviare" name='unitati-feroviare'
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">

                        @foreach ($unitatiFeroviare as $unitate)
                            <option value="{{ $unitate->firma }}" @if($unitate->firma == $permis->unitate) selected @endif>{{ $unitate->unitate }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="flex items-center mt-2">
                    <input checked id="id-in-tara" type="checkbox" name='id-in-tara' value=""
                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 mb-2">
                    <label for="id-in-tara" class="ms-2 text-sm mb-2 font-medium text-gray-900 dark:text-gray-300">ID în
                        țara
                        lui</label>
                </div>

                
                <div class="mb-2">
                    <label for="nr-angajator"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numărul
                        atribuit de
                        angajator</label>
                    <input type="text" id="nr-angajator" name='nr-angajator'
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="mb-2">
                    <label for="judet" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Judet domiciliu</label>
                    <select id="judet" name="judet" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="Alba">Alba</option>
                        <option value="Arad">Arad</option>
                        <option value="Argeș">Argeș</option>
                        <option value="Bacău">Bacău</option>
                        <option value="Bihor">Bihor</option>
                        <option value="Bistrița-Năsăud">Bistrița-Năsăud</option>
                        <option value="Botoșani">Botoșani</option>
                        <option value="Brașov">Brașov</option>
                        <option value="Brăila">Brăila</option>
                        <option value="Buzău">Buzău</option>
                        <option value="Caraș-Severin">Caraș-Severin</option>
                        <option value="Călărași">Călărași</option>
                        <option value="Cluj">Cluj</option>
                        <option value="Constanța">Constanța</option>
                        <option value="Covasna">Covasna</option>
                        <option value="Dâmbovița">Dâmbovița</option>
                        <option value="Dolj">Dolj</option>
                        <option value="Galați">Galați</option>
                        <option value="Giurgiu">Giurgiu</option>
                        <option value="Gorj">Gorj</option>
                        <option value="Harghita">Harghita</option>
                        <option value="Hunedoara">Hunedoara</option>
                        <option value="Ialomița">Ialomița</option>
                        <option value="Iași">Iași</option>
                        <option value="Ilfov">Ilfov</option>
                        <option value="Maramureș">Maramureș</option>
                        <option value="Mehedinți">Mehedinți</option>
                        <option value="Mureș">Mureș</option>
                        <option value="Neamț">Neamț</option>
                        <option value="Olt">Olt</option>
                        <option value="Prahova">Prahova</option>
                        <option value="Satu Mare">Satu Mare</option>
                        <option value="Sălaj">Sălaj</option>
                        <option value="Sibiu">Sibiu</option>
                        <option value="Suceava">Suceava</option>
                        <option value="Teleorman">Teleorman</option>
                        <option value="Timiș">Timiș</option>
                        <option value="Tulcea">Tulcea</option>
                        <option value="Vaslui">Vaslui</option>
                        <option value="Vâlcea">Vâlcea</option>
                        <option value="Vrancea">Vrancea</option>
                        <option value="București">București</option>
                        <option value="București Sector 1">București Sector 1</option>
                        <option value="București Sector 2">București Sector 2</option>
                        <option value="București Sector 3">București Sector 3</option>
                        <option value="București Sector 4">București Sector 4</option>
                        <option value="București Sector 5">București Sector 5</option>
                        <option value="București Sector 6">București Sector 6</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label for="restrictii-medicale"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Restricții
                        medicale</label>

                    <div class="flex items-center mb-2">
                        <input type="checkbox" value="" id='restrictii-medicale-1'
                            name='restrictii-medicale-1'
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="restrictii-medicale-1"
                            class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Utilizarea obligatorie a
                            ochelarilor/lentilelor de contact</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" value="" id='restrictii-medicale-2'
                            name='restrictii-medicale-2'
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="restrictii-medicale-2"
                            class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Utilizarea obligatorie a
                            protezei auditive/dispozitivului ajutător pentru comunicare</label>
                    </div>

                </div>
                <div class="mb-2">
                    <label for="info-relev"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Informații relevante
                        național</label>
                    <input type="text" id="info-relev" name='info-relev' value='--'
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
            <button type="submit"
                class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">Actualizează</button>
        </form>
        @endif
    </div>
</x-app-layout>
