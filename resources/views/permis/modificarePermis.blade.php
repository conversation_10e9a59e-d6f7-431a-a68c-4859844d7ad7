@props(['menus', 'dateTabelPermise', 'columns', 'unitatiFeroviare'])


<x-app-layout>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permise ASFR</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">


    <div class="max-w-7xl mx-auto bg-white shadow-md rounded p-5">
        <h1 class="text-2xl font-bold mb-5">Permise ASFR</h1>

        <div class="mt-5 border-2 border-gray-300 p-4 rounded-md overflow-hidden w-full mx-auto">
            <div class="grid grid-cols-12 items-center gap-4">
                <label class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">Număr permis:</label>

                <input type="text" class="col-span-2 bg-gray-50 border border-gray-300 text-gray-900 rounded-md p-2"
                    placeholder="RO" value="RO" readonly/>
                <input type="text" class="col-span-2 bg-gray-50 border border-gray-300 text-gray-900 rounded-md p-2"
                    placeholder="71" value="71" readonly/>
                <select id="an" name="an"
                    class="col-span-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="2016">2016</option>
                    <option value="2017">2017</option>
                    <option value="2018">2018</option>
                    <option value="2019">2019</option>
                    <option value="2020">2020</option>
                    <option value="2021">2021</option>
                    <option value="2022">2022</option>
                    <option value="2023">2023</option>
                    <option value="2024">2024</option>
                    <option value="2025">2025</option>
                    <option value="2026">2026</option>

                </select>
                <input type="number"
                class="col-span-2 bg-gray-50 border border-gray-300 text-gray-900 rounded-md p-2 no-spinner"
                min="0"
                max="9999"
                placeholder="0-9999" />
                <div class="flex-col">
                    <button id="cautare"
                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Căutare </button>
                </div>
            </div>
        </div>
        <div class="flex gap-x-10 mt-5 items-stretch">
            <div class="border border-gray-300 rounded p-4 w-1/3">
                <p class="font-semibold mb-2">Tip acțiune</p>

                <div class="mb-1">
                    <label class="inline-flex items-center">
                        <input id="actualizare-btn" type="radio" name="tip_actiune" value="actualizare"
                            class="form-radio text-blue-600">
                        <span class="ml-2 text-sm text-gray-800">Actualizare/Modicare</span>
                    </label>
                </div>

                <div class="mb-1">
                    <label class="inline-flex items-center">
                        <input id="plecare-btn" type="radio" name="tip_actiune" value="plecare"
                            class="form-radio text-blue-600">
                        <span class="ml-2 text-sm text-gray-800">Plecare (încetare contract de muncă)</span>
                    </label>
                </div>

                <div class="mb-1">
                    <label class="inline-flex items-center">
                        <input id="duplicat-btn" type="radio" name="tip_actiune" value="duplicat"
                            class="form-radio text-blue-600">
                        <span class="ml-2 text-sm text-gray-800">Emitere duplicat</span>
                    </label>
                </div>

                <div class="mb-1">
                    <label class="inline-flex items-center">
                        <input id="suspendare-btn" type="radio" name="tip_actiune" value="suspendare"
                            class="form-radio text-blue-600">
                        <span class="ml-2 text-sm text-gray-800">Suspendare</span>
                    </label>
                </div>

                <div class="mb-1">
                    <label class="inline-flex items-center">
                        <input id="retragere-btn" type="radio" name="tip_actiune" value="retragere"
                            class="form-radio text-blue-600">
                        <span class="ml-2 text-sm text-gray-800">Retragere</span>
                    </label>
                </div>

                <div class="mb-1">
                    <label class="inline-flex items-center">
                        <input id="reinnoire-btn" type="radio" name="tip_actiune" value="reinnoire"
                            class="form-radio text-blue-600">
                        <span class="ml-2 text-sm text-gray-800">Reînnoire</span>
                    </label>
                </div>
            </div>
            <div class="relative w-2/3">
                <div class="flex  flex-col items-start gap-x-2 border-2 border-gray-300 p-4 rounded-md overflow-hidden hidden"
                    id="container-actualizare">
                    <div class="mb-5 flex flex-col items-start">

                        <p class="font-semibold mb-2">Actualizare (schimbare angajator, nr. la angajator, domiciliu om)
                        </p>

                        <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Unitate
                            feroviară</label>
                        <select id="unitati-feroviare" name='unitati-feroviare'
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">

                            @foreach ($unitatiFeroviare as $unitate)
                                <option value="{{ $unitate->firma }}">{{ $unitate->unitate }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex-col">
                        <label class="block text-sm font-medium">Dată angajării</label>
                        <input id='data-angajarii-actualizare' type="date"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                    <div class="flex-col">
                        <label class="block mb-2 mt-2 text-sm font-medium text-gray-900 dark:text-white">Numărul
                            atribuit de
                            angajator</label>
                        <input type="text" id="numar-angajator-actualizare" name='numar-angajator-actualizare'
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                    <div class="mb-5">
                        <label for="judet"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Judet
                            domiciliu</label>
                        <select id="judet-actualizare" name="judet"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="Alba">Alba</option>
                            <option value="Arad">Arad</option>
                            <option value="Argeș">Argeș</option>
                            <option value="Bacău">Bacău</option>
                            <option value="Bihor">Bihor</option>
                            <option value="Bistrița-Năsăud">Bistrița-Năsăud</option>
                            <option value="Botoșani">Botoșani</option>
                            <option value="Brașov">Brașov</option>
                            <option value="Brăila">Brăila</option>
                            <option value="Buzău">Buzău</option>
                            <option value="Caraș-Severin">Caraș-Severin</option>
                            <option value="Călărași">Călărași</option>
                            <option value="Cluj">Cluj</option>
                            <option value="Constanța">Constanța</option>
                            <option value="Covasna">Covasna</option>
                            <option value="Dâmbovița">Dâmbovița</option>
                            <option value="Dolj">Dolj</option>
                            <option value="Galați">Galați</option>
                            <option value="Giurgiu">Giurgiu</option>
                            <option value="Gorj">Gorj</option>
                            <option value="Harghita">Harghita</option>
                            <option value="Hunedoara">Hunedoara</option>
                            <option value="Ialomița">Ialomița</option>
                            <option value="Iași">Iași</option>
                            <option value="Ilfov">Ilfov</option>
                            <option value="Maramureș">Maramureș</option>
                            <option value="Mehedinți">Mehedinți</option>
                            <option value="Mureș">Mureș</option>
                            <option value="Neamț">Neamț</option>
                            <option value="Olt">Olt</option>
                            <option value="Prahova">Prahova</option>
                            <option value="Satu Mare">Satu Mare</option>
                            <option value="Sălaj">Sălaj</option>
                            <option value="Sibiu">Sibiu</option>
                            <option value="Suceava">Suceava</option>
                            <option value="Teleorman">Teleorman</option>
                            <option value="Timiș">Timiș</option>
                            <option value="Tulcea">Tulcea</option>
                            <option value="Vaslui">Vaslui</option>
                            <option value="Vâlcea">Vâlcea</option>
                            <option value="Vrancea">Vrancea</option>
                            <option value="București">București</option>
                            <option value="București Sector 1">București Sector 1</option>
                            <option value="București Sector 2">București Sector 2</option>
                            <option value="București Sector 3">București Sector 3</option>
                            <option value="București Sector 4">București Sector 4</option>
                            <option value="București Sector 5">București Sector 5</option>
                            <option value="București Sector 6">București Sector 6</option>
                        </select>
                    </div>
                    <div class="mb-1">
                        <label class="inline-flex items-center">
                            <input id="ochelari-check" type="checkbox" name="tip_restrictie_medicala" value="ochelari"
                                class="form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">utilizarea obligatorie a ochelarilor/lentilelor de
                                contact</span>
                        </label>
                    </div>
                    <div class="mb-1">
                        <label class="inline-flex items-center">
                            <input id="proteza-check" type="checkbox" name="tip_restrictie_medicala" value="proteza"
                                class="form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">utilizarea obligatorie a protezei
                                auditive/dispozitivul ajutător pentru comunicare</span>
                        </label>
                    </div>
                    <div class="flex-col">
                        <label class="block text-sm font-medium">Dată reemiterii</label>
                        <input id='data-reemitere-actualizare' type="date"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                    <div class="flex-col">
                        <button id="transmite-date-actualizare"
                            class="text-white mt-2 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            Transmite date </button>
                    </div>
                </div>



                <div class="flex flex-col items-start gap-x-2 border-2 border-gray-300 p-4 rounded-md overflow-hidden hidden"
                    id="container-plecare">
                    <div class="mb-5 flex flex-col items-start">
                        <p class="font-semibold mb-2">Plecare (încetare contract de muncă)</p>
                    </div>
                    <div class="flex-col">
                        <label class="block text-sm font-medium">Din data de </label>
                        <input id='data-de-plecare' type="date"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                    <div class="flex-col">
                        <button id="transmite-date-plecare"
                            class="text-white mt-2 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            Transmite date </button>
                    </div>
                </div>



                <div class="flex  flex-col items-start gap-x-2 border-2 border-gray-300 p-4 rounded-md overflow-hidden hidden"
                    id="container-duplicat">
                    <div class="mb-5 flex flex-col items-start">
                        <p class="font-semibold mb-2">Emitere duplicat</p>
                    </div>
                    <div class="flex-col">
                        <label class="block text-sm font-medium">Dată reemiterii</label>
                        <input id='data-reemitere-plecare' type="date"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                    <div class="flex-col">
                        <button id="transmite-date-duplicat"
                            class="text-white mt-2 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            Transmite date </button>
                    </div>
                </div>



                <div class="flex  flex-col items-start gap-x-2 border-2 border-gray-300 p-4 rounded-md overflow-hidden hidden"
                    id="container-suspendare">
                </div>



                <div class="flex  flex-col items-start gap-x-2 border-2 border-gray-300 p-4 rounded-md overflow-hidden hidden"
                    id="container-retragere">
                    <p class="font-semibold mb-2">Motivul RETRAGERII permiselor de conducere conform prevederilor
                        articolului 28, alineatul (3) din Anexa 1 la OMT 615/2015</p>
                    <div class="mb-1">
                        <label for="denumire"
                            class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">litera a)</label>
                        <label class="inline-flex items-center">
                            <input id="lit-a-1" type="checkbox" name="motiv_retragere" value="lit-a-1"
                                class="ml-4 form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">1. mecanicul se face vinovat de producerea unui
                                accident/incident</span>
                        </label>
                    </div>
                    <div class="mb-1">
                        <label for="denumire"
                            class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">litera b)</label>
                        <label class="inline-flex items-center">
                            <input id="lit-b-1" type="checkbox" name="motiv_retragere" value="lit-b-1"
                                class="ml-4 form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">1. mecanicul are aviz "INAPT" din punct de vedere
                                medical pentru exercitarea funcției</span>
                        </label>
                    </div>
                    <div class="mb-1">
                        <label for="denumire"
                            class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">litera b)</label>
                        <label class="inline-flex items-center">
                            <input id="lit-b-2" type="checkbox" name="motiv_retragere" value="lit-b-2"
                                class="ml-4 form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">2. mecanicul are aviz "INAPT" din punct de vedere
                                psihologic pentru exercitarea funcției</span>
                        </label>
                    </div>
                    <div class="mb-1">
                        <label for="denumire"
                            class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">litera b)</label>
                        <label class="inline-flex items-center">
                            <input id="lit-b-3" type="checkbox" name="motiv_retragere" value="lit-b-3"
                                class="ml-4 form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">3. mecanicul a DECEDAT</span>
                        </label>
                    </div>
                    <div class="mb-1">
                        <label for="denumire"
                            class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">litera c)</label>
                        <label class="inline-flex items-center">
                            <input id="lit-c-1" type="checkbox" name="motiv_retragere" value="lit-c-1"
                                class="ml-4 form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">1. mecaniculul i s-a interzis exeratarea profesiei
                                prin hotărâre judecătorească</span>
                        </label>
                    </div>
                    <div class="mb-1">
                        <label for="denumire"
                            class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">litera d)</label>
                        <label class="inline-flex items-center">
                            <input id="lit-d-1" type="checkbox" name="motiv_retragere" value="lit-d-1"
                                class="ml-4 form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">1. mecanicul NU a obtinut avizul "APT din punct de
                                vedere medical timp de 12 luni de la data suspendării permisului</span>
                        </label>
                    </div>
                    <div class="mb-1">
                        <label for="denumire"
                            class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">litera d)</label>
                        <label class="inline-flex items-center">
                            <input id="lit-d-2" type="checkbox" name="motiv_retragere" value="lit-d-2"
                                class="ml-4 form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">2. mecanicul NU a obtinut avizul "APT" din punct
                                de vedere psihologic timp de 12 luni de la data suspendării permisului</span>
                        </label>
                    </div>
                    <div class="mb-1">
                        <label for="denumire"
                            class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">litera d)</label>
                        <label class="inline-flex items-center">
                            <input id="lit-d-3" type="checkbox" name="motiv_retragere" value="lit-d-3"
                                class="ml-4 form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">3. mecanicul NU a urmat un program de formare
                                profesională continuă timp de 12 luni de la data suspendări permisului</span>
                        </label>
                    </div>
                    <div class="mb-1">
                        <label for="denumire"
                            class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">litera d)</label>
                        <label class="inline-flex items-center">
                            <input id="lit-d-4" type="checkbox" name="motiv_retragere" value="lit-d-4"
                                class="ml-4 form-checkbox text-blue-600">
                            <span class="ml-2 text-sm text-gray-800">4. mecanicul NU a obținut certificarea periodică a
                                competențelor generale timp de 12 luni de la data suspendării permisului</span>
                        </label>
                    </div>
                    <div class="flex-col">
                        <label class="block text-sm font-medium">Dată retragerii</label>
                        <input id='data-reemitere-retragere' type="date"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                    <div class="flex-col">
                        <button id="transmite-date-retragere"
                            class="text-white mt-2 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            Transmite date </button>
                    </div>
                </div>



                <div class="flex  flex-col items-start gap-x-2 border-2 border-gray-300 p-4 rounded-md overflow-hidden hidden"
                    id="container-reinnoire">
                </div>

            </div>
        </div>
        <div class="mt-5 border-2 border-gray-300 p-4 rounded-md overflow-hidden w-full mx-auto">
            <div class="grid grid-cols-12 items-center gap-4">
                <label for="denumire"
                    class="col-span-2 text-sm font-medium text-gray-900 dark:text-white">Denumire</label>
                <input type="text" id="denumire" name="denumire" readonly
                    class="col-span-6 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-2.5 
                     focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 
                     dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                <label for="data-de" class="col-span-2 text-sm font-medium whitespace-nowrap">de la data de</label>
                <input type="date" id="data-de"
                    class="col-span-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 
                     dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
            </div>
        </div>
    </div>



</x-app-layout>

<script>
    document.querySelectorAll('input[type="radio"]').forEach(radio => {
        radio.addEventListener('change', () => {
            if (radio.checked) {
                // debifez tot
                document.querySelectorAll('input[type="radio"]').forEach(other => {
                    if (other !== radio) {
                        other.checked = false;
                    }
                });

                // ascund toate containerele
                document.querySelectorAll('[id^="container-"]').forEach(container => {
                    container.classList.add('hidden');
                });

                // afisez doar ce imi trebuie
                const containerId = 'container-' + radio.id.replace('-btn', '');
                const container = document.getElementById(containerId);
                if (container) {
                    container.classList.remove('hidden');
                }
            } else {
                // daca debifez ultima selectie ascund tot
                const containerId = 'container-' + radio.id.replace('-btn', '');
                const container = document.getElementById(containerId);
                if (container) {
                    container.classList.add('hidden');
                }
            }
        });
    });

    // });
</script>
