@props(['menus', 'dateTabelPermise', 'columns'])


<x-app-layout>
    <x-slot name="header">
        <nav
            class="flex justify-center items-center flex-wrap gap-1 bg-gray-200 dark:bg-gray-800 p-4 font-semibold text-xl dark:text-white text-black dark:text-gray-200 leading-tight">
            @foreach ($menus as $menu)
                <x-custom-dropdown align="left" width="48" contentClasses="py-1 bg-white dark:bg-gray-800"
                    :menuObj="$menu">
                </x-custom-dropdown>
            @endforeach
        </nav>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <form class='mb-2' method="GET" action="{{ route('permise.index') }}">
                <div class="flex items-center">
                    <input type="text" name="search" value="{{ request('search') }}"
                        class="form-input rounded-md shadow-sm mt-1 block w-full"
                        placeholder="Căutați după NR. Permis">
                    <button type="submit" class="ml-3 btn btn-primary">Căutați</button>
                    @if (isset($search))
                        <a href="{{ route('permise.index') }}" class="ml-3 btn btn-secondary text-red-500">X</a>
                    @endif

                </div>
            </form>

        </div>
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <button type="button" id="butonEditarePermis"
            class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Editează Permis</button>

            
            <button type="button" id="butonValidarePermis"
            class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Validează Permis</button>
            
            
            @role('super_admin')
                <button type="button" id="butonAcordareNumar"
                class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Acordă număr</button>
                
                <button type="button" id="butonValidareValidarePermis"
                class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Validează Număr Acordat</button>
            
                <button type="button" id="butonActualizare"
                class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Actualizare/Modificare</button>


                <button type="button" id="butonValidareActualizare"
                class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Validează Actualizare</button>
                
                <button type="button" id="butonTiparire"
                class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Tipărire Permis</button>

                <button type="button" id="butonValidareTiparire"
                class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Validare Tipărire</button>
            @endrole

            @if (isset($columns) && isset($dateTabelPermise))
                <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3 sticky">Action</th>
                                @foreach ($columns as $column => $columnName)
                                    @if ($column != 'id') 
                                        <th scope="col" class="px-6 py-3">{{ $columnName }}</th>
                                        @endif
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($dateTabelPermise as $dataRow)
                                <tr
                                    class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <td class="px-6 py-4 sticky">
                                        <input type="radio" name="data-radio" class="data-radio"
                                            data-row-id="{{ $dataRow->id }}" data-status="{{$dataRow->status}}" data-nr-permis="{{ $dataRow->numar_permis }}"
                                            data-nume="{{ $dataRow->nume}}" data-prenume="{{ $dataRow->prenume }}"
                                            data-data-emitere="{{ $dataRow->data_emitere }}"
                                            data-cnp="{{ $dataRow->cnp }}" data-judet-domiciliu="{{ $dataRow->judet_domiciliu }}"/>
                                    </td>
                                    @foreach ($columns as $column => $columnName)
                                        @if ($column != 'id') 
                                            <th scope="col" class="px-6 py-3">{{ $dataRow->$column }}</th>
                                        @endif
                                    @endforeach

                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $dateTabelPermise->appends(request()->query())->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dataRadios = document.querySelectorAll('.data-radio');
        //iau butonul de editare dupa id-ul atribuit acestuia
        const btnEditarePermis = document.getElementById('butonEditarePermis');
        const btnValidarePermis = document.getElementById('butonValidarePermis');
        const btnAcordareNumar = document.getElementById('butonAcordareNumar');
        const btnValidareAcordareNumar = document.getElementById('butonValidareAcordareNumar');
        const btnActualizare = document.getElementById('butonActualizare');
        const btnValidareActualizare = document.getElementById('butonValidareActualizare');
        const btnTiparire = document.getElementById('butonTiparire');
        const btnValidareTiparire = document.getElementById('butonValidareTiparire');
        


        // declar null aceste variabile pentru evitarea erorilor
        let selectedRowIdPermis = null;
        let selectedRowStatusPermis = null;
        let selectedRowNrPermisPermis = null;
        let selectedRowNumePermis = null;
        let selectedRowPrenumePermis = null;
        let selectedRowDataEmiterePermis = null;
        let selectedRowCnpPermis = null;
        let selectedRowJudetePermis = null;



        const updateButtonVisibility = () => {
            const anyChecked = Array.from(dataRadios).some(radio => radio.checked);

            if(anyChecked) {
                btnEditarePermis.classList.remove('hidden');
                const radio = document.querySelector('.data-radio:checked');
                selectedRowIdPermis = radio.getAttribute('data-row-id');
                selectedRowStatusPermis = radio.getAttribute('data-status');
                selectedRowNrPermisPermis = radio.getAttribute('data-nr-permis');
                selectedRowNumePermis = radio.getAttribute('data-nume');
                selectedRowPrenumePermis = radio.getAttribute('data-prenume');
                selectedRowDataEmiterePermis = radio.getAttribute('data-data-emitere');
                selectedRowCnpPermis = radio.getAttribute('data-cnp');
                selectedRowJudetePermis = radio.getAttribute('data-judet-domiciliu');



            switch (selectedRowStatusPermis){
                case 'IN LUCRU':
                        btnEditarePermis.classList.remove('hidden');
                        btnValidarePermis.classList.remove('hidden');

                        btnAcordareNumar.classList.add('hidden');
                        btnValidareAcordareNumar.classList.add('hidden');
                        btnActualizare.classList.add('hidden');
                        btnValidareActualizare.classList.add('hidden');
                        btnTiparire.classList.add('hidden');
                        btnValidareTiparire.classList.add('hidden');
                    break;

                case 'PERMIS VALIDAT':

                    break;

                case 'NR PERMIS ACORDAT':

                    break;


                case 'NR PERMIS VALIDAT':

                    break;

                case 'ACTUALIZARE PERMIS':

                    break;


                case 'ACTUALIZARE PERMIS VALIDAT':

                    break;

                case 'PERMIS TIPARIT':

                    break;


                case 'PERMIS TIPARIT VALIDAT':

                    break;

                case 'NR CARD INTRODUS':

                    break;


                case 'NR CARD VALIDAT':

                    break;

            }
            } else {
                btnEditarePermis.classList.add('hidden');

            }

        }


        dataRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                updateButtonVisibility();
            });
        });

        btnEditarePermis.addEventListener('click', function() {
            if (selectedRowIdPermis !== null){
                window.location.href = `/permise/edit/${selectedRowIdPermis}`;
            }
        });


    });

</script>