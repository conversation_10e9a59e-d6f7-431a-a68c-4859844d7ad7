@props(['menus', 'dateTabel', 'columns'])

<x-app-layout>
    <x-slot name="header">
        <nav
            class="flex justify-center items-center flex-wrap gap-1 bg-gray-200 dark:bg-gray-800 p-4 font-semibold text-xl dark:text-white text-black dark:text-gray-200 leading-tight">
            @foreach ($menus as $menu)
                <x-custom-dropdown align="left" width="48" contentClasses="py-1 bg-white dark:bg-gray-800"
                    :menuObj="$menu">
                </x-custom-dropdown>
            @endforeach
        </nav>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <div id='fields-nr-iesire' class='flex flex-row align-center'>
                <button type="button" id="btnEmitereAutorizatie"
                    class="hidden text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Emitere
                    permis</button>
                <button type="button" id="btnEditarePermisMetrorex"
                    class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Editează Permis</button>
            </div>
        </div>
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (isset($columns) && isset($dateTabel))
                <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3 sticky">Action</th>
                                @foreach ($columns as $colKey => $colLabel)
                                    @if($colKey !== 'id')
                                        <th scope="col" class="px-6 py-3">{{ $colLabel }}</th>
                                    @endif
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($dateTabel as $dataRow)
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <td class="px-6 py-4 sticky">
                                        <input type="radio" name="row-radio" class="row-radio"
                                            data-row-id="{{ $dataRow->id }}"
                                            data-status="{{ $dataRow->status }}" 
                                            data-nume="{{ $dataRow->nume }}"
                                            data-prenume="{{ $dataRow->prenume }}"
                                            data-cnp="{{ $dataRow->cnp }}"
                                            data-data-pv="{{ $dataRow->data_pv }}"
                                            data-numar-pv="{{ $dataRow->numar_pv }}"
                                            data-functie="{{ $dataRow->functie }}"
                                            data-numarPermisMetrorex="{{ $dataRow->nr_permis }}">
                                    </td>
                                    @foreach ($columns as $colKey => $colLabel)
                                        @if($colKey !== 'id')
                                            <td class="px-6 py-4" data-column="{{ $colKey }}">
                                                {{ $dataRow->$colKey }}
                                            </td>
                                        @endif
                                    @endforeach
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $dateTabel->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dataRadios = document.querySelectorAll('.row-radio');

        const btnEmitereAutorizatie = document.getElementById('btnEmitereAutorizatie');
        const btnEditarePermisMetrorex = document.getElementById('btnEditarePermisMetrorex');


        let selectedRowIdPermisMetrorex = null;
        let selectedRowStatusPermisMetrorex = null;
        let selectedRowNumePermisMetrorex = null;
        let selectedRowPrenumePermisMetrorex = null;
        let selectedRowCnpPermisMetrorex = null;
        let selectedRowDataPvPermisMetrorex = null;
        let selectedRowNumarPvPermisMetrorex = null;
        let selectedRowFunctiePermisMetrorex = null;
        let selectedRowNumarPermisMetrorex = null;


        const updateButtonVisibility = () => {
            const anyChecked = Array.from(dataRadios).some(radio => radio.checked);


            if(anyChecked) {
                btnEmitereAutorizatie.classList.remove('hidden');
                btnEditarePermisMetrorex.classList.remove('hidden');
                const radio = document.querySelector('.row-radio:checked');
                selectedRowIdPermisMetrorex = radio.getAttribute('data-row-id');
                selectedRowStatusPermisMetrorex = radio.getAttribute('data-status');
                selectedRowNumePermisMetrorex = radio.getAttribute('data-nume');
                selectedRowPrenumePermisMetrorex = radio.getAttribute('data-prenume');
                selectedRowCnpPermisMetrorex = radio.getAttribute('data-cnp');
                selectedRowDataPvPermisMetrorex = radio.getAttribute('data-data-pv');
                selectedRowNumarPvPermisMetrorex = radio.getAttribute('data-numar-pv');
                selectedRowFunctiePermisMetrorex = radio.getAttribute('data-functie');
                selectedRowNumarPermisMetrorex = radio.getAttribute('data-numarPermisMetrorex');
            } else {
                btnEmitereAutorizatie.classList.add('hidden');
                btnEditarePermisMetrorex.classList.add('hidden');

            }

        }
        dataRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                updateButtonVisibility();
            });
        });

        btnEditarePermisMetrorex.addEventListener('click', function() {
            if (selectedRowIdPermisMetrorex !== null){
                window.location.href = `/permisMetrorex/edit/${selectedRowIdPermisMetrorex}`;
            }
        });

        btnEmitereAutorizatie.addEventListener('click', () => {
            const radio = document.querySelector('.row-radio:checked');

            selectedRowId = radio.getAttribute('data-row-id');

            if (selectedRowId) {
                fetch(`/generateAutorizatieMetrorex/${selectedRowId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Success:', data);
                        // Decode the base64 PDF content
                        const pdfContent = atob(data.pdf);
                        // Convert the decoded content to an array buffer
                        const arrayBuffer = new Uint8Array([...pdfContent].map(char => char
                            .charCodeAt(0))).buffer;
                        // Create a Blob from the array buffer
                        const pdfBlob = new Blob([arrayBuffer], {
                            type: 'application/pdf'
                        });
                        // Create a URL for the Blob
                        const pdfUrl = URL.createObjectURL(pdfBlob);
                        // Create a link element to trigger the download
                        const link = document.createElement('a');
                        link.href = pdfUrl;
                        link.download = 'autorizatieMetrorex.pdf';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);


                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            }
        })
    })
</script>
