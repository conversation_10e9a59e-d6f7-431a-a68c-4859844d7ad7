
<x-app-layout>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permis Special</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <style>
        .big-container {
            display: flex;
        }
    </style>

{{--  
    AFER: tip_permis(AFER), nr_serie, nume, prenume, data_emitere, functie, unitate, valabilitate, ministru, poza
    AGIFER: tip_permis(AGIFER), nr_serie, nume, prenume, data_emitere, functie, unitate, valabilitate, ministru, poza

    Unitatea: Agenția de Investigare Feroviară Română - AGIFER
    SORIN MIHAI GRINDEANU

    Spate AGIFER:
        În conformitate cu prevederile art. 21, alin. (2) din Ordonanţa de urgenţă a Guvernului nr. 73/2019: „În conformitate cu legislaţia în vigoare şi, dac<PERSON> este cazul, în cooperare cu autorităţile responsabile pentru ancheta judiciară, investigatorilor li se acordă, de îndată ce este posibil, acces la informaţiile şi probele relevante pentru investigaţie. În special, investigatorilor li se acordă:
    a) acces  imediat  la  locul  accidentului  sau  incidentului,  precum  şi  la  materialul  rulant  implicat,  infrastructura 
        aferentă şi instalaţiile de control al traficului şi de semnalizare; 
    b) dreptul  de  a  întocmi  imediat  o  listă  a  probelor  şi  la  îndepărtarea  controlată  a  vehiculelor, instalaţiilor sau
        componentelor de infrastructură în vederea examinării sau analizei; 
    c) accesul  nerestricţionat  la/şi  utilizarea  conţinutului  aparatelor  de  înregistrare  de  bord  şi  a echipamentelor de
        înregistrare a mesajelor verbale şi de înregistrare a funcţionării sistemului de semnalizare şi control al traficului; 
    d) accesul la rezultatele examinării corpurilor victimelor; 
    e) accesul  la  rezultatele  examinării  personalului  trenului  şi  a  altui  personal  feroviar  implicat  în  accident  sau
        incident; 
    f) posibilitatea chestionării personalului feroviar implicat în accident sau incident şi a altor martori; 
    g) accesul la  orice  informaţii  relevante  sau  înregistrări deţinute de administratorul de infrastructură, operatorii de
        transport feroviar, entităţile responsabile cu întreţinerea şi de Autoritatea de Siguranţă Feroviară Română.

    În vederea exercitării atribuțiilor, posesorul legitimaţiei are drept de acces la locul producerii accidentelor şi incidentelor feroviare şi pe toate mijloacele de transport feroviar, inclusiv în cabina de conducere a acestora, în unităţile cu specific feroviar care funcționează sub autoritatea sau în subordinea MTI, în unităţile operatorilor de transport feroviar/operatorilor care efectuează numai manevră feroviară, în unităţile administratorului şi gestionarilor de infrastructură feroviară, pe liniile ferate industriale, în unităţile certificate ca entități responsabile cu întreţinerea vehiculelor feroviare şi în unităţile aferente transportului cu metroul.

    Prezenta legitimație este un document cu regim special, nominal şi netransmisibil!

    Spate AFER:
        În conformitate cu prevederile Hotărârii Guvernului nr. 310/2023 şi Ordonanţei Guvernului nr. 39/2000, titularul legitimației este împuternicit:
    1.	să efectueze acţiuni de monitorizare/supraveghere şi inspecție de siguranţă pentru respectarea de către furnizorii interni şi externi din domeniul feroviar, de transport cu metroul, metroul ușor, monorail şi transport urban, suburban şi regional pe şine, linii industriale, precum şi pe căi ferate cu caracter de patrimoniu, de muzeu sau turistic, care furnizează produse, servicii şi echipamente din domeniile menţionate, a cerinţelor legale şi de reglementare tehnică care au stat la baza emiterii de către AFER a documentelor specifice pentru produse/servicii/echipamente utilizate la construirea, modernizarea, repararea şi întreţinerea materialului rulant, altele decât cele care fac obiectul certificării ca ERI şi a infrastructurii feroviare, pentru metrou, metrou ușor, monorail şi transport urban, suburban şi regional pe şine, precum şi pe căi ferate cu caracter de patrimoniu, de muzeu sau turistic;
    2.	să efectueze inspecția vagoanelor-cisternă pentru acordarea avizului tehnic Compuși organici volatili - COV, precum şi a vehiculelor cu motoare termice din punctul de vedere al emisiilor de noxe;
    3.	să efectueze supravegherea prin inspecție tehnică a tuturor operatorilor economici deținători de LFI pentru care s-au eliberat autorizaţii de către ASFR, pe durata de valabilitate a acestora;
    4.	să efectueze controlul privind siguranţa circulaţiei pentru domeniile prevăzute la art. 3 pct. 5 din anexa la Hotărârea Guvernului nr. 310/2023;
    5.	să efectueze supravegherea din punct de vedere tehnic a respectării reglementărilor interne şi internaționale în domeniul de activitate al AFER;
    6.	să solicite şi să obțină datele, informațiile şi documentele necesare îndeplinirii atribuțiilor de control şi inspecție tehnică;
    7.	să constate, să încadreze şi să sancţioneze faptele contravenţionale, în conformitate cu atribuţiile prevăzute în actele normative în vigoare.

    În vederea exercitării atribuțiilor, posesorul legitimaţiei are drept de acces şi control pe toate mijloacele de transport feroviar, transport cu metroul, metroul uşor, monorail şi transport urban, suburban şi regional pe şine, pe linii ferate industriale, precum şi pe căile ferate cu caracter de patrimoniu, de muzeu sau turistic, inclusiv în cabina de conducere a acestora, în unităţile cu specific feroviar care funcţionează sub autoritatea sau în subordinea Ministerului Transporturilor şi Infrastructurii, în unităţile operatorilor de transport feroviar, în unităţile administratorului şi gestionarilor de infrastructură feroviară, de linii ferate industriale ori material rulant, precum şi în unităţile operatorilor economici care furnizează produse sau prestează servicii pentru transportul feroviar, transportul cu metroul, metroul uşor, monorail şi transportul urban, suburban şi regional pe şine, precum şi pe căile ferate cu caracter de patrimoniu, de muzeu sau turistic.

    Prezenta legitimație este un document cu regim special, nominal şi netransmisibil!

    Spate MTI:
        În conformitate cu prevederile Hotărârii Guvernului nr. 370/2021, cu modificările şi completările ulterioare şi a Ordonanţei Guvernului nr. 39/2000, titularul legitimației este împuternicit:

    1.	să efectueze acţiuni de control şi monitorizare a domeniului de transport feroviar;
    2.	să efectueze inspecție şi control privind respectarea reglementărilor interne şi internaționale în activităţile specifice transporturilor feroviare;
    3.	să efectueze inspecție de stat şi control în transporturile feroviare şi infrastructura de transport feroviar de interes naţional.
    4.	să constate, să încadreze şi să sancţioneze faptele contravenţionale produse în desfăşurarea operaţiunilor de transport feroviar şi cu metroul, în conformitate cu atribuţiile prevăzute în actele normative în vigoare.

    În vederea exercitării atribuțiilor, posesorul legitimaţiei are drept de acces pe toate mijloacele de transport feroviar, inclusiv în cabina de conducere a acestora, în unităţile cu specific feroviar care funcționează sub autoritatea sau în subordinea MTI, în unităţile operatorilor de transport feroviar/operatorilor care efectuează numai manevră feroviară, în unităţile administratorului şi gestionarilor de infrastructură feroviară, pe liniile ferate industriale, în unităţile certificate ca entități responsabile cu întreţinerea vehiculelor feroviare, în unităţile aferente transportului cu metroul, precum şi în unităţile destinate altor tipuri de transport pe şine.

    Prezenta legitimație este un document cu regim special, nominal şi netransmisibil!


    Spate ASFR:
        În conformitate cu prevederile Ordonanţei de urgenţă a Guvernului nr. 73/2019, Ordonanţei Guvernului nr. 39/2000 coroborate cu Ordonanţa Guvernului nr. 14/2023, Hotărârii Guvernului nr. 309/2023, Hotărârii Guvernului nr. 117/2010, Legii nr. 202/2016, Ordonanţei Guvernului nr. 60/2004 şi ale Hotărârii Guvernului nr. 527/2023, titularul legitimației este împuternicit:
    1.	să efectueze supravegherea pe teritoriul României a conformităţii elementelor constitutive de interoperabilitate cu cerinţele esenţiale;
    2.	să efectueze supravegherea, prin acţiuni specifice de control, inspecție şi audit, utilizării în activităţile de întreţinere a subsistemelor structurale şi a vehiculelor feroviare a componentelor critice pentru siguranţă;
    3.	să efectueze supravegherea prin audit, inspecţie pe procese şi control/inspecţie de stat a operatorilor de transport feroviar, administratorului/gestionarilor de infrastructură feroviară, operatorilor care efectuează numai manevră feroviară, gestionarilor de linii ferate industriale, deţinătorilor de linii de metrou pentru transportul de călători/operatorilor care efectuează transport urban cu metroul, entităţilor responsabile cu întreţinerea vehiculelor feroviare, operatorilor economici care efectuează funcţiile de întreţinere, operatorilor care efectuează transport pe căile ferate cu caracter de patrimoniu, de muzeu sau turistic şi/sau alt tip de transport pe şine, pe durata valabilităţii documentelor emise de ASFR;
    4.	să efectueze monitorizarea menținerii îndeplinirii cerinţelor de acordare a licenţelor de către operatorii care efectuează transport feroviar, operatorii care efectuează numai manevră feroviară, operatorii care efectuează transport urban cu metroul şi intermediarii activităţilor de transport pe calea ferată;
    5.	să efectueze supravegherea subsistemelor de control-comandă şi semnalizare terestre, energie şi infrastructură;
    6.	să efectueze supravegherea respectării Regulamentului (UE) 2021/782 privind drepturile şi obligaţiile călătorilor din transportul feroviar;
    7.	să constate, să încadreze şi să sancţioneze faptele contravenţionale produse în desfăşurarea operaţiunilor de transport feroviar şi cu metroul, în conformitate cu atribuţiile prevăzute în actele normative în vigoare;
    8.	să solicite şi să obțină datele, informațiile şi documentele necesare îndeplinirii atribuțiilor de audit, control şi inspecție de stat.

    În vederea exercitării atribuțiilor, posesorul legitimaţiei are drept de acces pe toate mijloacele de transport feroviar, inclusiv în cabina de conducere a acestora, în unităţile cu specific feroviar care funcționează sub autoritatea sau în subordinea MTI, în unităţile operatorilor de transport feroviar/operatorilor care efectuează numai manevră feroviară, în unităţile administratorului şi gestionarilor de infrastructură feroviară, pe liniile ferate industriale, în unităţile certificate ca entități responsabile cu întreţinerea vehiculelor feroviare, în unităţile aferente transportului cu metroul, precum şi în unităţile destinate altor tipuri de transport pe şine.

    Prezenta legitimație este un document cu regim special, nominal şi netransmisibil!





 --}}

    <div class="max-w-7xl mx-auto bg-white shadow-md rounded p-5">
        <h1 class="text-2xl font-bold mb-5">INTRODUCERE PERSONAL SPECIAL</h1>
        <div class='big-container'>
            <div class='w-4/5'>
                <select id="tip-permis" name="tip-permis"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    <option value="">Selectează Tip permis</option>
                    <option value="ASFR">ASFR</option>
                    <option value="AFER">AFER</option>
                    <option value="AGIFER">AGIFER</option>
                    <option value="MTI">MTI</option>
                </select>
                <input id='numar-serie-special' type="text" placeholder="Număr Serie"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='personal-special-nume' type="text" placeholder="Nume"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='personal-special-prenume' type="text" placeholder="Prenume"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='personal-special-functie' type="text" placeholder="Functie"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='personal-special-unitate' type="text" placeholder="Unitate"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='personal-special-ministru' type="text" placeholder="Ministru" value="CIPRIAN-CONSTANTIN ȘERBAN"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <label for="Data emitere"
                    class="block mb-2 mt-2 text-sm font-medium text-gray-900  dark:text-white">Data emitere</label>
                <input id='data-emitere-special' type="date" placeholder="Dată Emitere ZZ//LL/YYYY"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <label for="Data valabilitate"
                    class="block mb-2 mt-2 text-sm font-medium text-gray-900  dark:text-white">Data valabilitate</label>
                <input id='data-valabilitate-special' type="date" placeholder="Dată Valabilitate ZZ//LL/YYYY"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <label for="fotografie-permis-special"
                    class="block mb-2 text-sm font-medium text-gray-900  dark:text-white">Fotografie permis</label>
                <input type="file" id="fotografie-permis-special" name="fotografie-permis-special" accept="image/*"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <div class="mb-5 w-1/5">
                <div class="mt-4 flex justify-center items-center">
                    <img id="preview-image" src="" alt="Preview" class="hidden"
                        style="width: 132px; height: 170px;">
                </div>

            </div>
        </div>
        {{-- <div>
            <label class="mt-2 type3 block text-sm font-medium text-gray-700">Tip de autorizatie solicitat</label>
            <div class="type3 mt-1 p-2 border border-gray-300 bg-gray-50 rounded-md flex flex-row">
                <div class="flex flex-col gap-2 mr-2">
                    <p>Funcție:</p>
                    <p id='tip-autorizatie-special'></p>
                </div>
                <div class="flex flex-col gap-2" id='tip-autorizatie-solicitat'>

                </div>
            </div>
        </div> --}}
        <div class="flex justify-between">
            <button id='submitBtn'
                class="mt-2 bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">Continuare</button>
            <button class=" mt-2 bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded"
                onclick="window.location.href='/dashboard'">Abandonare</button>

        </div>
    </div>

</x-app-layout>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const formData = {};

        const tip = this.value;
        const unitateInput = document.getElementById('personal-special-unitate');

        const map = {
            'AFER': 'Autoritatea Feroviară Română - AFER',
            'ASFR': 'Autoritatea de Siguranță Feroviară Română - ASFR',
            'AGIFER': 'Agenția de Investigare Feroviară Română - AGIFER',
            'MTI': 'Ministerul Transporturilor și Infrastructurii',
        };

        unitateInput.value = map[tip] || '';
        const updateUnitate = () => {
            const tip = document.getElementById('tip-permis').value; // preluăm tipul de permis selectat
            const unitateInput = document.getElementById('personal-special-unitate'); // câmpul unitate
            unitateInput.value = map[tip] || ''; // actualizăm câmpul unitate
        }
        
        document.getElementById('tip-permis').addEventListener('change', updateUnitate);

        updateUnitate();

        const onSubmit = async () => {
            const mapErrorsToFields = {
                tipPermis: 'Tip permis',
                nrSerie: 'Număr serie',
                nume: 'Nume',
                prenume: 'Prenume',
                functie: 'Funcție',
                unitate: 'Unitate',
                ministru: 'Ministru',
                dataEmitere: 'Data emitere',
                valabilitate: 'Data valabilitate',
                poza: 'Fotografie permis'
            };
            
            formData.tip_permis = document.getElementById('tip-permis')?.value;
            formData.nr_serie = document.getElementById('numar-serie-special')?.value;
            formData.nume = document.getElementById('personal-special-nume')?.value;
            formData.prenume = document.getElementById('personal-special-prenume')?.value;
            formData.functie = document.getElementById('personal-special-functie')?.value;
            formData.unitate = document.getElementById('personal-special-unitate')?.value;
            formData.ministru = document.getElementById('personal-special-ministru')?.value;
            formData.data_emitere = document.getElementById('data-emitere-special')?.value;
            formData.data_valabilitate = document.getElementById('data-valabilitate-special')?.value;
            formData.poza = formData.poza || ''; // completat în FileReader


            // cum stochez pozele sa le pot accesa cand o sa am nevoie?
            

            if (
                !formData.tip_permis ||
                !formData.nr_serie ||
                !formData.nume ||
                !formData.prenume ||
                !formData.functie ||
                // !formData.unitate ||
                // !formData.ministru ||
                !formData.data_emitere ||
                !formData.data_valabilitate ||
                !formData.poza
            ) {
                alert('Eroare de validare: Introduceți toate datele');
                return;
            }


            try {
                const response = await fetch('/permisSpecial/store', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(formData),
                    redirect: 'manual'
                });

                if (response.ok) {
                    alert('Permisul a fost înregistrat cu succes!');
                } else if (response.status === 422) {
                    const errorData = await response.json();
                    let fieldErrors = '';

                    Object.keys(errorData.errors).forEach(field => {
                        if (mapErrorsToFields[field]) {
                            fieldErrors.length > 0
                                ? fieldErrors += `, ${mapErrorsToFields[field]}`
                                : fieldErrors += mapErrorsToFields[field];
                        }
                    });

                    alert(`${errorData.message || 'Eroare de validare'}: ${fieldErrors}`);
                } else {
                    const contentType = response.headers.get('content-type') || '';
                    const isJson = contentType.includes('application/json');
                    const errorData = isJson ? await response.json() : {};
                    console.log(JSON.stringify(response.json()));
                    alert('Eroare: ' + (errorData.message || 'Eroare necunoscută.'));
                }
            } catch (error) {
                console.error('Eroare:', error);
                alert('A avut loc o eroare. Verificați toate câmpurile și încercați din nou.');
            }



        };


        document.getElementById('submitBtn').addEventListener('click', function(event) {
            event.preventDefault();
            onSubmit();
        });

        document.getElementById('fotografie-permis-special').addEventListener('change', function(event) {
            const file = event.target.files[0];
            const previewImage = document.getElementById('preview-image');

            if (file) {
                const reader = new FileReader();

                
                reader.onload = function(e) {
                    formData.poza = e.target.result;
                    previewImage.src = e.target.result;
                    previewImage.classList.remove('hidden'); 
                };

                reader.readAsDataURL(file); 
            } else {
                previewImage.classList.add('hidden'); 
            }
        });
    });
</script>
