<!DOCTYPE html>
<html lang="ro">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-size: 10px;
            font-family: <PERSON><PERSON><PERSON><PERSON>, sans-serif;
            margin-left: 70px;
            margin-right: 70px;
        }

        .container {
            display: flex;
        }

        .text {
            flex: 1;
        }

        .image {
            margin-right: 10px
        }

        .bold {
            font-weight: bold;
        }

        .head {
            text-align: center;
            align-content: center;
            font-weight: bold;
            font-size: 14px;
            line-height: 17px;
        }

        .centered {
            text-align: center;
            align-content: center;
            font-size: 12px;
            margin-top: -9px;
        }

        .text {
            text-align: justify;
            font-size: 12px;
            width: 8in;
        }

        .ticket {
            width: 8in;
        }

        .logo {
            width: 8in;
        }

        img {
            max-width: inherit;
            width: inherit;
        }


        .center {
            margin-left: 0.1in;
            margin-right: 0.1in;
        }



        .bottom-item {
            width: 30%;
        }

        .img-fit {
            width: 100%;
            height: 100px;
            max-width: 100%;
        }

        @page {
            margin: 0.1in;
            size: 11.69in 8.27in;
        }

        .bottom-items-table {
            margin-top: 20px;
            width: 100%;
            table-layout: fixed;
            border: none;
        }

        .bottom-item {
            display: table-cell;
            vertical-align: top;
            padding: 10px;
            border: none;
            text-align: center;
        }

        .bottom-item p {
            margin: 0;
        }
        .td-persoane {
            border: 0.75px solid #000000;
            padding: 9px;
            height: auto;
            text-align: center;
        }

        .th-persoane {
            border: 0.75px solid #000000;
            padding: 9px;
            height: auto;
            text-align: center;
        }
        .paragraph {
            margin-bottom: 5px;
        }
        .table-antet {
            width: 100%;
            border-collapse: collapse;
        }
        .table-container2 {
            width: 100%; /* Latimea unei pagini A4 */
            margin: 0 auto; /* Centreaza containerul pe pagina */
            padding: 8px; /* Adaugă puțin spațiu între container și marginea paginii */
            box-sizing: border-box;
        }
        td.first-column {
            width: 120px;
            height: 20px;
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }
        .tr-height {
            height: 2px;
            padding: 0; /* Eliminăm padding-ul din celule */
            text-align: center;
            line-height: 1; /* Reducem line-height pentru a apropia textul */
        }
        .line-color {
            width: 100%;
            height: 0.5mm;
            margin-bottom: 1mm;
            margin-top: 1mm;
            background-color: black;
        }
        p {
            margin: 2px;
            padding: 0px;
        }
    </style>
</head>

@php
    $dataISF = $comisie->data_ISF;
    $date1 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataISF);
    $formattedDataISF = $date1->format('d/m/Y');
    $dataExamenT1 = $comisie->data_exam_t1;
    $date1 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataExamenT1);
    $formattedDataExamenT1 = $date1->format('d/m/Y');
    //format data EXAMEN PRACTIC
    $dataExamenP1 = $comisie->data_exam_p1;
    $date2 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataExamenP1);
    $formattedDataExamenP1 = $date2->format('d/m/Y');

    $dataPvIntrare = $comisie->pv_data_intrare;
    $date3 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataPvIntrare);
    $formattedDataPvIntrare = $date3->format('d/m/Y');

    $dataPvIesire = $comisie->pv_data_iesire;
    $date4 = \Carbon\Carbon::createFromFormat('Y-m-d', $dataPvIesire);
    $formattedDataPvIesire = $date4->format('d/m/Y');
@endphp

<body>

    <header>
        <div class="table-container2">
            <table class="table-antet">
                <tr>
                    <td class="first-column" rowspan="5"><img class="img-fit" src="{{ public_path('/ASFR-logo.png') }}" alt="Image"></td>
                    <td class="tr-height ">MINISTERUL TRANSPORTURILOR ȘI INFRASTRUCTURII</td>
                </tr>
                <tr>
                    <td class="tr-height ">Autoritatea de Siguranță Feroviară Română - ASFR</td>
                </tr>
                <tr>
                    <td class="tr-height ">București, Calea Griviței, nr. 393, sector 1</td>
                </tr>
                <tr>
                    <td class="tr-height ">Cod  Poștal: 010719, Cod Fiscal: 48008564,</td>
                </tr>
                <tr>
                    <td class="tr-height ">E-mail:  isf.{{strtr(strtolower($isfuri->ISF), 
                        ['ă'=>'a', 'â'=>'a', 'ș'=>'s', 'ț'=>'t', 'î'=>'i', 'Ă'=>'A', 'Â'=>'A', 'Ș'=>'S', 'Ț'=>'T', 'Î'=>'I'])}}@sigurantaferoviara.ro  www.sigurantaferoviara.ro</td>
                </tr>
            </table>
            <div class="line-color"></div>
        </div>
    </header>
    <p class="bold paragraph">
        @if ($isfuri->ISF === 'București' || $isfuri->ISF === 'Brașov' || $isfuri->ISF === 'Craiova' || $isfuri->ISF === 'Galați')
            Ieșire CISF
        @else Ieșire ISF
        @endif
        <span
            class="bold">{{ $isfuri->ISF }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Intrare
        CENAFER
    </p>
    <p class="paragraph">
        Nr. <span class="bold">{{ $isfuri->cod_serviciu }} / {{ $comisie->pv_nr_iesire }} / din
            {{ $formattedDataPvIesire }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <span class="bold">Nr. {{ $comisie->pv_nr_intrare }} / din
            {{ $formattedDataPvIntrare }}</span>
    </p>
    <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PROCES-VERBAL
        DE <span class="bold">{{ $solicitare->tip_comisie }}</span></p>
    <p>Încheiat astăzi {{ $formattedDataPvIntrare }}, în
        3 exemplare cu ocazia examinării personalului cu responsabilități
        în siguranța circulației care urmează să desfășoare, pe proprie răspundere, activități specifice
        transportului feroviar, la solicitarea <span class="bold">{{ $solicitare->solicitant_nume }}
            {{ $solicitare->solicitant_prenume }}</span> în vederea autorizării pentru:<p></p>
        <span class="bold">{{ $solicitare->functie }}</span><p></p>
        @if (!empty($solicitare->autorizatie))
            <p><span class="bold">{{ $solicitare->autorizatie }}</span></p>
        @endif
        @if (!empty($solicitare->domeniu))
            <p><span class="bold">{{ $solicitare->domeniu }}</span></p>
        @endif
    </p>

    <p>Examinarea teoretică a fost efectuată la <span class="bold">{{ $comisie->loc_exam_t }}</span> în data
        de
        <span class="bold">{{ $formattedDataExamenT1 }}</span>
    </p>
    <p>Examinarea practică a fost efectuată la <span class="bold">{{ $comisie->loc_exam_p }}</span> în data de
        <span class="bold">{{ $formattedDataExamenP1 }}</span>
    </p>
    <p>Rezultatele examinării sunt consemnate în tabelul de mai jos.</p>
    <table cellspacing="0" cellpadding="0" class="center">
        <tbody>
            <tr>
                <th class="th-persoane" rowspan="4">Nr.Crt</th>
                <th class="th-persoane" colspan='11'>Nume, prenume</th>
                <th class="th-persoane" colspan='4'>CNP</th>
                <th class="th-persoane" colspan='13'>Mențiuni</th>
            </tr>
            <tr>
                <td class="td-persoane" colspan='15'>Note pe subiecte la examenul teoretic</td>
                <td class="bold td-persoane" colspan="3" rowspan="2">Medie la materia</td>
                <td class="bold td-persoane" rowspan="3">Medie generală</td>
                <td class="td-persoane" colspan="3" rowspan="3">Calificativ examen teoretic</td>
                <td class="td-persoane" colspan="3" rowspan="3">Calificativ examen practic</td>
                <td class="td-persoane" colspan="3" rowspan="3">Calificativ general examen</td>
            </tr>
            <tr>
                <td class="td-persoane" colspan='5'>Materia 1</td>
                <td class="td-persoane" colspan='5'>Materia 2</td>
                <td class="td-persoane" colspan='5'>Materia 3</td>
            </tr>
            <tr>
                <td class="td-persoane">1</td>
                <td class="td-persoane">2</td>
                <td class="td-persoane">3</td>
                <td class="td-persoane">4</td>
                <td class="td-persoane">5</td>
                <td class="td-persoane">1</td>
                <td class="td-persoane">2</td>
                <td class="td-persoane">3</td>
                <td class="td-persoane">4</td>
                <td class="td-persoane">5</td>
                <td class="td-persoane">1</td>
                <td class="td-persoane">2</td>
                <td class="td-persoane">3</td>
                <td class="td-persoane">4</td>
                <td class="td-persoane">5</td>
                <td class="bold td-persoane">1</td>
                <td class="bold td-persoane">2</td>
                <td class="bold td-persoane">3</td>
            </tr>
            <tr>
                <td class="bold td-persoane" rowspan="2">1</td>
                <td class="bold td-persoane" colspan="11">{{ $solicitare->solicitant_nume }}
                    {{ $solicitare->solicitant_prenume }}</td>
                <td class="bold td-persoane" colspan="4">{{ $solicitare->cnp }}</td>
                <td class="td-persoane" colspan="13"></td>
            </tr>
            <tr>
                <td class="td-persoane">{{ (int) $solicitare->mat1_nota1 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat1_nota2 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat1_nota3 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat1_nota4 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat1_nota5 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat2_nota1 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat2_nota2 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat2_nota3 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat2_nota4 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat2_nota5 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat3_nota1 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat3_nota2 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat3_nota3 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat3_nota4 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat3_nota5 }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat1_medie }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat2_medie }}</td>
                <td class="td-persoane">{{ (int) $solicitare->mat3_medie }}</td>
                <td class="td-persoane">{{ $solicitare->medie_t }}</td>
                <td class="td-persoane" colspan="3">{{ ucwords($solicitare->calificativ_t) }}</td>
                <td class="td-persoane" colspan="3">{{ ucwords($solicitare->calificativ_p) }}</td>
                <td class="td-persoane" colspan="3">{{ ucwords($solicitare->calificativ) }}</td>
            </tr>
        </tbody>
    </table>
    <p><span class="bold">NOTĂ</span> 1. Până la data emiterii de către ASFR a autorizației/autorizațiilor, care au făcut obiectul examinării teoretice și practice personalul declarat “Corespunzător” poate exercita funcția, sau, după caz, să manipuleze tipurile de instalații SC și/sau să desfășoare activitățile specifice pentru care a fost autorizat conform prezentului proces verbal.
    <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. Prezentul Proces Verbal s-a întocmit în 3 exemplare cu valoare de original, fiecare membru al comisiei păstrând un exemplar.</p>


    <table class="bottom-items-table">
        <tr>
            <td class="bottom-item">
                <p>Comisia de examinare:</p>
                <p>Președinte: <span class="bold">{{ $solicitare->nume }}
                        {{ $solicitare->prenume }}</span></p>
                        <p>.................................</p>
                <p>Membri:
                    @if (!empty($comisie->nume_cenafer1) && !empty($comisie->prenume_cenafer1))
                        <p><span class="bold">{{ $comisie->nume_cenafer1 }}
                                {{ $comisie->prenume_cenafer1 }}</span></p>
                                <p>.................................</p>
                        <p></p>
                    @endif
                    @if (!empty($comisie->nume_cenafer2) && !empty($comisie->prenume_cenafer2))
                        <p><span class="bold">{{ $comisie->nume_cenafer2 }}
                                {{ $comisie->prenume_cenafer2 }}</span></p>
                                <p>.................................</p>
                        <p></p>
                    @endif
                    @if (!empty($comisie->nume_uf) && !empty($comisie->prenume_uf))
                </p><span class="bold">{{ $comisie->nume_uf }}
                    {{ $comisie->prenume_uf }}</span></p>
                    <p>.................................</p>
                @endif
                </p>
            </td>
            <td class="bottom-item">
                <p class="bold">Solicitată de 
                    @if ($isfuri->ISF === 'București' || $isfuri->ISF === 'Brașov' || $isfuri->ISF === 'Craiova' || $isfuri->ISF === 'Galați')
                        CISF
                    @else ISF
                    @endif prin actul</p>
                <p>cu numărul <span class="bold">{{ $comisie->nr_ISF }}</span></p>
                <p>din data de <span class="bold">{{ $formattedDataISF }}</span></p>
            </td>
        </tr>
    </table>

</body>

</html>
