{{-- @php
    dd($solicitariPj);
@endphp --}}
<style>
    /* <PERSON>minare sageti in Webkit (Chrome, Safari) */
    input[type="number"]::-webkit-outer-spin-button,
    input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
        color: black;
        bockground-color: white;
    }

    /* Eliminare sageti in Firefox */
    input[type="number"] {
        -moz-appearance: textfield;
    }

    .materie-css {
        justify-content: center;
        text-align: center;
    }

    .intrare-iesire {
        display: flex;

    }

    select {
        -webkit-appearance: none;
        /* Safari/Chrome */
        -moz-appearance: none;
        /* Firefox */
        appearance: none;
        /* Standard syntax */
        background-image: none;
        /* Remove the default dropdown arrow */
    }
</style>

<x-app-layout>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formular Note Examene</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <div class="max-w-full mx-auto bg-white shadow-md rounded p-5 ">
        <h1 class="text-2xl font-bold mb-5">Rezultate Examinare</h1>
        @if (isset($solicitarePf))
            <div class="flex space-x-4">

                <div class="mb-5">
                    <table class="min-w-full border-collapse">
                        <thead>
                            <tr>
                                <th class="border p-2">Nume</th>
                                <th class="border p-2">Prenume</th>
                                <th class="border p-2">CNP</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if ($solicitarePf)
                                <!-- Verifică dacă solicitarea există -->
                                <tr>
                                    <td class="border p-2">{{ $solicitarePf->solicitant_nume }}</td>
                                    <td class="border p-2">{{ $solicitarePf->solicitant_prenume }}</td>
                                    <td class="border p-2">{{ $solicitarePf->cnp }}</td>
                                    <td class="border p-2 hidden" id='pf-oameni' data-id={{ $solicitarePf->oameni }}>
                                        {{ $solicitarePf->oameni }}</td>
                                </tr>
                            @else
                                <tr>
                                    <td class="border p-2" colspan="3">Solicitarea nu a fost găsită.</td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
                <div class="flex flex-wrap">
                    <!-- Materia 1 -->
                    <div class="mb-5 flex flex-col w-45 materie-css">
                        <h2 class="text-xl font-semibold mb-2">Materia 1</h2>
                        <div class="grid grid-cols-5 gap-1">
                            @for ($i = 1; $i <= 5; $i++)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">{{ $i }}</label>
                                    <select id="pf_mat1_nota{{ $i }}" name="mat1_nota{{ $i }}"
                                        class="nota-select mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                        required>
                                        @for ($j = 0; $j <= 10; $j += 0.5)
                                            <option value="{{ $j }}"
                                                {{ old('mat1_nota' . $i, $solicitarePf->{'mat1_nota' . $i}) == $j ? 'selected' : '' }}>
                                                {{ $j }}</option>
                                        @endfor
                                    </select>
                                </div>
                            @endfor
                        </div>
                        <div class="mt-4">
                            <label class="text-xl font-semibold mb-2">Media Materia 1</label>
                            <input type="number" id="pf_mat1_medie" min="0" max="10" name="mat1_medie"
                                value="{{ $solicitarePf->mat1_medie ?? 0 }}"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                required readonly>
                        </div>
                    </div>

                    <!-- Materia 2 -->
                    <div class="mb-5 flex flex-col w-45 materie-css">
                        <h2 class="text-xl font-semibold mb-2">Materia 2</h2>
                        <div class="grid grid-cols-5 gap-1">
                            @for ($i = 1; $i <= 5; $i++)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">{{ $i }}</label>
                                    {{-- <input type="number" id="pf_mat2_nota{{ $i }}" min="0" max="10"
                                    name="mat2_nota{{ $i }}"
                                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    required> --}}
                                    <select id="pf_mat2_nota{{ $i }}" name="mat2_nota{{ $i }}"
                                        class="nota-select mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                        required>
                                        @for ($j = 0; $j <= 10; $j += 0.5)
                                            <option value="{{ $j }}"
                                                {{ old('mat2_nota' . $i, $solicitarePf->{'mat2_nota' . $i}) == $j ? 'selected' : '' }}>
                                                {{ $j }}</option>
                                        @endfor
                                    </select>
                                </div>
                            @endfor
                        </div>
                        <div class="mt-4">
                            <label class="text-xl font-semibold mb-2">Media Materia 2</label>
                            <input type="number" id="pf_mat2_medie" min="0" max="10" name="mat2_medie"
                                value="{{ $solicitarePf->mat2_medie ?? 0 }}"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                required readonly>
                        </div>
                    </div>

                    <!-- Materia 3 -->
                    <div class="mb-5 flex flex-col w-45 materie-css">
                        <h2 class="text-xl font-semibold mb-2">Materia 3</h2>
                        <div class="grid grid-cols-5 gap-1">
                            @for ($i = 1; $i <= 5; $i++)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">{{ $i }}</label>
                                    {{-- <input type="number" id="pf_mat3_nota{{ $i }}" min="0"
                                    max="10" name="mat3_nota{{ $i }}"
                                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    required> --}}
                                    <select id="pf_mat3_nota{{ $i }}" name="mat3_nota{{ $i }}"
                                        class="nota-select mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                        required>
                                        @for ($j = 0; $j <= 10; $j += 0.5)
                                            <option value="{{ $j }}"
                                                {{ old('mat3_nota' . $i, $solicitarePf->{'mat3_nota' . $i}) == $j ? 'selected' : '' }}>
                                                {{ $j }}</option>
                                        @endfor
                                    </select>
                                </div>
                            @endfor
                        </div>
                        <div class="mt-4">
                            <label class="text-xl font-semibold mb-2">Media Materia 3</label>
                            <input type="number" id="pf_mat3_medie" min="0" max="10" name="mat3_medie"
                                value="{{ $solicitarePf->mat3_medie ?? 0 }}"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                required readonly>
                        </div>
                    </div>

                    <!-- Media Generală -->
                    <div class="mb-5">
                        <label class="text-xl font-semibold mb-2">Media Generală</label>
                        <input type="number" id="pf_medie_t" min="0" max="10" name="medie_t"
                            value="{{ $solicitarePf->medie_t ?? 0 }}"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            required readonly>
                    </div>
                </div>


                <div>
                    <label class="text-xl font-semibold mb-2">CALIFICATIVE EXAMENE</label>
                    <div class="mb-5">
                        <label class="text-xl font-semibold mb-2">Teoretic</label>
                        <div class="mt-2">
                            <label class="inline-flex items-center mb-2">
                                <input type="radio" id="pf_corespunzator_t" name="calificativ_teoretic"
                                    value="corespunzator" class="form-radio text-indigo-600"
                                    {{ $solicitarePf->calificativ_t == 'corespunzator' ? 'checked' : '' }}>
                                <span class="ml-2">Corespunzător</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" id="pf_necorespunzator_t" name="calificativ_teoretic"
                                    value="necorespunzator" class="form-radio text-indigo-600"
                                    {{ $solicitarePf->calificativ_t == 'necorespunzator' ? 'checked' : '' }}>
                                <span class="ml-2">Necorespunzător</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" id="pf_absent_t" name="calificativ_teoretic" value="absent"
                                    class="form-radio text-indigo-600"
                                    {{ $solicitarePf->calificativ_t == 'absent' ? 'checked' : '' }}>
                                <span class="ml-2">Absent</span>
                            </label>
                        </div>
                    </div>

                    <div class="mb-5">
                        <label class="text-xl font-semibold mb-2">Practic</label>
                        <div class="mt-2">
                            <label class="inline-flex items-center mb-2">
                                <input type="radio" id="pf_corespunzator_p" name="calificativ_practic"
                                    value="corespunzator" class="form-radio text-indigo-600"
                                    {{ $solicitarePf->calificativ_p == 'corespunzator' ? 'checked' : '' }}>
                                <span class="ml-2">Corespunzător</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" id="pf_necorespunzator_p" name="calificativ_practic"
                                    value="necorespunzator" class="form-radio text-indigo-600"
                                    {{ $solicitarePf->calificativ_p == 'necorespunzator' ? 'checked' : '' }}>
                                <span class="ml-2">Necorespunzător</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" id="pf_absent_p" name="calificativ_practic" value="absent"
                                    class="form-radio text-indigo-600"
                                    {{ $solicitarePf->calificativ_p == 'Absent' ? 'checked' : '' }}>
                                <span class="ml-2">Absent</span>
                            </label>
                        </div>
                    </div>

                    <div class="mt-4">
                        <label class="text-xl font-semibold mb-2">General</label>
                        <input type="text" id="pf_g" name="calificativ_medie"
                            value="{{ $solicitarePf->calificativ ?? 0 }}"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            required readonly>
                    </div>
                </div>

            </div>
        @elseif (isset($solicitariPj))
            <div class="flex flex-col space-y-4">
                @foreach ($solicitariPj as $index => $solicitarePj)
                    <div style="border-top: 2px solid #D1D5DB; margin-bottom: 20px;"></div>
                    <div class='flex space-x-4'>
                        <div class="mb-5">
                            <table class="min-w-full border-collapse">
                                <thead>
                                    <tr>
                                        <th class="border p-2">Nume</th>
                                        <th class="border p-2">Prenume</th>
                                        <th class="border p-2">CNP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if ($solicitarePj)
                                        <!-- Verifică dacă solicitarea există -->
                                        <tr>
                                            <td class="border p-2">{{ $solicitarePj->solicitant_nume }}</td>
                                            <td class="border p-2">{{ $solicitarePj->solicitant_prenume }}</td>
                                            <td class="border p-2">{{ $solicitarePj->cnp }}</td>
                                            <td class="border p-2 hidden" id='pj-oameni-{{ $index }}'
                                                data-id={{ $solicitarePj->oameni }}>
                                                {{ $solicitarePj->oameni }}</td>
                                        </tr>
                                    @else
                                        <tr>
                                            <td class="border p-2" colspan="3">Solicitarea nu a fost găsită.</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        <div class="flex flex-wrap">

                            <!-- Materia 1 -->
                            <div class="mb-5 flex flex-col w-45 materie-css">
                                <h2 class="text-xl font-semibold mb-2">Materia 1</h2>
                                <div class="grid grid-cols-5 gap-1">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <div>
                                            <label
                                                class="block text-sm font-medium text-gray-700">{{ $i }}</label>
                                            {{-- <input type="number"
                                            id="{{ $solicitarePj->oameni }}pj_mat1_nota{{ $i }}"
                                            min="0" max="10" name="mat1_nota{{ $i }}"
                                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                            required> --}}

                                            <select id="{{ $solicitarePj->oameni }}pj_mat1_nota{{ $i }}"
                                                name="mat1_nota{{ $i }}"
                                                class="nota-select mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                                required>
                                                @for ($j = 0; $j <= 10; $j += 0.5)
                                                    <option value="{{ $j }}"
                                                        {{ old('mat1_nota' . $i, $solicitarePj->{'mat1_nota' . $i}) == $j ? 'selected' : '' }}>
                                                        {{ $j }}</option>
                                                @endfor
                                            </select>
                                        </div>
                                    @endfor
                                </div>
                                <div class="mt-4">
                                    <label class="text-xl font-semibold mb-2">Media Materia 1</label>
                                    <input type="number" id="{{ $solicitarePj->oameni }}pj_mat1_medie"
                                        min="0" max="10" name="mat1_medie"
                                        value="{{ $solicitarePj->mat1_medie ?? 0 }}"
                                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                        required readonly>
                                </div>
                            </div>

                            <!-- Materia 2 -->
                            <div class="mb-5 flex flex-col w-45 materie-css">
                                <h2 class="text-xl font-semibold mb-2">Materia 2</h2>
                                <div class="grid grid-cols-5 gap-1">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <div>
                                            <label
                                                class="block text-sm font-medium text-gray-700">{{ $i }}</label>
                                            {{-- <input type="number"
                                            id="{{ $solicitarePj->oameni }}pj_mat2_nota{{ $i }}"
                                            min="0" max="10" name="mat2_nota{{ $i }}"
                                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                            required> --}}
                                            <select id="{{ $solicitarePj->oameni }}pj_mat2_nota{{ $i }}"
                                                name="mat2_nota{{ $i }}"
                                                class="nota-select mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                                required>
                                                @for ($j = 0; $j <= 10; $j += 0.5)
                                                    <option value="{{ $j }}"
                                                        {{ old('mat2_nota' . $i, $solicitarePj->{'mat2_nota' . $i}) == $j ? 'selected' : '' }}>
                                                        {{ $j }}</option>
                                                @endfor
                                            </select>
                                        </div>
                                    @endfor
                                </div>
                                <div class="mt-4">
                                    <label class="text-xl font-semibold mb-2">Media Materia 2</label>
                                    <input type="number" id="{{ $solicitarePj->oameni }}pj_mat2_medie"
                                        min="0" max="10" name="mat2_medie"
                                        value="{{ $solicitarePj->mat2_medie ?? 0 }}"
                                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                        required readonly>
                                </div>
                            </div>

                            <!-- Materia 3 -->
                            <div class="mb-5 flex flex-col w-45 materie-css">
                                <h2 class="text-xl font-semibold mb-2">Materia 3</h2>
                                <div class="grid grid-cols-5 gap-1">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <div>
                                            <label
                                                class="block text-sm font-medium text-gray-700">{{ $i }}</label>
                                            <select id="{{ $solicitarePj->oameni }}pj_mat3_nota{{ $i }}"
                                                name="mat3_nota{{ $i }}"
                                                class="nota-select mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                                required>
                                                @for ($j = 0; $j <= 10; $j += 0.5)
                                                    <option value="{{ $j }}"
                                                        {{ old('mat3_nota' . $i, $solicitarePj->{'mat3_nota' . $i}) == $j ? 'selected' : '' }}>
                                                        {{ $j }}</option>
                                                @endfor
                                            </select>
                                        </div>
                                    @endfor
                                </div>

                                <div class="mt-4">
                                    <label class="text-xl font-semibold mb-2">Media Materia 3</label>
                                    <input type="number" id="{{ $solicitarePj->oameni }}pj_mat3_medie"
                                        min="0" max="10" name="mat3_medie"
                                        value="{{ $solicitarePj->mat3_medie ?? 0 }}"
                                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                        required readonly>
                                </div>
                            </div>

                            <!-- Media Generală -->
                            <div class="mb-5">
                                <label class="text-xl font-semibold mb-2">Media Generală</label>
                                <input type="number" id="{{ $solicitarePj->oameni }}pj_medie_t" min="0"
                                    max="10" name="medie_t" value="{{ $solicitarePj->medie_t ?? 0 }}"
                                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    required readonly>
                            </div>
                        </div>
                        <div>
                            <label class="text-xl font-semibold mb-2">CALIFICATIVE EXAMENE</label>
                            <div class="mb-5">
                                <label class="text-xl font-semibold mb-2">Teoretic</label>
                                <div class="mt-2">
                                    <label class="inline-flex items-center mb-2">
                                        <input type="radio" id="{{ $solicitarePj->oameni }}pj_corespunzator_t"
                                            name="{{ $solicitarePj->oameni }}pj_calificativ_teoretic"
                                            value="corespunzator" class="form-radio text-indigo-600"
                                            {{ $solicitarePj->calificativ_t == 'corespunzator' ? 'checked' : '' }}>
                                        <span class="ml-2">Corespunzător</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="radio" id="{{ $solicitarePj->oameni }}pj_necorespunzator_t"
                                            name="{{ $solicitarePj->oameni }}pj_calificativ_teoretic"
                                            value="necorespunzator" class="form-radio text-indigo-600"
                                            {{ $solicitarePj->calificativ_t == 'necorespunzator' ? 'checked' : '' }}>
                                        <span class="ml-2">Necorespunzător</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="radio" id="{{ $solicitarePj->oameni }}pj_absent_t"
                                            name="{{ $solicitarePj->oameni }}pj_calificativ_teoretic" value="absent"
                                            class="form-radio text-indigo-600"
                                            {{ $solicitarePj->calificativ_t == 'absent' ? 'checked' : '' }}>
                                        <span class="ml-2">Absent</span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-5">
                                <label class="text-xl font-semibold mb-2">Practic</label>
                                <div class="mt-2">
                                    <label class="inline-flex items-center mb-2">
                                        <input type="radio" id="{{ $solicitarePj->oameni }}pj_corespunzator_p"
                                            name="{{ $solicitarePj->oameni }}pj_calificativ_practic"
                                            value="corespunzator" class="form-radio text-indigo-600"
                                            {{ $solicitarePj->calificativ_p == 'corespunzator' ? 'checked' : '' }}>
                                        <span class="ml-2">Corespunzător</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="radio" id="{{ $solicitarePj->oameni }}pj_necorespunzator_p"
                                            name="{{ $solicitarePj->oameni }}pj_calificativ_practic"
                                            value="necorespunzator" class="form-radio text-indigo-600"
                                            {{ $solicitarePj->calificativ_p == 'necorespunzator' ? 'checked' : '' }}>
                                        <span class="ml-2">Necorespunzător</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="radio" id="{{ $solicitarePj->oameni }}pj_absent_p"
                                            name="{{ $solicitarePj->oameni }}pj_calificativ_practic" value="absent"
                                            class="form-radio text-indigo-600"
                                            {{ $solicitarePj->calificativ_p == 'absent' ? 'checked' : '' }}>
                                        <span class="ml-2">Absent</span>
                                    </label>
                                </div>
                            </div>

                            <div class="mt-4">
                                <label class="text-xl font-semibold mb-2">General</label>
                                <input type="text" id="{{ $solicitarePj->oameni }}pj_g"
                                    name="{{ $solicitarePj->oameni }}pj_calificativ_medie"
                                    value="{{ $solicitarePj->calificativ ?? 0 }}"
                                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    required readonly>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif

        <div class="flex space-x-4 mb-4">

            <div class="mb-4 w-[50px]">
                <label for="pv_nr_intrare_input" class="block text-sm font-medium text-gray-700">Număr CENAFER</label>
                <input type="text" name="pv_nr_intrare_input" id="pv_nr_intrare"
                    value="{{ $comisie->nr_CENAFER ?? 0 }}"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md" required>
            </div>

            <div class="mb-4 w-[50px]">
                <label for="pv_nr_iesire_input" class="block text-sm font-medium text-gray-700">Număr ASFR</label>
                <input type="text" name="pv_nr_iesire_input" id="pv_nr_iesire"
                    value="{{ $comisie->nr_ISF ?? 0 }}"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md" required>
            </div>

            <div class="mb-4 w-[50px]">
                <label for="pv_data_intrare_input" class="block text-sm font-medium text-gray-700">Data
                    Intrare</label>
                <input type="date" name="pv_data_intrare_input" id="pv_data_intrare"
                    value="{{ $comisie->pv_data_intrare ?? 0 }}"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md" required>
            </div>

            <div class="mb-4 w-[50px]">
                <label for="pv_data_iesire_input" class="block text-sm font-medium text-gray-700">Data Ieșire</label>
                <input type="date" name="pv_data_iesire_input" id="pv_data_iesire"
                    value="{{ $comisie->pv_data_iesire ?? 0 }}"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md" required>
            </div>
            <div class="flex justify-end">
                <button type="submit" id="submitFormButton"
                    class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Trimite</button>
            </div>
        </div>
    </div>
</x-app-layout>
<script>
    // luam toate notele:
    // daca e pf - un singur obiect in array
    // daca e pj - multiple obiecte in array
    document.addEventListener('DOMContentLoaded', async function() {


        const solicitariPj = document.querySelectorAll('[id^="pj-oameni-"]');


        const calificativTeoretic = document.getElementsByName('calificativ_teoretic');
        const calificativPractic = document.getElementsByName('calificativ_practic');
        const pfGInput = document.getElementById('pf_g');




        function calculateCalificativGeneral() {
            let teoreticValue = '';
            let practicValue = '';

            // Get the selected value for teoretic
            calificativTeoretic.forEach(radio => {
                if (radio.checked) {
                    teoreticValue = radio.value;
                }
            });

            // If teoretic is absent, automatically set practic to absent
            if (teoreticValue === 'absent') {
                const practicRadios = document.getElementsByName('calificativ_practic');
                practicRadios.forEach(radio => {
                    if (radio.value === 'absent') {
                        radio.checked = true;
                    }
                });
                practicValue = 'absent';
            } else {
                // Get the selected value for practic only if teoretic is not absent
                calificativPractic.forEach(radio => {
                    if (radio.checked) {
                        practicValue = radio.value;
                    }
                });
            }

            let finalResult = "Corespunzător"; // Default is "Corespunzător"

            // Apply the priority logic
            if (teoreticValue === 'absent' || practicValue === 'absent') {
                finalResult = "Absent"; // Absent has the highest priority
            } else if (teoreticValue === 'necorespunzator' || practicValue === 'necorespunzator') {
                finalResult = "Necorespunzător"; // Necorespunzător has priority over Corespunzător
            }

            // Set the calculated result in the pf_g input field
            pfGInput.value = finalResult;
        }

        // Add event listeners to all radio buttons
        calificativTeoretic.forEach(radio => {
            radio.addEventListener('change', calculateCalificativGeneral);
        });

        calificativPractic.forEach(radio => {
            radio.addEventListener('change', calculateCalificativGeneral);
        });

        const solicitarePf = document.getElementById('pf-oameni');
        let formData = {
            note: []
        };



        if (solicitarePf) {
            // Function to calculate Media Generala for PF
            function calculateMediaGenerala() {
                const media1 = parseFloat(document.getElementById('pf_mat1_medie').value) || 0;
                const media2 = parseFloat(document.getElementById('pf_mat2_medie').value) || 0;
                const media3 = parseFloat(document.getElementById('pf_mat3_medie').value) || 0;

                const total = media1 + media2 + media3;
                //const count = (media1 > 0) + (media2 > 0) + (media3 >
                // 0); // Count how many averages are valid
                const mediaGenerala = (total / 3).toFixed(2) || 0;

                document.getElementById('pf_medie_t').value =
                    mediaGenerala; // Store Media Generala in the respective input

                if (parseFloat(mediaGenerala) >= 5 && parseFloat(media1) >= 5 && parseFloat(media2) >= 5 &&
                    parseFloat(media3) >= 5) {
                    // Get all radio buttons by name (they share the same name attribute)
                    const radios = document.getElementsByName(
                        `calificativ_teoretic`);

                    radios.forEach((radio) => {
                        radio.disabled = true; // Disable the radio buttons
                    });
                    const corespunzatorRadio = document.getElementById(`pf_corespunzator_t`);

                    if (corespunzatorRadio) {
                        corespunzatorRadio.checked = true; // Check the "Corespunzător" radio button
                    }

                } else {
                    // Get all radio buttons by name (they share the same name attribute)
                    const radios = document.getElementsByName(
                        `calificativ_teoretic`);

                    radios.forEach((radio) => {
                        radio.disabled = false; // Disable the radio buttons
                    });
                }
            }
            // Function to calculate the average
            function calculateAverage(subject) {
                const notaInputs = document.querySelectorAll(`[id^="pf_${subject}_nota"]`);
                const mediaInput = document.getElementById(`pf_${subject}_medie`);

                let total = 0;

                // Loop through each input and accumulate the values
                notaInputs.forEach(input => {
                    const value = parseFloat(input.value);
                    if (!isNaN(value)) {
                        total += value;
                    }
                });

                // Calculate the average and update the media field
                const average = (total / 5).toFixed(2) || 0;
                mediaInput.value = average;

                return average;


            }

            // Attach event listeners for each subject
            const subjects = ['mat1', 'mat2', 'mat3'];

            subjects.forEach(subject => {
                const notaInputs = document.querySelectorAll(`[id^="pf_${subject}_nota"]`);
                notaInputs.forEach(input => {

                    input.addEventListener('input', () => {
                        let min = parseInt(this.min);
                        let max = parseInt(this.max);
                        let value = parseInt(this.value);
                        console.log(min, max, value);

                        if (isNaN(value)) {
                            this.value = "";
                        } else if (value < min) {
                            this.value = min;
                        } else if (value > max) {
                            this.value = max;
                        }
                        calculateAverage(subject);
                        calculateMediaGenerala
                            (); // Call Media Generala calculation on input change

                    });
                });
            });


            calculateMediaGenerala();


        } else {


            // Function to calculate the average for PJ inputs
            function calculateAveragePj(subject, oameniData) {
                const notaInputs = document.querySelectorAll(`[id^="${oameniData}pj_${subject}_nota"]`);
                const mediaInput = document.getElementById(`${oameniData}pj_${subject}_medie`);

                let total = 0;

                // Loop through each input and accumulate the values
                notaInputs.forEach(input => {
                    const value = parseFloat(input.value);
                    if (!isNaN(value)) {
                        total += value;
                    }
                });

                // Calculate the average and update the media field
                const average = (total / 5).toFixed(2) || 0;
                mediaInput.value = average;

                return average; // Return the average for Media Generala calculation

            }

            // Function to calculate Media Generala for PJ
            function calculateMediaGeneralaPj(oameniData) {
                const media1 = parseFloat(document.getElementById(`${oameniData}pj_mat1_medie`).value) || 0;
                const media2 = parseFloat(document.getElementById(`${oameniData}pj_mat2_medie`).value) || 0;
                const media3 = parseFloat(document.getElementById(`${oameniData}pj_mat3_medie`).value) || 0;

                const total = media1 + media2 + media3;
                console.log('total->', total);
                // const count = (media1 > 0) + (media2 > 0) + (media3 >
                //     0); // Count how many averages are valid
                const mediaGenerala = (total / 3).toFixed(2) || 0;
                console.log('medie generala->', mediaGenerala);

                document.getElementById(`${oameniData}pj_medie_t`).value =
                    mediaGenerala; // Store Media Generala for PJ

                const corespunzatorRadio = document.getElementById(`${oameniData}pj_corespunzator_t`);
                if (parseFloat(mediaGenerala) >= 5 && parseFloat(media1) >= 5 && parseFloat(media2) >= 5 &&
                    parseFloat(media3) >= 5) {
                    // Get all radio buttons by name (they share the same name attribute)
                    const radios = document.getElementsByName(
                        `${oameniData}pj_calificativ_teoretic`);

                    radios.forEach((radio) => {
                        radio.disabled = true; // Disable the radio buttons
                    });
                    console.log('corespunzatorRadio', `${oameniData}pj_corespunzator_t`);

                    if (corespunzatorRadio) {
                        corespunzatorRadio.checked = true; // Check the "Corespunzător" radio button
                    }
                } else {
                    // Get all radio buttons by name (they share the same name attribute)
                    const radios = document.getElementsByName(
                        `${oameniData}pj_calificativ_teoretic`);

                    // radios.forEach((radio) => {
                    //     radio.disabled = false; // Disable the radio buttons
                    // });


                    // if (corespunzatorRadio) {
                    //     corespunzatorRadio.checked = true; // Check the "Corespunzător" radio button
                    // }
                }
            }


            solicitariPj?.forEach((element) => {
                const oameniData = element.getAttribute('data-id');


                const calificativTeoretic = document.getElementsByName(
                    `${oameniData}pj_calificativ_teoretic`);
                const calificativPractic = document.getElementsByName(
                    `${oameniData}pj_calificativ_practic`);
                let pjGInput = document.getElementById(`${oameniData}pj_g`);

                calculateMediaGeneralaPj(
                    oameniData
                );

                calculateCalificativGeneralPj();

                // Function to calculate the "General" result for PJ
                function calculateCalificativGeneralPj() {
                    let teoreticValue = '';
                    let practicValue = '';

                    // Get the selected value for teoretic
                    calificativTeoretic.forEach(radio => {
                        if (radio.checked) {
                            teoreticValue = radio.value;
                        }
                    });

                    // If teoretic is absent, automatically set practic to absent
                    if (teoreticValue === 'absent') {
                        const practicRadios = document.getElementsByName(
                            `${oameniData}pj_calificativ_practic`);
                        practicRadios.forEach(radio => {
                            if (radio.value === 'absent') {
                                radio.checked = true;
                            }
                        });
                        practicValue = 'absent';
                    } else {
                        // Get the selected value for practic only if teoretic is not absent
                        calificativPractic.forEach(radio => {
                            if (radio.checked) {
                                practicValue = radio.value;
                            }
                        });
                    }

                    let finalResult = "Corespunzător"; // Default is "Corespunzător"

                    // Apply the priority logic
                    if (teoreticValue === 'absent' || practicValue === 'absent') {
                        finalResult = "Absent"; // Absent has the highest priority
                    } else if (teoreticValue === 'necorespunzator' || practicValue ===
                        'necorespunzator') {
                        finalResult =
                            "Necorespunzător"; // Necorespunzător has priority over Corespunzător
                    }

                    // Set the calculated result in the respective pj_g input field
                    pjGInput.value = finalResult;
                }

                // Add event listeners to all radio buttons for this specific person (PJ)
                calificativTeoretic.forEach(radio => {
                    radio.addEventListener('change', calculateCalificativGeneralPj);
                });

                calificativPractic.forEach(radio => {
                    radio.addEventListener('change', calculateCalificativGeneralPj);
                });


                const pjSubjects = ['mat1', 'mat2', 'mat3'];
                pjSubjects.forEach(subject => {
                    const notaInputs = document.querySelectorAll(
                        `[id^="${oameniData}pj_${subject}_nota"]`);
                    notaInputs.forEach(input => {
                        input.addEventListener("blur", function() {
                            let min = parseInt(this.getAttribute("min"));
                            let max = parseInt(this.getAttribute("max"));
                            let value = parseInt(this.value);

                            if (isNaN(value) || value < min) {
                                this.value = min;
                            } else if (value > max) {
                                this.value = max;
                            }
                            calculateAveragePj(
                                subject, oameniData);
                            calculateMediaGeneralaPj(
                                oameniData
                            ); // Call Media Generala calculation for PJ
                        });
                        input.addEventListener('input', () => {
                            let min = parseInt(this.getAttribute("min"));
                            let max = parseInt(this.getAttribute("max"));
                            let value = parseInt(this.value);

                            if (isNaN(value) || value < min) {
                                this.value = min;
                            } else if (value > max) {
                                this.value = max;
                            }
                            calculateAveragePj(
                                subject, oameniData);
                            calculateMediaGeneralaPj(
                                oameniData
                            ); // Call Media Generala calculation for PJ
                        });
                    });
                });


            });
        }

        // Attach event listener to the submit button
        const submitButton = document.getElementById('submitFormButton');

        submitButton.addEventListener('click', async function() {
            const nrIntrare = document.getElementById('pv_nr_intrare')
                .value; //pas1 aici pun datele luate de pe butoane in variabile
            const dataIntrare = document.getElementById('pv_data_intrare').value;
            const nrIesire = document.getElementById('pv_nr_iesire').value;
            const dataIesire = document.getElementById('pv_data_iesire').value;

            formData.pv_nr_intrare = nrIntrare;
            formData.pv_nr_iesire = nrIesire;
            formData.pv_data_intrare = dataIntrare;
            formData.pv_data_iesire = dataIesire;
            try {


                if (solicitarePf) {

                    //logica pt a lua toate notele din inputurile pf

                    const noteObj = {};
                    const dataIdValue = document.getElementById('pf-oameni')?.getAttribute(
                        'data-id'
                    ); //aici iau valoarea lui data-id din elemtul cu ID-ul "pf-oameni", il folosesc ca si cheie pentru noteObj
                    noteObj[dataIdValue] = {};
                    for (let i = 1; i <= 5; i++) {

                        noteObj[dataIdValue][`mat1_nota${i}`] = parseInt(document
                            .getElementById(
                                `pf_mat1_nota${i}`)?.value
                        ); // aici introduc notele in noteObj, sub forma de key-value pair. Creand un obiect cu 15 elemente.
                        noteObj[dataIdValue][`mat2_nota${i}`] = parseInt(document
                            .getElementById(
                                `pf_mat2_nota${i}`)
                            ?.value);
                        noteObj[dataIdValue][`mat3_nota${i}`] = parseInt(document
                            .getElementById(
                                `pf_mat3_nota${i}`)
                            ?.value);


                    }
                    noteObj[dataIdValue][`calificativ_t`] = document.querySelector(
                        'input[name="calificativ_teoretic"]:checked')?.value;
                    noteObj[dataIdValue][`calificativ_p`] = document.querySelector(
                        'input[name="calificativ_practic"]:checked')?.value;
                    noteObj[dataIdValue][`calificativ`] = document.getElementById('pf_g')
                        ?.value;

                    noteObj[dataIdValue][`mat1_medie`] = parseInt(document.getElementById(
                            'pf_mat1_medie')
                        ?.value);
                    noteObj[dataIdValue][`mat2_medie`] = parseInt(document.getElementById(
                            'pf_mat2_medie')
                        ?.value);
                    noteObj[dataIdValue][`mat3_medie`] = parseInt(document.getElementById(
                            'pf_mat3_medie')
                        ?.value);
                    noteObj[dataIdValue][`medie_t`] = parseFloat(document.getElementById(
                            'pf_medie_t')
                        ?.value).toFixed(2);

                    console.log('noteObj', noteObj);
                    formData.note.push(noteObj);

                } else {


                    solicitariPj?.forEach((element) => {
                        const noteObjPj = {};
                        // Create a new note object for PJ
                        const oameniData = element.getAttribute('data-id');
                        noteObjPj[oameniData] = {}; // Initialize for this `oameniData`

                        for (let i = 1; i <= 5; i++) {
                            // Dynamically fetch PJ inputs based on oameniData
                            noteObjPj[oameniData][`mat1_nota${i}`] = parseInt(
                                document
                                .getElementById(
                                    `${oameniData}pj_mat1_nota${i}`)?.value);
                            noteObjPj[oameniData][`mat2_nota${i}`] = parseInt(
                                document
                                .getElementById(
                                    `${oameniData}pj_mat2_nota${i}`)?.value);
                            noteObjPj[oameniData][`mat3_nota${i}`] = parseInt(
                                document
                                .getElementById(
                                    `${oameniData}pj_mat3_nota${i}`)?.value);


                        }


                        noteObjPj[oameniData][`calificativ_t`] = document.querySelector(
                            `input[name="${oameniData}pj_calificativ_teoretic"]:checked`
                        )?.value;
                        noteObjPj[oameniData][`calificativ_p`] = document.querySelector(
                            `input[name="${oameniData}pj_calificativ_practic"]:checked`
                        )?.value;
                        noteObjPj[oameniData][`calificativ`] = document.getElementById(
                            `${oameniData}pj_g`)?.value;


                        noteObjPj[oameniData][`mat1_medie`] = parseInt(document
                            .getElementById(
                                `${oameniData}pj_mat1_medie`)
                            ?.value);
                        noteObjPj[oameniData][`mat2_medie`] = parseInt(document
                            .getElementById(
                                `${oameniData}pj_mat2_medie`)
                            ?.value);
                        noteObjPj[oameniData][`mat3_medie`] = parseInt(document
                            .getElementById(
                                `${oameniData}pj_mat3_medie`)
                            ?.value);
                        noteObjPj[oameniData][`medie_t`] = parseFloat(document
                            .getElementById(
                                `${oameniData}pj_medie_t`)
                            ?.value).toFixed(2);


                        formData.note.push(
                            noteObjPj); // Push the PJ note object into formData

                    });
                }

                const mapErrorsToFields = {
                    'mat1_nota1': 'Materia 1 Nota 1',
                    'mat1_nota2': 'Materia 1 Nota 2',
                    'mat1_nota3': 'Materia 1 Nota 3',
                    'mat1_nota4': 'Materia 1 Nota 4',
                    'mat1_nota5': 'Materia 1 Nota 5',
                    'mat2_nota1': 'Materia 2 Nota 1',
                    'mat2_nota2': 'Materia 2 Nota 2',
                    'mat2_nota3': 'Materia 2 Nota 3',
                    'mat2_nota4': 'Materia 2 Nota 4',
                    'mat2_nota5': 'Materia 2 Nota 5',
                    'mat3_nota1': 'Materia 3 Nota 1',
                    'mat3_nota2': 'Materia 3 Nota 2',
                    'mat3_nota3': 'Materia 3 Nota 3',
                    'mat3_nota4': 'Materia 3 Nota 4',
                    'mat3_nota5': 'Materia 3 Nota 5',
                    'mat1_medie': 'Media Materia 1',
                    'mat2_medie': 'Media Materia 2',
                    'mat3_medie': 'Media Materia 3',
                    'medie_t': 'Media Generală',
                    'calificativ_t': 'Calificativul Teoretic',
                    'calificativ_p': 'Calificativul Practic',
                    'calificativ': 'Calificativul General',
                    'pv_nr_intrare': 'Nr Intrare CENAFER',
                    'pv_data_intrare': 'Data Intrare CENAFER',
                    'pv_nr_iesire': 'Nr Iesire ISF',
                    'pv_data_iesire': 'Data Iesire ISF',
                };
                //aici ma duc in RezultateExamenController apeland functia store() de acolo
                console.log('form data', formData)
                const response = await fetch(
                    `/rezultateExamen/update`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector(
                                'meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(formData), // Send formData
                    }
                );
                if (response.status === 200) {
                    const responseData = await response.json();

                    console.log('response data', responseData);
                    alert(responseData.message);

                    window.location.href = '/solicitari';
                } else if (response.status === 422) {
                    const errorData = await response.json();
                    let fieldErrors = '';

                    errorData.errors.forEach(errorField => {

                        fieldErrors.length > 0 ? fieldErrors +=
                            `, ${mapErrorsToFields[errorField]}` : fieldErrors +=
                            `${mapErrorsToFields[errorField]}`
                    })

                    alert(`${errorData.message}: ${fieldErrors}`);
                } else {
                    const errorData = await response.json();
                    alert('Eroare: ' + errorData.message);
                }

                formData = {
                    note: []
                };

            } catch (error) {
                console.error('An error occurred:', error);
            }
        });
    })
</script>
