@php

    function renderOptions($functii)
    {
        $html = '';
        foreach ($functii as $functie) {
            $html .= '<option value="' . $functie->id . '">' . $functie->domeniu . '</option>';
            if (isset($functie->children)) {
                $html .= renderOptions($functie->children);
            }
        }
        return $html;
    }
    $selectedDataId = '';
@endphp

<x-app-layout>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autorizare Personal SC</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <div class="max-w-7xl mx-auto bg-white shadow-md rounded p-5">
        <h1 class="text-2xl font-bold mb-5">Autorizare Personal SC</h1>


        <div class="flex justify-between mb-5">
            <div class="border-2 border-gray-300 p-2 rounded-md w-1/2">
                <label class="block text-sm font-medium text-gray-700">Tip solicitare</label>
                <select id='tip-solicitare'
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    @foreach ($tipSolicitare as $tip)
                        <option data-denumire="{{ $tip->denumire }}" value="{{ $tip->id }}">{{ $tip->denumire }}
                        </option>
                    @endforeach
                </select>
                <div class="mt-2 type2Hidden type3 ">
                    <label class="inline-flex items-center">
                        <input id="regim-urgenta"type="checkbox" class="form-checkbox">
                        <span class="ml-2">În regim de urgență</span>
                    </label>
                </div>
                <div class="mt-2">
                    <label class="block text-sm font-medium text-gray-700">Solicitant</label>
                    <div class="mt-1">
                        <label class="inline-flex items-center">
                            <input id='solicitant-pers-fizica' type="radio" name="solicitant-fj" class="form-radio">
                            <span class="ml-2">Persoană fizică</span>
                        </label>
                    </div>
                    <div class="mt-1">
                        <label class="inline-flex items-center">
                            <input id='solicitant-pers-juridica' type="radio" name="solicitant-fj" class="form-radio">
                            <span class="ml-2">Persoană juridică</span>
                        </label>
                    </div>
                </div>
                <div class="mt-2">
                    <div class='type3'>
                        <label class="block text-sm font-medium text-gray-700">Alegere</label>
                        <div class="mt-1">
                            {{-- la apasare se deschid optiunile din tabelul functii --}}
                            <x-modal-tip-autorizatie :functions='$functions' :selectedDataId='$selectedDataId' />
                        </div>
                    </div>
                    <div id ='alegere-pers-juridica'class="mt-1 hidden">
                        {{-- la apasare se deschid optiunile din tabelul functii --}}
                        <x-modal-unitati-feroviare :unitati='$unitatiFeroviare' :columns='$unitatiColumns' />

                    </div>

                    {{-- <div class="mt-1">
                        <label class="inline-flex items-center">
                            <input type="radio" name="alegere" class="form-radio">
                            <span class="ml-2">I.S.F.</span>
                        </label>
                    </div>
                    <p>sau</p>
                    <div class="mt-1">
                        <label class="inline-flex items-center">
                            <input type="radio" name="alegere" class="form-radio">
                            <span class="ml-2">S.C.M.A.A.M.P</span>
                        </label>
                    </div> --}}
                    {{-- la alegere (din unitate feroviara) completeaza campurile solicitant persoana juridica si cod fiscal --}}

                </div>
                <div class="mt-2">
                    <label class="block text-sm font-medium text-gray-700">Cerere solicitant</label>
                    <input id='numar-cerere-solicitant' type="text"
                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Număr (maxim 25 de caractere)" />
                    <input id='data-cerere-solicitant' type="date"
                        class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md" />

                    <select class="hidden mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        name="dropdown-art-suspendare" id="dropdown-art-suspendare">
                        <option value="">Selectați motiv suspendare</option>
                        @foreach ($optiuniArtSuspendare as $key => $value)
                            <option value="{{ $value->literaArticol }}">{{ $value->literaArticol }}</option>
                        @endforeach
                    </select>
                    <input type="number" id="input-zile-suspendare" min="90" max="5000"
                        class="hidden mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Număr zile suspendare">

                </div>

                <div class="mt-2">
                    <div class="type3">
                        <label class="block text-sm font-medium text-gray-700 mt-5">Aviz CENAFER</label>
                        <label class="block text-sm font-medium text-gray-700 mt-5">CENAFER</label>

                        <select id='locatie-cenafer'
                            class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                            @foreach ($cenaferuri as $cenafer)
                                <option value="{{ $cenafer->contor }}">{{ $cenafer->CENAFER }}</option>
                            @endforeach
                        </select>
                        <input id='numar-cenafer' type="text"
                            class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            placeholder="Număr">
                        <input id='data-cenafer' type="date"
                            class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                    <div class='type2 hidden type3ReautorizareHidden' id='container-inputs-fizica-3'>
                        <label class="block text-sm font-medium text-gray-700 mt-5">Aprobare inițială SCMAP</label>
                        <input id='numar-aprobare-initiala' type="text"
                            class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            placeholder="Număr">
                        <input id='data-aprobare-initiala' type="date"
                            class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                </div>

            </div>

            <div>
                <label class="type3 block text-sm font-medium text-gray-700">Tip de autorizatie solicitat</label>
                <div class="type3 mt-1 p-2 border border-gray-300 bg-gray-50 rounded-md flex flex-row">
                    <div class="flex flex-col gap-2 mr-2">
                        <p>Funcție:</p>
                        <p>Autorizare:</p>
                        <p>Activitate:</p>
                    </div>
                    <div class="flex flex-col gap-2" id='tip-autorizatie-solicitat'>

                    </div>
                </div>
                {{-- Select un isf iar apoi tragem toate datele in campuri, campurile nu sunt editabile --}}
                <label class="block text-sm font-medium text-gray-700 mt-5">Inspectoratul de Siguranță
                    Feroviară</label>
                <label for="isfDropdown">Selectează ISF</label>
                <select id="isfDropdown"
                    class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    <option value="">Selectează...</option>
                    @foreach ($isfuri as $isf)
                        <option value="{{ $isf->contor }}" data-isf="{{ $isf }}">
                            {{ $isf->nr_ISF }} - {{ $isf->ISF }} - {{ $isf->sef_ISF }} -
                            {{ $isf->cod_serviciu }}
                        </option>
                    @endforeach
                </select>
                <div class="mt-1 p-2 border border-gray-300 bg-gray-50 rounded-md">
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label class="block text-sm">Cod serviciu</label>
                            <input id='cod-serviciu-isf' type="text"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm">Unitate</label>
                            <input id='unitate-isf' type="text"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                    </div>
                    <div class="mt-2 grid grid-cols-2 gap-2">
                        <div>
                            <label class="block text-sm">Telefon PTT</label>
                            <input id='telefon-ptt-isf' type="text"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm">Fax PTT</label>
                            <input id='fax-ptt-isf' type="text"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                    </div>
                    <div class="mt-2 grid grid-cols-2 gap-2">
                        <div>
                            <label class="block text-sm">Telefon CFR</label>
                            <input id='telefon-cfr-isf' type="text"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm">E-mail</label>
                            <input id='email-cfr-isf' type="text"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                    </div>
                    <div class="mt-2">
                        <label class="block text-sm">Inspector Șef Teritorial</label>
                        <input id='inspector-sef-isf' type="text"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                </div>
                <label class="block text-sm font-medium text-gray-700 mt-5">Solicitare de autorizare</label>
                <div class="mt-1 p-2 border border-gray-300 bg-gray-50 rounded-md">
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label class="block text-sm">Număr ieșire ISF</label>
                            <input id='numar-iesire-solicitare' type="text"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm">Dată ieșire ISF</label>
                            <input id='data-iesire-solicitare' type="date"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                    </div>
                    <div class="mt-2 grid grid-cols-2 gap-2 border-2">
                        <div>
                            <label class="block text-sm">Redactată de</label>
                            <input id='redactata-de-solicitare' type="text"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm">Dată/oră redactare</label>
                            <input id='data-ora-redactare-solicitare' type="datetime-local"
                                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="type3 flex mb-5 space-x-5 border-2 border-gray-300 p-2 rounded-md">
            <div class="mb-5 space-x-5 border-2 border-gray-300 p-2 rounded-md">
                <label class="block text-sm font-medium text-gray-700">Președinte comisie (reprezentant ASFR)</label>
                <input id='nume-presedinte-comisie' type="text"
                    class="mt-1 block w-90% bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                    placeholder="Nume">
                <input id='prenume-presedinte-comisie' type="text"
                    class="mt-2 block w-90% bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                    placeholder="Prenume">
            </div>

            <div class="flex justify-between mb-5 space-x-5 border-2 border-gray-300 p-2 rounded-md">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Membru (reprezentant CENAFER)</label>
                    <div class="grid grid-cols-2 gap-2">
                        <input id='nume-membru-comisie' type="text"
                            class="col-span-2 mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            placeholder="Nume">
                        <input id='prenume-membru-comisie' type="text"
                            class="col-span-2 mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            placeholder="Prenume">
                    </div>
                </div>

                {{-- <div>
                    <label class="block text-sm font-medium text-gray-700">Atestat</label>
                    <input id='serie-atestat-membru' type="text"
                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Serie">
                    <input id='numar-atestat-membru' type="text"
                        class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Număr">
                </div> --}}
            </div>



            <div class="type2 hidden flex justify-between mb-5 space-x-5 border-2 border-gray-300 p-2 rounded-md"
                id='container-inputs-fizica-2'>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Reprezentant CENAFER</label>
                    <input id='nume-reprezentant-cenafer' type="text"
                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Nume">
                    <input id='prenume-reprezentant-cenafer' type="text"
                        class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Prenume">
                </div>

                {{-- <div>
                    <label class="block text-sm font-medium text-gray-700">Atestat</label>
                    <input id='serie-atestat-cenafer' type="text"
                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Serie">
                    <input id='numar-atestat-cenafer' type="text"
                        class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Număr">
                </div> --}}
            </div>

            <div class="hidden flex justify-between mb-5 space-x-5 border-2 border-gray-300 p-2 rounded-md"
                id='container-inputs-juridica-2'>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Reprezentant unitate feroviară</label>
                    <input id='nume-reprezentant-unitate-feroviara' type="text"
                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Nume">
                    <input id='prenume-reprezentant-unitate-feroviara' type="text"
                        class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Prenume">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Atestat</label>
                    <input id='serie-atestat-reprezentant' type="text"
                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Serie">
                    <input id='numar-atestat-reprezentant' type="text"
                        class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Număr">
                </div>
            </div>
        </div>

        <div class="mb-5 hidden" id='container-inputs-juridica'>
            <label class="block text-sm font-medium text-gray-700">Solicitant - persoană juridică</label>
            <input disabled id='solicitant-pers-juridica-denumire' type="text" placeholder="Denumire"
                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
            <input disabled id='solicitant-pers-juridica-cif' type="text" placeholder="CIF"
                class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
            {{-- <label class="block text-sm font-medium text-gray-700 mt-5">Solicitant</label> --}}
            <div id="dynamic-rows" class="flex flex-col gap-4">
                <!-- Dynamic rows will be added here -->
                <div class="solicitant-row flex-col space-x-4" data-row-nr="0">
                    <div class="mb-5 type3SchimbareNume hidden" id='container-inputs-schimbare-pj-0'>
                        <label class="block  text-sm font-medium text-gray-700">Solicitant - persoană juridică -
                            Modifică mai jos NUMELE
                            PRECEDENT</label>
                        <input id='solicitant-pj-nume-schimbare-0' type="text" placeholder="Nume precedent"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        <input id='solicitant-pj-prenume-schimbare-0' type="text" placeholder="Prenume precedent"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                    <div>
                        <label class="block  text-sm font-medium text-gray-700">Solicitant -> 1</label>
                        <input id="numePJ-0" type="text" placeholder="Nume"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        <input id="prenumePJ-0" type="text" placeholder="Prenume"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        <input type="text" placeholder="CNP" id="cnpPJ-0"
                            class="CNP-PJ mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                        <button id="validareCnpPJ-0"
                            class="validareCnpPJ bg-green-400 hover:bg-green-500 text-white font-bold py-2 px-4 ml-4 rounded"
                            data-row-nr="0" data-cnp-id="cnpPJ-0">Validează</button>
                        <button type="button" class="remove-row bg-red-500 text-white px-2 rounded">-</button>
                    </div>
                    <select
                        class="hidden dropdown-art-suspendare-pj mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        name="dropdown-art-suspendare-pj-0" id="dropdown-art-suspendare-pj-0">
                        <option value="">Selectați motiv suspendare</option>
                        @foreach ($optiuniArtSuspendare as $key => $value)
                            <option value="{{ $value->literaArticol }}">{{ $value->literaArticol }}</option>
                        @endforeach
                    </select>
                    <input type="number" id="input-zile-suspendare-pj-0" min="90" max="5000"
                        class="hidden mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                        placeholder="Număr zile suspendare">
                    <div class='hidden' id='incetare-suspendare-inputs-pj-0'>
                        <label>
                            <input type="radio" data-row-nr="0" name="incetare_suspendare_document_type_pj-0"
                                id="incetare_suspendare_aviz_radio_pj-0" /> Nr. Aviz
                            Medical/Psihologic
                        </label>
                        <label>
                            <input type="radio" data-row-nr="0" name="incetare_suspendare_document_type_pj-0"
                                id="incetare_suspendare_competente_radio_pj-0" /> Nr.
                            Certificat Competențe
                        </label>

                        <div id="aviz_psihologic_inputs_pj-0" class="hidden">
                            <label>Nr. Aviz Medical/Psihologic: <input
                                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="text" id="incetare_suspendare_nr_aviz_pj-0"></label><br>
                            <label>Eliberat de: <input
                                    class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="text" id="incetare_suspendare_eliberat_aviz_pj-0"></label><br>
                            <label>La data de: <input
                                    class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="date" id="incetare_suspendare_data_aviz_pj-0"></label>
                        </div>

                        <div id="certificat_competente_inputs_pj-0" class="hidden">
                            <label>Nr. Certificat Competențe: <input
                                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="text" id="incetare_suspendare_nr_certificat_pj-0"></label><br>
                            <label>Eliberat de: <input
                                    class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="text" id="incetare_suspendare_eliberat_certificat_pj-0"></label><br>
                            <label>La data de: <input
                                    class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="date" id="incetare_suspendare_data_certificat_pj-0"></label>
                        </div>
                    </div>
                </div>

            </div>
            <button type="button" id="add-row" class="mt-2 bg-blue-500 text-white px-4 py-2 rounded">+</button>
        </div>



        <div class="mb-5 hidden" id='container-inputs-fizica'>
            {{-- Solicitant persoana fizica sau persoana juridica
            pers fizica are serie CI, Numar CI, Localitate, Adresa
            per juridica are Unitate si Cod Fiscal
            --}}
            <div class="mb-5 type3SchimbareNume hidden" id='container-inputs-schimbare'>
                <label class="block  text-sm font-medium text-gray-700">Solicitant - persoană fizică - Modifică mai jos
                    NUMELE
                    PRECEDENT</label>
                <input id='solicitant-pers-fizica-nume-schimbare' type="text" placeholder="Nume precedent"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <input id='solicitant-pers-fizica-prenume-schimbare' type="text" placeholder="Prenume precedent"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
            </div>
            <label class="block text-sm font-medium text-gray-700">Solicitant - persoană fizică</label>
            <input id='solicitant-pers-fizica-nume' type="text" placeholder="Nume"
                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
            <input id='solicitant-pers-fizica-prenume' type="text" placeholder="Prenume"
                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
            <input id='solicitant-pers-fizica-serie-CI' type="text" placeholder="Serie CI"
                class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
            <input id='solicitant-pers-fizica-numar-CI' type="text" placeholder="Numar CI"
                class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
            <div class='flex flex-row items-center mt-2 '>
                <input id='solicitant-pers-fizica-cnp' type="text" placeholder="CNP"
                    class="block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <button id='validareCnp'
                    class="bg-green-400 hover:bg-green-500 text-white font-bold py-2 px-4 ml-4 rounded">Validează</button>
            </div>
            <input id='solicitant-pers-fizica-localitate' type="text" placeholder="Localitate"
                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
            <input id='solicitant-pers-fizica-adresa' type="text" placeholder="Adresa"
                class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
            <div class='hidden' id='incetare-suspendare-inputs'>
                <label>
                    <input type="radio" name="incetare_suspendare_document_type_pf"
                        id="incetare_suspendare_aviz_radio" /> Nr. Aviz
                    Medical/Psihologic
                </label>
                <label>
                    <input type="radio" name="incetare_suspendare_document_type_pf"
                        id="incetare_suspendare_competente_radio" /> Nr.
                    Certificat Competențe
                </label>

                <div id="aviz_psihologic_inputs" class="hidden">
                    <label>Nr. Aviz Medical/Psihologic: <input
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            type="text" id="incetare_suspendare_nr_aviz"></label><br>
                    <label>Eliberat de: <input
                            class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            type="text" id="incetare_suspendare_eliberat_aviz"></label><br>
                    <label>La data de: <input
                            class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            type="date" id="incetare_suspendare_data_aviz"></label>
                </div>

                <div id="certificat_competente_inputs" class="hidden">
                    <label>Nr. Certificat Competențe: <input
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            type="text" id="incetare_suspendare_nr_certificat"></label><br>
                    <label>Eliberat de: <input
                            class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            type="text" id="incetare_suspendare_eliberat_certificat"></label><br>
                    <label>La data de: <input
                            class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            type="date" id="incetare_suspendare_data_certificat"></label>
                </div>
            </div>
        </div>


        <x-modal-autorizatii-anterioare />


        <div class="type3 flex justify-between mb-5 space-x-5 border-2 border-gray-300 p-2 rounded-md">
            <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700">Examinare teoretică</label>
                <input id='locatie-examinare-teoretica' type="text"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                    placeholder="Examinare teoretică">
                <div class="mt-2 flex space-x-2">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700">De</label>
                        <input id="teoretica-de" type="text"
                            class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            placeholder="Ziua">
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700">În data de, la ora </label>
                        <input id="teoretica-in-data-de" type="datetime-local"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                </div>
                <div class="mt-2 flex space-x-2">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700">Până</label>
                        <input id="teoretica-pana" type="text"
                            class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            placeholder="Ziua">
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700">În data de, la ora </label>
                        <input id="teoretica-in-data-de-pana" type="datetime-local"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                </div>
            </div>
            <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700">Examinare practică</label>
                <input id='locatie-examinare-practica' type="text"
                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                    placeholder="Examinare practică">
                <div class="mt-2 flex space-x-2">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700">De</label>
                        <input id="practica-de" type="text"
                            class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            placeholder="Ziua">
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700">În data de, la ora </label>
                        <input id="practica-in-data-de" type="datetime-local"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                </div>
                <div class="mt-2 flex space-x-2">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700">Până</label>
                        <input id="practica-pana" type="text"
                            class="mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                            placeholder="Ziua">
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700">În data de, la ora </label>
                        <input id="practica-in-data-de-pana" type="datetime-local"
                            class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    </div>
                </div>
            </div>
        </div>

        <div class="flex justify-between">
            <button id='submitBtn'
                class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">Continuare</button>
            <button class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded"
                onclick="window.location.href='/dashboard'">Abandonare</button>

        </div>
    </div>
</x-app-layout>

<script>
    // Move the function outside DOMContentLoaded to make it globally accessible
    function checkDuplicateCNP(input) {
        const cnp = input.value;
        console.log('cnp in checkDuplicateCNP', cnp);
        if (!cnp) return;

        const allCnpInputs = document.querySelectorAll('.CNP-PJ');
        let isDuplicate = false;
        console.log('allCnpInputs', allCnpInputs);
        allCnpInputs.forEach(otherInput => {
            console.log('otherInput', otherInput);
            if (otherInput.id !== input.id && otherInput.value === cnp) {
                isDuplicate = true;
            }
        });

        if (isDuplicate) {
            alert(
                'Acest CNP a fost deja introdus pentru alt solicitant! Apăsați pe minus pentru a elimina rândul sau introduceți alt CNP.'
            );
            input.value = ''; // Clear the duplicate CNP
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // logica validare CNP si fetch solicitari anterioare
        let autorizatiiColumns = [];
        let autorizatiiData = [];
        const selectedRowIds = new Set();
        const modalContent = document.querySelector('#modal-autorizatii .modal-body-autorizatii');
        const confirmButton = document.getElementById('autorizatii-confirm-button');
        const confirmedTable = document.getElementById('autorizatii-table');
        const confirmedTableBody = confirmedTable.querySelector('tbody');
        const confirmedTableHead = confirmedTable.querySelector('thead');
        let idsAutorizatiiAnterioareSelected = [];
        let rowsIndex = 1;
        let solicitantNume = '';
        let solicitantPrenume = '';


        const selectTipSolicitare = document.getElementById('tip-solicitare');

        function updateConfirmButtonStatus() {
            confirmButton.disabled = selectedRowIds.size === 0;
        }

        function updateModalContent(data) {
            const modalBody = document.querySelector('#modal-autorizatii .modal-body-autorizatii');
            console.log('aici este data afisat', data);
            if (!data) {
                modalBody.innerHTML =
                    `<table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400"></table>`;
            } else {


                if (autorizatiiColumns.length === 0) {
                    autorizatiiColumns = data.autorizatiiColumns;
                }

                autorizatiiData = autorizatiiData.concat(data.autorizatii);
                solicitantNume = data?.nume;
                solicitantPrenume = data?.prenume;

                // Generate table header
                let headerHtml =
                    '<thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400"><tr>';
                autorizatiiColumns.forEach(column => {
                    headerHtml += `<th scope="col" class="px-6 py-3">${column}</th>`;
                });
                headerHtml += '</tr></thead>';

                // Generate table rows
                let bodyHtml = '<tbody>';
                autorizatiiData.forEach((row, index) => {
                    console.log('row', row);
                    let isSelected = selectedRowIds.has(`${row.oameni}`) ? 'selected-row' : '';
                    bodyHtml +=
                        `<tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 ${isSelected}" data-id="${row.oameni}">`;
                    autorizatiiColumns.forEach(column => {
                        bodyHtml += `<td class="px-6 py-4">${row[column]}</td>`;
                    });
                    bodyHtml += '</tr>';
                });
                bodyHtml += '</tbody>';

                // Insert into the modal content
                modalBody.innerHTML =
                    `<table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">${headerHtml}${bodyHtml}</table>`;
            }

        }

        async function validateCnpAndFetchAutorizatii(cnp) {
            // console.log(`Request trimis către: /validateCnpAndGetAutorizatii/${cnp}/${tipSolicitare}`);
            const tipSolicitareSelectata = selectTipSolicitare?.options[selectTipSolicitare
                    .selectedIndex]
                .dataset.denumire;
            console.log('tip solicitare selectata', tipSolicitareSelectata);
            await fetch(`/validateCnpAndGetAutorizatii/${cnp}/${tipSolicitareSelectata}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                            'content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.isValid) {
                        alert('CNP valid!');
                        updateModalContent(data);
                        // const modal = document.getElementById('modal-autorizatii');
                        // modal.classList.remove('hidden'); // Show the modal
                        // return data.autorizatii;
                    } else {
                        alert('CNP-ul este invalid! Verifică și încearcă din nou.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while validating the CNP');
                });
        }
        const disableEnableFieldWithId = (enable, inputId) => {
            if (enable) {
                document.getElementById(inputId).disabled = false;
            } else {
                document.getElementById(inputId).disabled = true;

            }
        }
        // Event listener for individual CNP validation
        document.getElementById('validareCnp').addEventListener('click', async function() {
            const cnp = document.getElementById('solicitant-pers-fizica-cnp').value;

            await validateCnpAndFetchAutorizatii(cnp);



            const numeField = document.getElementById('solicitant-pers-fizica-nume');
            numeField.value = solicitantNume || '';
            const prenumeField = document.getElementById('solicitant-pers-fizica-prenume');
            prenumeField.value = solicitantPrenume || '';
            //abc
            const numeSchimbareField = document.getElementById(
                'solicitant-pers-fizica-nume-schimbare');
            numeSchimbareField.value = solicitantNume || '';
            const prenumeSchimbareField = document.getElementById(
                'solicitant-pers-fizica-prenume-schimbare');
            prenumeSchimbareField.value = solicitantPrenume || '';
            const valueTipSolicitare = selectTipSolicitare.options[selectTipSolicitare
                    .selectedIndex]
                .dataset
                .denumire;

            if (solicitantNume && solicitantPrenume && valueTipSolicitare.toLowerCase() !==
                'schimbarenume') {
                disableEnableFieldWithId(false, 'solicitant-pers-fizica-nume');
                disableEnableFieldWithId(false, 'solicitant-pers-fizica-prenume');
                disableEnableFieldWithId(true, 'solicitant-pers-fizica-nume-schimbare');
                disableEnableFieldWithId(true, 'solicitant-pers-fizica-prenume-schimbare');
            } else {
                disableEnableFieldWithId(true, 'solicitant-pers-fizica-nume');
                disableEnableFieldWithId(true, 'solicitant-pers-fizica-prenume');
                disableEnableFieldWithId(false, 'solicitant-pers-fizica-nume-schimbare');
                disableEnableFieldWithId(false, 'solicitant-pers-fizica-prenume-schimbare');
            }

        });

        // Function to add event listener for corporate CNP validation
        function addCnpPjValidationListener(button) {
            button.addEventListener('click', async function() {
                const cnpId = this.dataset.cnpId;
                const rowNr = this.dataset.rowNr;
                console.log('cnpId', cnpId);
                console.log('rowNr', rowNr);

                const cnp = document.getElementById(cnpId).value;

                // Check for duplicate CNP
                const allCnpInputs = document.querySelectorAll('.CNP-PJ');
                let isDuplicate = false;
                let duplicateRowNr = null;

                allCnpInputs.forEach(input => {
                    if (input.id !== cnpId && input.value === cnp) {
                        isDuplicate = true;
                        // Extract row number from the input ID
                        const match = input.id.match(/cnpPJ-(\d+)/);
                        if (match) {
                            duplicateRowNr = match[1];
                        }
                    }
                });

                if (isDuplicate) {
                    alert(
                        `Acest CNP a fost deja introdus pentru solicitantul ${parseInt(duplicateRowNr) + 1}! Apăsați pe minus pentru a elimina rândul sau introduceți alt CNP.`
                    );
                    return;
                }

                await validateCnpAndFetchAutorizatii(cnp);
                console.log('autorizatii data', autorizatiiData);
                console.log('cnp', cnp);
                const autorizatiiUtilizator = autorizatiiData.filter(autorizatie => autorizatie
                    .cnp === cnp.toString());
                console.log('autorizatii user', autorizatiiUtilizator);



                const numeField = document.getElementById(`numePJ-${rowNr}`);

                if (solicitantNume) numeField.value = solicitantNume || '';

                const prenumeField = document.getElementById(`prenumePJ-${rowNr}`);
                if (solicitantPrenume) prenumeField.value = solicitantPrenume || '';

                const containerSchimbarePj = document.getElementById(
                    `container-inputs-schimbare-pj-${rowNr}`);

                const numeSchimbareField = document.getElementById(
                    `solicitant-pj-nume-schimbare-${rowNr}`);
                if (solicitantNume) numeSchimbareField.value = solicitantNume || '';

                const prenumeSchimbareField = document.getElementById(
                    `solicitant-pj-prenume-schimbare-${rowNr}`);
                if (solicitantPrenume) prenumeSchimbareField.value = solicitantPrenume || '';


                const valueTipSolicitare = selectTipSolicitare.options[selectTipSolicitare
                        .selectedIndex]
                    .dataset
                    .denumire;

                if (solicitantNume && solicitantPrenume && valueTipSolicitare.toLowerCase() !==
                    'schimbarenume') {
                    disableEnableFieldWithId(false, `numePJ-${rowNr}`);
                    disableEnableFieldWithId(false, `prenumePJ-${rowNr}`);
                    // disableEnableFieldWithId(true, `container-inputs-schimbare-pj-${rowNr}`);
                    // disableEnableFieldWithId(true, `solicitant-pj-nume-schimbare-${rowNr}`);
                    // disableEnableFieldWithId(true, `solicitant-pj-prenume-schimbare-${rowNr}`);
                } else {
                    disableEnableFieldWithId(true, `numePJ-${rowNr}`);
                    disableEnableFieldWithId(true, `prenumePJ-${rowNr}`);
                    disableEnableFieldWithId(false, `container-inputs-schimbare-pj-${rowNr}`);
                    disableEnableFieldWithId(false, `solicitant-pj-nume-schimbare-${rowNr}`);
                    disableEnableFieldWithId(false, `solicitant-pj-prenume-schimbare-${rowNr}`);
                }

                // const autorizatii = validateCnpAndFetchAutorizatii(cnp);
            });
        }

        // Attach event listeners to initial corporate CNP buttons
        document.querySelectorAll('.validareCnpPJ').forEach(button => {
            addCnpPjValidationListener(button);
        });

        // Modal close button functionality
        document.querySelectorAll('[data-modal-toggle="modal-autorizatii"]').forEach(button => {
            button.addEventListener('click', function() {
                const modal = document.getElementById('modal-autorizatii');
                modal.classList.toggle('hidden'); // Toggle visibility
            });
        });

        // Handle row selection inside modal
        modalContent.addEventListener('click', function(event) {
            const selectedRow = event.target.closest('tr');
            if (selectedRow) {
                const rowId = selectedRow.getAttribute('data-id');
                if (selectedRowIds.has(rowId)) {
                    selectedRowIds.delete(rowId);
                    selectedRow.classList.remove('selected-row');
                } else {
                    selectedRowIds.add(rowId);
                    selectedRow.classList.add('selected-row');
                }
                updateConfirmButtonStatus();
            }
        });

        // Confirm selection and clone selected rows to confirmed table
        confirmButton.addEventListener('click', function() {
            if (selectedRowIds.size > 0) {
                confirmedTableBody.innerHTML = '';
                confirmedTableHead.innerHTML = '';
                idsAutorizatiiAnterioareSelected = [];

                // Generate table header for the confirmed table
                let headerHtml = '<tr>';
                autorizatiiColumns.forEach(column => {
                    headerHtml += `<th scope="col" class="px-6 py-3">${column}</th>`;
                });
                headerHtml += '</tr>';

                // Set the header for the confirmed table
                confirmedTableHead.innerHTML = headerHtml;

                // Clone the selected rows and append them to the confirmed table
                selectedRowIds.forEach(rowId => {
                    const currentSelectedRow = modalContent.querySelector(
                        `tr[data-id="${rowId}"]`);
                    if (currentSelectedRow) {
                        const clonedRow = currentSelectedRow.cloneNode(true);
                        clonedRow.classList.remove('selected-row');
                        confirmedTableBody.appendChild(clonedRow);
                        idsAutorizatiiAnterioareSelected.push(rowId);
                    } else {
                        console.error(`Row with data-id "${rowId}" not found`);
                    }
                });

                // Show the confirmed table
                confirmedTable.classList.remove('hidden');
                // Close the modal
                const butonClose = document.getElementById('autorizatii-modal-close');
                butonClose.click();
            } else {
                alert('Please select at least one row before confirming.');
            }
        });

        // Listeners pentru radio buttons de la incetare suspendare PJ
        function addIncetareSuspendarePJRadiosListener(event) {

            const rowNr = event.target.dataset.rowNr;
            console.log('row nr in incetare suspendare pj', rowNr);
            const avizInputs = document.getElementById(`aviz_psihologic_inputs_pj-${rowNr}`);
            const competenteInputs = document.getElementById(`certificat_competente_inputs_pj-${rowNr}`);
            const selectedRadio = document.querySelector(
                `input[name="incetare_suspendare_document_type_pj-${rowNr}"]:checked`);

            if (!selectedRadio) return; // Exit if no radio is selected

            if (selectedRadio.id === `incetare_suspendare_aviz_radio_pj-${rowNr}`) {
                avizInputs.classList.remove('hidden');
                competenteInputs.classList.add('hidden');
            } else if (selectedRadio.id === `incetare_suspendare_competente_radio_pj-${rowNr}`) {
                competenteInputs.classList.remove('hidden');
                avizInputs.classList.add('hidden');
            }


        }
        document.querySelectorAll('input[name^="incetare_suspendare_document_type_pj"]')
            ?.forEach(button => {
                console.log(
                    'button', button
                );
                button.addEventListener('change', addIncetareSuspendarePJRadiosListener);

            });

        // Listeners pentru radio buttons de la incetare suspendare PF
        function toggleIncetareSuspendarePFInputs() {
            console.log('toggled toggleIncetareSuspendarePFInputs');
            const avizInputs = document.getElementById('aviz_psihologic_inputs');
            const competenteInputs = document.getElementById('certificat_competente_inputs');
            const selectedRadio = document.querySelector(
                'input[name="incetare_suspendare_document_type_pf"]:checked');

            console.log('val', selectedRadio);
            if (!selectedRadio) return; // Exit if no radio is selected

            if (selectedRadio.id === 'incetare_suspendare_aviz_radio') {
                avizInputs.classList.remove('hidden');
                competenteInputs.classList.add('hidden');
            } else if (selectedRadio.id === 'incetare_suspendare_competente_radio') {
                competenteInputs.classList.remove('hidden');
                avizInputs.classList.add('hidden');
            }
        }
        document.querySelectorAll('input[name="incetare_suspendare_document_type_pf"]').forEach(input => {
            input.addEventListener('change', toggleIncetareSuspendarePFInputs);
        });

        // Logic to add multiple fields for corporate entities
        const addRowButton = document.getElementById('add-row');
        const dynamicRowsContainer = document.getElementById('dynamic-rows');

        function addRow() {
            const newRow = document.createElement('div');
            const tipSolicitareValue = selectTipSolicitare.options[selectTipSolicitare.selectedIndex].dataset
                .denumire;

            const existingDropdown = document.getElementById('dropdown-art-suspendare-pj-0');
            if (!existingDropdown) return;

            // Clone the options for new dropdown
            let optionsHtml = existingDropdown.innerHTML;
            newRow['data-row-nr'] = rowsIndex;
            newRow.classList.add('solicitant-row', 'flex-col', 'space-x-4');
            newRow.innerHTML = `
            <div>
                <div class="mb-5 type3SchimbareNume ${tipSolicitareValue.toLowerCase() === 'schimbarenume' ? '' : 'hidden'}" id='container-inputs-schimbare-pj-${rowsIndex}'>
                    <label class="block  text-sm font-medium text-gray-700">Solicitant - persoană juridică - Modifică mai jos NUMELE PRECEDENT</label>
                    <input id='solicitant-pj-nume-schimbare-${rowsIndex}' type="text" placeholder="Nume precedent"
                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    <input id='solicitant-pj-prenume-schimbare-${rowsIndex}' type="text" placeholder="Prenume precedent"
                        class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                </div>
                <div>
                    <label class="block  text-sm font-medium text-gray-700">Solicitant -> ${rowsIndex+1}</label>
                    <input type="text" placeholder="Nume" id="numePJ-${rowsIndex}" class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    <input type="text" placeholder="Prenume" id="prenumePJ-${rowsIndex}" class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    <input type="text" placeholder="CNP" id="cnpPJ-${rowsIndex}" class="CNP-PJ mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md" onchange="checkDuplicateCNP(this)">
                    <button id="validareCnpPJ-${rowsIndex}" class="validareCnpPJ bg-green-400 hover:bg-green-500 text-white font-bold py-2 px-4 ml-4 rounded" data-row-nr="${rowsIndex}" data-cnp-id="cnpPJ-${rowsIndex}">Validează</button>
                    <button type="button" class="remove-row bg-red-500 text-white px-2 rounded">-</button>
                </div>
                <select class="hidden dropdown-art-suspendare-pj mt-2 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                name="dropdown-art-suspendare-pj-${rowsIndex}" id="dropdown-art-suspendare-pj-${rowsIndex}">
                    ${optionsHtml}
                </select>
                <input type="number" id="input-zile-suspendare-pj-${rowsIndex}" min="90" max="5000"
                    class="hidden mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                    placeholder="Număr zile suspendare">
                <div class='hidden' id='incetare-suspendare-inputs-pj-${rowsIndex}'>
                        <label>
                            <input type="radio" data-row-nr="${rowsIndex}" name="incetare_suspendare_document_type_pj-${rowsIndex}"
                                id="incetare_suspendare_aviz_radio_pj-${rowsIndex}" /> Nr. Aviz
                                Medical/Psihologic
                        </label>
                        <label>
                            <input type="radio" data-row-nr="${rowsIndex}" name="incetare_suspendare_document_type_pj-${rowsIndex}"
                                id="incetare_suspendare_competente_radio_pj-${rowsIndex}" /> Nr.
                            Certificat Competențe
                        </label>

                        <div id="aviz_psihologic_inputs_pj-${rowsIndex}" class="hidden">
                            <label>Nr. Aviz Medical/Psihologic: <input
                                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="text" id="incetare_suspendare_nr_aviz_pj-${rowsIndex}"></label><br>
                            <label>Eliberat de: <input
                                    class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="text" id="incetare_suspendare_eliberat_aviz_pj-${rowsIndex}"></label><br>
                            <label>La data de: <input
                                    class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="date" id="incetare_suspendare_data_aviz_pj-${rowsIndex}"></label>
                        </div>

                        <div id="certificat_competente_inputs_pj-${rowsIndex}" class="hidden">
                            <label>Nr. Certificat Competențe: <input
                                    class="mt-1 block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="text" id="incetare_suspendare_nr_certificat_pj-${rowsIndex}"></label><br>
                            <label>Eliberat de: <input
                                    class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="text" id="incetare_suspendare_eliberat_certificat_pj-${rowsIndex}"></label><br>
                            <label>La data de: <input
                                    class=" block w-full bg-gray-50 border border-gray-300 text-gray-900 rounded-md"
                                    type="date" id="incetare_suspendare_data_certificat_pj-${rowsIndex}"></label>
                        </div>
                </div>
            </div>
        `;
            dynamicRowsContainer.appendChild(newRow);
            rowsIndex++;
            // add listener to incetare suspendare radio buttons
            newRow.querySelectorAll('input[name^="incetare_suspendare_document_type_pj"]')
                ?.forEach(button => {
                    button.addEventListener('change', addIncetareSuspendarePJRadiosListener);
                });

            const valueTipSolicitare = selectTipSolicitare.options[selectTipSolicitare.selectedIndex]
                .dataset
                .denumire;
            console.log('value tip solicitare in addRow', valueTipSolicitare);

            if (valueTipSolicitare.toLowerCase() === 'suspendare') {
                document.getElementById('dropdown-art-suspendare').classList.add('hidden');
                document.querySelectorAll('[id^="dropdown-art-suspendare-pj-"]').forEach((dropdown) => {
                    dropdown.classList.remove('hidden');
                });
            } else {
                document.getElementById('dropdown-art-suspendare').classList.add('hidden');
                document.querySelectorAll('[id^="dropdown-art-suspendare-pj-"]').forEach((dropdown) => {
                    dropdown.classList.add('hidden');
                });

            }
            // Facem input zile suspendare hidden de fiecare data cand se schimba PF/PJ/Type-ul
            document.getElementById('input-zile-suspendare').classList.add('hidden');
            document.querySelectorAll('[id^="input-zile-suspendare-pj-"]').forEach((dropdown) => {
                dropdown.classList.add('hidden');
            });


            if (valueTipSolicitare.toLowerCase() === 'incetaresuspendare') {
                document.getElementById('incetare-suspendare-inputs').classList.add('hidden');
                document.querySelectorAll('[id^="incetare-suspendare-inputs-pj-"]').forEach((
                    dropdown) => {
                    dropdown.classList.remove('hidden');
                });
            } else {
                document.getElementById('incetare-suspendare-inputs').classList.add('hidden');
                document.querySelectorAll('[id^="incetare-suspendare-inputs-pj-"]').forEach((
                    dropdown) => {
                    dropdown.classList.add('hidden');
                });
            }

            // Attach event listener to the new button
            addCnpPjValidationListener(newRow.querySelector('.validareCnpPJ'));
            addDropdownArtSuspendareListener();


        }

        // Function to remove a row
        function removeRow(event) {
            if (dynamicRowsContainer.children.length > 1) {
                event.target.parentElement.parentElement.remove();
                rowsIndex--;
            }
        }

        // Event listener for adding a new row
        addRowButton.addEventListener('click', () => {

            addRow();

        });

        // Event listener for removing a row
        dynamicRowsContainer.addEventListener('click', function(event) {
            if (event.target.classList.contains('remove-row')) {
                removeRow(event);
            }
        });
        // Function to collect data from the dynamic rows and format it into an array of objects
        function collectSolicitantiData() {
            const rows = document.querySelectorAll('.solicitant-row');
            const solicitantiData = [];
            console.log('rows', rows);
            rows.forEach(row => {
                console.log('row', row);
                const nume = row.querySelector('input[placeholder="Nume"]')?.value;
                const prenume = row.querySelector('input[placeholder="Prenume"]')?.value;
                const cnp = row.querySelector('input[placeholder="CNP"]')?.value;
                const motivSuspendare = row?.querySelector(
                        '[id^="dropdown-art-suspendare-pj"], [name^="dropdown-art-suspendare-pj"]')
                    ?.value;
                const nrZileSuspendare = row?.querySelector(
                        '[id^="input-zile-suspendare-pj"], [name^="input-zile-suspendare-pj"]')
                    ?.value;

                const incetareSuspendareNrAviz = row.querySelector(
                    '[id^="incetare_suspendare_nr_aviz_pj-"]')?.value || '';
                const incetareSuspendareEliberatAviz = row.querySelector(
                    '[id^="incetare_suspendare_eliberat_aviz_pj-"]')?.value || '';
                const incetareSuspendareDataAviz = row.querySelector(
                    '[id^="incetare_suspendare_data_aviz_pj-"]')?.value || '';
                const incetareSuspendareNrCertificat = row.querySelector(
                    '[id^="incetare_suspendare_nr_certificat_pj-"]')?.value || '';
                const incetareSuspendareEliberatCertificat = row.querySelector(
                    '[id^="incetare_suspendare_eliberat_certificat_pj-"]')?.value || '';
                const incetareSuspendareDataCertificat = row.querySelector(
                    '[id^="incetare_suspendare_data_certificat_pj-"]')?.value || '';


                console.log('nume prenume cnp', nume, prenume, cnp);
                if (nume && prenume && cnp) {
                    solicitantiData.push({
                        nume,
                        prenume,
                        cnp,
                        motivSuspendare,
                        nrZileSuspendare,
                        incetareSuspendareNrAviz,
                        incetareSuspendareDataAviz,
                        incetareSuspendareEliberatAviz,
                        incetareSuspendareNrCertificat,
                        incetareSuspendareEliberatCertificat,
                        incetareSuspendareDataCertificat,
                    });
                }
            });

            return solicitantiData;
        }

        const radioPersoanaFizica = document.getElementById('solicitant-pers-fizica');
        const radioPersoanaJuridica = document.getElementById('solicitant-pers-juridica');

        function debifeazaRadio() {
            radioPersoanaFizica.checked = false;
            radioPersoanaJuridica.checked = false;

            // Declanșează manual evenimentul 'change'
            const event = new Event('change');
            radioPersoanaFizica.dispatchEvent(event);
            radioPersoanaJuridica.dispatchEvent(event);
        }


        const clearAllInputs = () => {
            document.querySelectorAll("input").forEach(input => input.value = "");

            document.querySelectorAll("select").forEach(select => {
                if (select.id !== "tip-solicitare" && select.id !== "myDropdown") {
                    select.selectedIndex = 0; // Reset to the first option
                }
            });

            autorizatiiColumns = [];
            autorizatiiData = [];
            selectedRowIds.clear();
            idsAutorizatiiAnterioareSelected = [];
            rowsIndex = 1;
            solicitantNume = '';
            solicitantPrenume = '';
            // updateModalContent();
            confirmedTableBody.innerHTML = '';
            confirmedTableHead.innerHTML = '';

            // document.getElementById('tip-autorizatie-0')?.textContent = '';
            // document.getElementById('tip-autorizatie-1')?.textContent = '';
            // document.getElementById('tip-autorizatie-2')?.textContent = '';

        };

        const handleSelectChange = function(event) {

            let value;
            debifeazaRadio();
            clearAllInputs();
            if (event) {
                console.log('event ', event.target)
                value = event.target.options[event.target.selectedIndex].dataset.denumire
            } else {

                value = selectTipSolicitare.options[selectTipSolicitare.selectedIndex].dataset
                    .denumire;
                console.log('init value', value);
            }
            console.log('value', value);
            const type2Values = ['reexaminare', 'reprogramare', 'reautorizare'];

            const type3Values = ['vizeperiodice', 'duplicate', 'schimbarenume', 'suspendare',
                'incetaresuspendare',
                'retragereautorizatie',
                'preschimbare'
            ];

            const type2Class = 'type2';
            const type2HiddenClass = 'type2Hidden';
            const type3SchimbareNume = 'type3SchimbareNume';
            const type3Class = 'type3';
            const type3Hidden = 'type3Hidden';
            const type3Reautorizare = 'type3Reautorizare';
            const type3ReautorizareHidden = 'type3ReautorizareHidden';





            if (type2Values.includes(value.toLowerCase())) {
                // afisam elementele pentru type2
                const elementsType2 = document.getElementsByClassName(type2Class);
                Array.from(elementsType2).forEach(element => element.classList.remove('hidden'));

                // pentru type3 afisam elementele daca au fost ascunse anterior
                const elementsType3 = document.getElementsByClassName(type3Class);
                Array.from(elementsType3).forEach(element => element.classList.remove('hidden'));
                const elementsType3Hidden = document.getElementsByClassName(type3Hidden);
                Array.from(elementsType3Hidden).forEach(element => element.classList.add('hidden'));

                const elementsType3SchimbareNumeToHide = document.getElementsByClassName(
                    type3SchimbareNume);
                Array.from(elementsType3SchimbareNumeToHide).forEach(element => element.classList.add(
                    'hidden'));

                if (value.toLowerCase() === type2Values[1]) {
                    // ascundem elementele type2Hidden
                    const elementsType2ToHide = document.getElementsByClassName(type2HiddenClass);
                    Array.from(elementsType2ToHide).forEach(element => element.classList.add(
                        'hidden'));
                } else if (value.toLowerCase() === type2Values[0]) {

                    // afisam elementele type2Hidden
                    const elementsType2ToHide = document.getElementsByClassName(type2HiddenClass);
                    Array.from(elementsType2ToHide).forEach(element => element.classList.remove(
                        'hidden'));
                }

                if (value.toLowerCase() === 'reautorizare') {
                    const elementsType3Reautorizare = document.getElementsByClassName(type3Reautorizare);
                    Array.from(elementsType3Reautorizare).forEach(element => element.classList.remove(
                        'hidden'));

                    const elementsType3ReautorizareHidden = document.getElementsByClassName(
                        type3ReautorizareHidden);
                    Array.from(elementsType3ReautorizareHidden).forEach(element => element.classList.add(
                        'hidden'));

                } else {
                    const elementsType3Reautorizare = document.getElementsByClassName(type3Reautorizare);
                    Array.from(elementsType3Reautorizare).forEach(element => element.classList.add(
                        'hidden'));

                    const elementsType3ReautorizareHidden = document.getElementsByClassName(
                        type3ReautorizareHidden);
                    Array.from(elementsType3ReautorizareHidden).forEach(element => element.classList.remove(
                        'hidden'));

                }

            } else if (type3Values.includes(value.toLowerCase())) {
                // ascundem toate elementele type2 daca au fost afisate anterior
                const elementsType2 = document.getElementsByClassName(type2Class);
                Array.from(elementsType2).forEach(element => element.classList.add('hidden'));
                // afisam elementele type2Hidden ascunse anterior
                const elementsType2ToHide = document.getElementsByClassName(type2HiddenClass);
                Array.from(elementsType2ToHide).forEach(element => element.classList.remove(
                    'hidden'));

                // pentru type3 ascundem elementele pe care nu le vrem
                const elementsType3 = document.getElementsByClassName(type3Class);
                Array.from(elementsType3).forEach(element => element.classList.add('hidden'));
                const elementsType3Hidden = document.getElementsByClassName(type3Hidden);
                Array.from(elementsType3Hidden).forEach(element => element.classList.remove('hidden'));



                if (value.toLowerCase() === 'schimbarenume') {
                    const elementsType3SchimbareNumeToShow = document.getElementsByClassName(
                        type3SchimbareNume);
                    Array.from(elementsType3SchimbareNumeToShow).forEach(element => element.classList
                        .remove('hidden'));
                } else {
                    const elementsType3SchimbareNumeToHide = document.getElementsByClassName(
                        type3SchimbareNume);
                    Array.from(elementsType3SchimbareNumeToHide).forEach(element => element.classList.add(
                        'hidden'));
                }

            } else {
                // ascundem toate elementele type2 daca au fost afisate anterior
                const elementsType2 = document.getElementsByClassName(type2Class);
                Array.from(elementsType2).forEach(element => element.classList.add('hidden'));
                // afisam elementele type2Hidden ascunse anterior
                const elementsType2ToHide = document.getElementsByClassName(type2HiddenClass);
                Array.from(elementsType2ToHide).forEach(element => element.classList.remove(
                    'hidden'));

                // pentru type3 afisam elementele daca au fost ascunse anterior
                const elementsType3 = document.getElementsByClassName(type3Class);
                Array.from(elementsType3).forEach(element => element.classList.remove('hidden'));
                const elementsType3Hidden = document.getElementsByClassName(type3Hidden);
                Array.from(elementsType3Hidden).forEach(element => element.classList.add('hidden'));

                const elementsType3SchimbareNumeToHide = document.getElementsByClassName(
                    type3SchimbareNume);
                Array.from(elementsType3SchimbareNumeToHide).forEach(element => element.classList.add(
                    'hidden'));
            }
        };
        handleSelectChange();
        selectTipSolicitare.addEventListener('change', handleSelectChange);

        // logica solicitanti
        const inputsFizica = document.getElementById('container-inputs-fizica');
        const inputsFizica2 = document.getElementById('container-inputs-fizica-2');
        const inputsFizica3 = document.getElementById('container-inputs-fizica-3');
        const inputsFizicaSchimbareNume = document.getElementById('container-inputs-schimbare');
        const inputsJuridicaSchimbareNume = document.getElementById('container-inputs-schimbare-pj');
        const alegerePersoanaJuridica = document.getElementById('alegere-pers-juridica');



        const inputsJuridica = document.getElementById('container-inputs-juridica');
        const inputsJuridica2 = document.getElementById('container-inputs-juridica-2');


        // Event listener for the change event on the radio buttons
        radioPersoanaFizica.addEventListener('change', function(event) {

            const valueTipSolicitare = selectTipSolicitare.options[selectTipSolicitare.selectedIndex]
                .dataset
                .denumire;

            if (valueTipSolicitare.toLowerCase() === 'suspendare') {
                document.getElementById('dropdown-art-suspendare').classList.remove('hidden');
                document.querySelectorAll('[id^="dropdown-art-suspendare-pj-"]').forEach((dropdown) => {
                    dropdown.classList.add('hidden');
                });
            } else {
                document.getElementById('dropdown-art-suspendare').classList.add('hidden');
                document.querySelectorAll('[id^="dropdown-art-suspendare-pj-"]').forEach((dropdown) => {
                    dropdown.classList.add('hidden');
                });

            }
            // Facem input zile suspendare hidden de fiecare data cand se schimba PF/PJ
            document.getElementById('input-zile-suspendare').classList.add('hidden');
            document.querySelectorAll('[id^="input-zile-suspendare-pj-"]').forEach((dropdown) => {
                dropdown.classList.add('hidden');
            });


            if (valueTipSolicitare.toLowerCase() === 'incetaresuspendare') {
                document.getElementById('incetare-suspendare-inputs').classList.remove(
                    'hidden');
                document.querySelectorAll('[id^="incetare-suspendare-inputs-pj-"]').forEach(
                    (
                        dropdown) => {
                        dropdown.classList.add('hidden');
                    });
            } else {
                document.getElementById('incetare-suspendare-inputs').classList.add(
                    'hidden');
                document.querySelectorAll('[id^="incetare-suspendare-inputs-pj-"]').forEach(
                    (
                        dropdown) => {
                        dropdown.classList.add('hidden');
                    });
            }

            console.log('event.target.checked', event.target.checked);
            if (event.target
                .checked) {
                inputsJuridica.classList.add('hidden');
                inputsJuridica2.classList.add('hidden');
                alegerePersoanaJuridica.classList.add('hidden');
                inputsFizica.classList.remove('hidden');
                inputsFizica2.classList.remove('hidden');
                // inputsFizicaSchimbareNume.classList.remove('hidden');
            } else {
                inputsJuridica.classList.add('hidden');
                inputsJuridica2.classList.add('hidden');
                alegerePersoanaJuridica.classList.add('hidden');
                inputsFizica.classList.add('hidden');
                inputsFizica2.classList.add('hidden');
                // inputsFizicaSchimbareNume.classList.add('hidden');
            }
        });

        radioPersoanaJuridica.addEventListener('change', function() {
            const valueTipSolicitare = selectTipSolicitare.options[selectTipSolicitare
                    .selectedIndex]
                .dataset
                .denumire;

            if (valueTipSolicitare.toLowerCase() === 'suspendare') {
                document.getElementById('dropdown-art-suspendare').classList.add('hidden');
                document.querySelectorAll('[id^="dropdown-art-suspendare-pj-"]').forEach((
                    dropdown) => {
                    dropdown.classList.remove('hidden');
                });

            } else {
                document.getElementById('dropdown-art-suspendare').classList.add('hidden');
                document.querySelectorAll('[id^="dropdown-art-suspendare-pj-"]').forEach((
                    dropdown) => {
                    dropdown.classList.add('hidden');
                });

            }
            // Facem input zile suspendare hidden de fiecare data cand se schimba PF/PJ
            document.getElementById('input-zile-suspendare').classList.add('hidden');
            document.querySelectorAll('[id^="input-zile-suspendare-pj-"]').forEach((dropdown) => {
                dropdown.classList.add('hidden');
            });


            if (valueTipSolicitare.toLowerCase() === 'incetaresuspendare') {
                document.getElementById('incetare-suspendare-inputs').classList.add(
                    'hidden');
                document.querySelectorAll('[id^="incetare-suspendare-inputs-pj-"]').forEach(
                    (
                        dropdown) => {
                        dropdown.classList.remove('hidden');
                    });
            } else {
                document.getElementById('incetare-suspendare-inputs').classList.add(
                    'hidden');
                document.querySelectorAll('[id^="incetare-suspendare-inputs-pj-"]').forEach(
                    (
                        dropdown) => {
                        dropdown.classList.add('hidden');
                    });
            }

            if (event.target.checked) {
                inputsJuridica.classList.remove('hidden');
                inputsJuridica2.classList.remove('hidden');
                alegerePersoanaJuridica.classList.remove('hidden');
                inputsFizica.classList.add('hidden');
                inputsFizica2.classList.add('hidden');
            } else {
                inputsJuridica.classList.add('hidden');
                inputsJuridica2.classList.add('hidden');
                alegerePersoanaJuridica.classList.add('hidden');
                inputsFizica.classList.add('hidden');
                inputsFizica2.classList.add('hidden');
            }

        });
        let selectedIsfOption;

        // event listener ISF uri
        document.getElementById('isfDropdown').addEventListener('change', function() {
            const selectedValue = this.value;
            if (selectedValue) {
                selectedIsfOption = document.querySelector(
                    `#isfDropdown option[value='${selectedValue}']`);
                console.log('selected isf option', selectedIsfOption);
                console.log('data isf', selectedIsfOption?.getAttribute('data-isf'));
                const isfData = JSON.parse(selectedIsfOption?.getAttribute('data-isf'));

                document.getElementById('cod-serviciu-isf').value = isfData.cod_serviciu;
                document.getElementById('unitate-isf').value = isfData.ISF;
                document.getElementById('telefon-ptt-isf').value = isfData.tel_ISF;
                document.getElementById('fax-ptt-isf').value = isfData.fax_ISF;
                document.getElementById('telefon-cfr-isf').value = isfData.tel_ISF_CFR;
                document.getElementById('email-cfr-isf').value = isfData.email_ISF;
                document.getElementById('inspector-sef-isf').value = isfData.sef_ISF;
            } else {
                selectedIsfOption = null;

                document.getElementById('cod-serviciu-isf').value = '';
                document.getElementById('unitate-isf').value = '';
                document.getElementById('telefon-ptt-isf').value = '';
                document.getElementById('fax-ptt-isf').value = '';
                document.getElementById('telefon-cfr-isf').value = '';
                document.getElementById('email-cfr-isf').value = '';
                document.getElementById('inspector-sef-isf').value = '';
            }
        });



        //logica date
        const teoreticaInDataDe = document.getElementById('teoretica-in-data-de');
        const teoreticaDe = document.getElementById('teoretica-de');
        const teoreticaInDataDePana = document.getElementById('teoretica-in-data-de-pana');
        const teoreticaPana = document.getElementById('teoretica-pana');
        const practicaInDataDe = document.getElementById('practica-in-data-de');
        const practicaDe = document.getElementById('practica-de');
        const practicaInDataDePana = document.getElementById('practica-in-data-de-pana');

        // Add this after the dropdown-art-suspendare select element
        document.getElementById('dropdown-art-suspendare').addEventListener('change', function() {
            console.log('dropdown changed');
            const selectedValue = this.value;
            const zileInputContainer = document.getElementById('input-zile-suspendare');
            console.log('selectedVal', selectedValue);

            if (selectedValue === 'Art. 8 alin 1. lit. s)') {
                console.log('it is s');
                zileInputContainer.classList.remove('hidden');

            } else {
                zileInputContainer.classList.add('hidden');
            }
        });

        // Listener dropdown art suspendare - dam enable la input nr zile suspendare pt litera S
        function dropdownArtSuspendarePjListener() {
            const selectedValue = this.value;
            const rowNr = this.id.split('-').pop();
            const zileInputContainer = document.getElementById(
                    `input-zile-suspendare-pj-${rowNr}`) ||
                document.createElement('div');

            if (selectedValue === 'Art. 8 alin 1. lit. s)') {
                console.log('row nr', rowNr);
                console.log('zile input', zileInputContainer);
                zileInputContainer.classList.remove('hidden');

            } else {
                zileInputContainer.classList.add('hidden');
            }

        }

        function addDropdownArtSuspendareListener() {
            document.querySelectorAll('.dropdown-art-suspendare-pj').forEach(select => {
                select.addEventListener('change', dropdownArtSuspendarePjListener);
            });
        };

        addDropdownArtSuspendareListener();

        const practicaPana = document.getElementById('practica-pana');

        const onSubmit = async () => {
            console.log('onsubmit');
            const mapErrorsToFields = {
                'tipSolicitare': 'Tip Solicitare',
                'numarCerereSolicitant': 'Numar cerere solicitant',
                'dataCerereSolicitant': 'Data cerere solicitant',
                'locatieCenafer': 'Locatie Cenafer',
                'numarCenafer': 'Numar Cenafer',
                'dataCenafer': 'Data Cenafer',
                'codServiciuIsf': 'Cod Serviciu ISF',
                'unitateIsf': 'Unitate ISF',
                'telefonPttIsf': 'Telefon ISF',
                'faxPttIsf': 'Fax ISF',
                'telefonCfrIsf': 'Telefon CFR ISF',
                'emailCfrIsf': 'Email CFR ISF',
                'inspectorSefIsf': 'Inspector Sef ISF',
                'numarIesireSolicitare': 'Numar Iesire Solicitare',
                'dataIesireSolicitare': 'Data Iesire Solicitare',
                'redactataDeSolicitare': 'Redactata De Solicitare',
                'dataOraRedactareSolicitare': 'Data Ora Redactare Solicitare',
                'numePresedinteComisie': 'Nume Presedinte Comisie',
                'prenumePresedinteComisie': 'Prenume Presedinte Comisie',
                'numeMembruComisie': 'Nume Membru Comisie',
                'prenumeMembruComisie': 'Prenume Membru Comisie',
                // 'serieAtestatMembru': 'Serie Atestat Membru',
                // 'numarAtestatMembru': 'Numar Atestat Membru',
                'serieAtestatReprezentant': 'Serie Atestat Reprezentant',
                'numarAtestatReprezentant': 'Numar Atestat Reprezentant',
                'numeReprezentantUnitateFeroviara': 'Nume Reprezentant Unitate Feroviara',
                'prenumeReprezentantUnitateFeroviara': 'Prenume Reprezentant Unitate Feroviara',
                'numeReprezentantCenafer': 'Nume Reprezentant Cenafer',
                'prenumeReprezentantCenafer': 'Prenume Reprezentant Cenafer',
                // 'serieReprezentantCenafer': 'Serie Atestat Cenafer',
                // 'numarReprezentantCenafer': 'Numar Atestat Cenafer',
                'solicitantPersJuridicaDenumire': 'Solicitant Pers Juridica Denumire',
                'solicitantPersJuridicaCif': 'Solicitant Pers Juridica CIF',
                'solicitanti': 'Lista solicitanti',
                'solicitantPersFizicaNume': 'Solicitant Pers Fizica Nume',
                'solicitantPersFizicaPrenume': 'Solicitant Pers Fizica Prenume',
                'solicitantPersFizicaSerieCi': 'Solicitant Pers Fizica Serie CI',
                'solicitantPersFizicaCnp': 'Solicitant Pers Fizica CNP',
                'solicitantPersFizicaNumarCi': 'Solicitant Pers Fizica Numar CI',
                'solicitantPersFizicaLocalitate': 'Solicitant Pers Fizica Localitate',
                'solicitantPersFizicaAdresa': 'Solicitant Pers Fizica Adresa',
                'locatieExaminareTeoretica': 'Locatie Examinare Teoretica',
                'dataInceputExaminareTeoretica': 'Data Inceput Examinare Teoretica',
                'dataSfarsitExaminareTeoretica': 'Data Sfarsit Examinare Teoretica',
                'locatieExaminarePractica': 'Locatie Examinare Practica',
                'dataInceputExaminarePractica': 'Data Inceput Examinare Practica',
                'dataSfarsitExaminarePractica': 'Data Sfarsit Examinare Practica',
                'numarAprobareInitialaScmap': 'Numar Aprobare Initiala SC MAP',
                'dataAprobareInitialaScmap': 'Data Aprobare Initiala SC MAP',
                'functie': 'Functie',
                'autorizare': 'Autorizare',
                'activitate': 'Activitate',
                'idsAutorizatiiAnterioareSelected': 'Autorizatii Anterioare',
                'motivSuspendare': 'Motiv suspendare',
                'nrZileSuspendare': 'Numar zile suspendare',
                // de adaugat pt incetare suspendare

            };

            const selectTipSolicitareVal = document.getElementById('tip-solicitare');
            const formData = {
                tipSolicitare: selectTipSolicitare?.options[selectTipSolicitare
                        .selectedIndex]
                    .dataset.denumire,
                tipSolicitareId: selectTipSolicitare?.value,
                regimUrgenta: document.getElementById('regim-urgenta')?.checked,
                numarCerereSolicitant: document.getElementById(
                        'numar-cerere-solicitant')
                    ?.value,
                dataCerereSolicitant: document.getElementById('data-cerere-solicitant')
                    ?.value,
                locatieCenafer: document.getElementById('locatie-cenafer')?.value,
                numarCenafer: document.getElementById('numar-cenafer')?.value,
                dataCenafer: document.getElementById('data-cenafer')?.value,
                codServiciuIsf: document.getElementById('cod-serviciu-isf')?.value,
                unitateIsf: document.getElementById('unitate-isf')?.value,
                telefonPttIsf: document.getElementById('telefon-ptt-isf')?.value,
                faxPttIsf: document.getElementById('fax-ptt-isf')?.value,
                telefonCfrIsf: document.getElementById('telefon-cfr-isf')?.value,
                emailCfrIsf: document.getElementById('email-cfr-isf')?.value,
                inspectorSefIsf: document.getElementById('inspector-sef-isf')?.value,
                numarIesireSolicitare: document.getElementById(
                        'numar-iesire-solicitare')
                    ?.value,
                dataIesireSolicitare: document.getElementById('data-iesire-solicitare')
                    ?.value,
                redactataDeSolicitare: document.getElementById(
                        'redactata-de-solicitare')
                    ?.value,
                dataOraRedactareSolicitare: document.getElementById(
                    'data-ora-redactare-solicitare')?.value,
                numePresedinteComisie: document.getElementById(
                        'nume-presedinte-comisie')
                    ?.value,
                prenumePresedinteComisie: document.getElementById(
                        'prenume-presedinte-comisie')
                    ?.value,
                numeMembruComisie: document.getElementById('nume-membru-comisie')
                    ?.value,
                prenumeMembruComisie: document.getElementById('prenume-membru-comisie')
                    ?.value,
                // serieAtestatMembru: document.getElementById('serie-atestat-membru')?.value,
                // numarAtestatMembru: document.getElementById('numar-atestat-membru')?.value,
                serieAtestatReprezentant: document.getElementById(
                        'serie-atestat-reprezentant')
                    ?.value,
                numarAtestatReprezentant: document.getElementById(
                        'numar-atestat-reprezentant')
                    ?.value,
                numeReprezentantUnitateFeroviara: document.getElementById(
                    'nume-reprezentant-unitate-feroviara')?.value,
                prenumeReprezentantUnitateFeroviara: document.getElementById(
                    'prenume-reprezentant-unitate-feroviara')?.value,
                numeReprezentantCenafer: document.getElementById(
                        'nume-reprezentant-cenafer')
                    ?.value,
                prenumeReprezentantCenafer: document.getElementById(
                        'prenume-reprezentant-cenafer')
                    ?.value,
                // serieReprezentantCenafer: document.getElementById('serie-atestat-cenafer')
                // ?.value,
                // numarReprezentantCenafer: document.getElementById('numar-atestat-cenafer')
                // ?.value,
                solicitantPersJuridicaDenumire: document.getElementById(
                    'solicitant-pers-juridica-denumire')?.value,
                solicitantPersJuridicaCif: document.getElementById(
                    'solicitant-pers-juridica-cif')?.value,
                // numeSolicitantPj: document.getElementById('nume-solicitant-pj')?.value,
                // prenumeSolicitantPj: document.getElementById('prenume-solicitant-pj')?.value,
                // cnpSolicitantPj: document.getElementById('cnp-solicitant-pj')?.value,
                solicitanti: collectSolicitantiData(),
                solicitantPersFizicaNume: document.getElementById(
                    'solicitant-pers-fizica-nume')?.value,
                solicitantPersFizicaPrenume: document.getElementById(
                    'solicitant-pers-fizica-prenume')?.value,
                solicitantPersFizicaSerieCi: document.getElementById(
                    'solicitant-pers-fizica-serie-CI')?.value,
                solicitantPersFizicaCnp: document.getElementById(
                        'solicitant-pers-fizica-cnp')
                    ?.value,
                solicitantPersFizicaNumarCi: document.getElementById(
                    'solicitant-pers-fizica-numar-CI')?.value,
                solicitantPersFizicaLocalitate: document.getElementById(
                    'solicitant-pers-fizica-localitate')?.value,
                solicitantPersFizicaAdresa: document.getElementById(
                    'solicitant-pers-fizica-adresa')?.value,
                locatieExaminareTeoretica: document.getElementById(
                    'locatie-examinare-teoretica')?.value,
                dataInceputExaminareTeoretica: document.getElementById(
                    'teoretica-in-data-de')?.value,
                dataSfarsitExaminareTeoretica: document.getElementById(
                    'teoretica-in-data-de-pana')?.value,
                locatieExaminarePractica: document.getElementById(
                    'locatie-examinare-practica')?.value,
                dataInceputExaminarePractica: document.getElementById(
                    'practica-in-data-de')?.value,
                dataSfarsitExaminarePractica: document.getElementById(
                    'practica-in-data-de-pana')?.value,
                numarAprobareInitialaScmap: document.getElementById(
                        'numar-aprobare-initiala')
                    ?.value,
                dataAprobareInitialaScmap: document.getElementById(
                        'data-aprobare-initiala')
                    ?.value,
                functie: document.getElementById('tip-autorizatie-0')?.textContent ||
                    '',
                autorizare: document.getElementById('tip-autorizatie-1')?.textContent ||
                    '',
                activitate: document.getElementById('tip-autorizatie-2')?.textContent ||
                    '',
                id_isf: JSON.parse(selectedIsfOption?.getAttribute('data-isf'))?.contor,
                idsAutorizatiiAnterioareSelected,
                motivSuspendare: document.getElementById('dropdown-art-suspendare')
                    ?.value,
                incetareSuspendareNrAviz: document.getElementById(
                        'incetare_suspendare_nr_aviz')
                    ?.value || '',
                incetareSuspendareEliberatAviz: document.getElementById(
                    'incetare_suspendare_eliberat_aviz')?.value || '',
                incetareSuspendareDataAviz: document.getElementById(
                        'incetare_suspendare_data_aviz')
                    ?.value || '',
                incetareSuspendareNrCertificat: document.getElementById(
                    'incetare_suspendare_nr_certificat')?.value || '',
                incetareSuspendareEliberatCertificat: document.getElementById(
                    'incetare_suspendare_eliberat_certificat')?.value || '',
                incetareSuspendareDataCertificat: document.getElementById(
                    'incetare_suspendare_data_certificat')?.value || '',
                nrZileSuspendare: document.getElementById('input-zile-suspendare')
                    ?.value || ''
            };
            console.log('Solicitanti', JSON.stringify(formData.solicitanti));

            if (!radioPersoanaFizica.checked && !formData.solicitanti.length) {
                alert('Eroare de validare: Introduceti cel putin un solicitant');
                return;
            }
            const type3Values = ['vizeperiodice', 'duplicate', 'schimbarenume',
                'suspendare',
                'incetaresuspendare',
                'retragereautorizatie',
                'preschimbare', 'reautorizare'
            ];
            if (type3Values.includes(selectTipSolicitare?.options[selectTipSolicitare
                        .selectedIndex]
                    .dataset.denumire.toLowerCase()) && !idsAutorizatiiAnterioareSelected
                .length) {
                alert('Selectați cel puțin o autorizație anterioară!');
                return;
            }
            try {

                const response = await fetch(
                    `/solicitari/store${radioPersoanaFizica.checked?'PersFizica':'PersJuridica'}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector(
                                    'meta[name="csrf-token"]')
                                .getAttribute('content')
                        },
                        body: JSON.stringify(formData),
                        redirect: 'manual'
                    });
                console.log('response', response);

                let responseData = null;
                try {
                    responseData = await response.json(); // Only attempt once
                } catch (jsonError) {
                    console.warn("Response is not JSON:", jsonError);
                }

                if (response.status === 200) {
                    //pe asteaa le comentam ca nu primeste json din controller, deci da eroare ca raspunsul nu e json si o sa ajunga in catch
                    //const responseData = await response.json();
                    //console.log('response data', responseData);

                    //alert(responseData.message);
                    // const responseData = await response.json();
                    alert(responseData?.message ||
                        'Solicitarea a fost inregistrata cu succes!');

                    // window.location.href = '/solicitari';
                } else if (response.status === 422) {
                    // const errorData = await response.json();
                    let fieldErrors = '';

                    responseData.errors.forEach(errorField => {

                        fieldErrors.length > 0 ? fieldErrors +=
                            `, ${mapErrorsToFields[errorField]}` : fieldErrors +=
                            `${mapErrorsToFields[errorField]}`
                    })

                    alert(`${errorData.message}: ${fieldErrors}`);
                } else {
                    // const errorData = await response.json();
                    alert('Eroare: ' + responseData.message);
                }
            } catch (error) {
                console.error('Eroare:', error);
                alert('A avut loc o eroare. Verifica toate campurile si incearca din nou.');
            }
        }
        document.getElementById('submitBtn').addEventListener('click', function(event) {
            event.preventDefault();
            onSubmit();
        });



        function getDayOfWeek(date) {
            const daysOfWeek = ['Duminică', 'Luni', 'Marți', 'Miercuri', 'Joi', 'Vineri',
                'Sâmbătă'
            ];
            return daysOfWeek[date.getDay()];
        }

        // Event listener for the change event on the date inputs
        teoreticaInDataDe.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            console.log('selected date', selectedDate);
            if (!isNaN(selectedDate.getTime())) {

                const dayOfWeek = getDayOfWeek(selectedDate);
                teoreticaDe.value = `${dayOfWeek}`;
            }
        });

        teoreticaInDataDePana.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            if (!isNaN(selectedDate.getTime())) {

                const dayOfWeek = getDayOfWeek(selectedDate);
                teoreticaPana.value = `${dayOfWeek}`;
            }
        });

        practicaInDataDe.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            if (!isNaN(selectedDate.getTime())) {

                const dayOfWeek = getDayOfWeek(selectedDate);
                practicaDe.value = `${dayOfWeek}`;
            }
        });

        practicaInDataDePana.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            if (!isNaN(selectedDate.getTime())) {

                const dayOfWeek = getDayOfWeek(selectedDate);
                practicaPana.value = `${dayOfWeek}`;
            }
        });
    });
</script>
