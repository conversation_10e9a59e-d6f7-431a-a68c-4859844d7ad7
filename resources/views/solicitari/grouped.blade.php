@props(['menus', 'dateTabel', 'columns'])


<x-app-layout>
    <x-slot name="header">
        <nav
            class="flex justify-center items-center flex-wrap gap-1 bg-gray-200 dark:bg-gray-800 p-4 font-semibold text-xl dark:text-white text-black dark:text-gray-200 leading-tight">
            @foreach ($menus as $menu)
                <x-custom-dropdown align="left" width="48" contentClasses="py-1 bg-white dark:bg-gray-800"
                    :menuObj="$menu">
                </x-custom-dropdown>
            @endforeach
        </nav>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <form class='mb-2' method="GET" action="{{ route('solicitariGrouped.index') }}"> {{-- //aici plec in web.php --}}
                <div class="flex items-center">
                    <input type="text" name="search" value="{{ request('search') }}"
                        class="form-input rounded-md shadow-sm mt-1 block w-full"
                        placeholder="Căutați după CNP sau Nume Prenume">
                    <button type="submit" class="ml-3 btn btn-primary">Căutați</button>
                    @if (isset($search))
                        <a href="{{ route('solicitariGrouped.index') }}"
                            class="ml-3 btn btn-secondary text-red-500">X</a>
                    @endif
                </div>
            </form>

            @if (isset($columns) && isset($dateTabel))
                <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>

                                @foreach ($columns as $column)
                                    <th scope="col" class="px-6 py-3">{{ $column }}</th>
                                @endforeach
                                <th scope="col" class="p-4"></th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($dateTabel as $dataRow)
                                <tr
                                    class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">

                                    @foreach ($columns as $column)
                                        <td class="px-6 py-4">{{ $dataRow->$column }}</td>
                                    @endforeach
                                    <td class="px-6 py-4">
                                        <a href="{{ route('solicitari.index', ['cnp' => $dataRow->cnp]) }}"
                                            class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Accesare</a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $dateTabel->links() }}
                    </div>
                </div>

            @endif
        </div>
    </div>
</x-app-layout>
