@props(['menus', 'dateTabel', 'columns'])


@php
    $isAdmin = auth()->user()->hasRole('super_admin');
@endphp

<x-app-layout>
    <x-slot name="header">
        <nav
            class="flex justify-center items-center flex-wrap gap-1 bg-gray-200 dark:bg-gray-800 p-4 font-semibold text-xl dark:text-white text-black dark:text-gray-200 leading-tight">
            @foreach ($menus as $menu)
                <x-custom-dropdown align="left" width="48" contentClasses="py-1 bg-white dark:bg-gray-800"
                    :menuObj="$menu">
                </x-custom-dropdown>
            @endforeach
        </nav>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <form class='mb-2' method="GET" action="{{ route('solicitari.index') }}">
                <div class="flex items-center">
                    <input type="text" name="search" value="{{ request('search') }}"
                        class="form-input rounded-md shadow-sm mt-1 block w-full"
                        placeholder="Căutați după NR. Solicitare">
                    <button type="submit" class="ml-3 btn btn-primary">Căutați</button>
                    @if (isset($search))
                        <a href="{{ route('solicitari.index') }}" class="ml-3 btn btn-secondary text-red-500">X</a>
                    @endif

                </div>
            </form>
            <div id='fields-nr-iesire' class='flex flex-row align-center'>
                <input type="text" placeholder="Introdu număr ieșire PENTRU EMITERE LISTĂ" id="inputNrIesire"
                    class="mt-1 mr-2 block w-1/2 bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                <button type="button" id="butonPrintareAutorizatiiEmise"
                    class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Emitere
                    listă autorizații</button>
                <div
                    class="flex justify-center items-center flex-wrap gap-1 p-4 font-semibold text-xl dark:text-white text-black dark:text-gray-200 leading-tight">
                    {{ Log::info('filterMenus: ' . json_encode($filterMenus)) }}
                    @foreach ($filterMenus as $filterMenu)
                        <x-custom-dropdown align="left" width="48" contentClasses="py-1 bg-white dark:bg-gray-800"
                            :menuObj="$filterMenu">
                        </x-custom-dropdown>
                    @endforeach
                </div>
            </div>

        </div>
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="px-6 py-4 sticky flex flex-row flex-wrap gap-1 justify-start">
                <button type="button" id="butonTiparireComunicare"
                    class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Tipărire
                    Comunicare</button>
                <button type="button" id="butonValidareComunicare"
                    class="hidden text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Validare
                    Comunicare</button>
                <button type="button" id="butonEditareComunicare"
                    class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Editează
                    Solicitare</button>
                <button type="button" id="butonTiparireProcesVerbal"
                    class="hidden text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Tipărire
                    machetă proces verbal</button>
                <button type="button" id="butonValidareProcesVerbal"
                    class="hidden text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Validare
                    proces verbal</button>
                <button type="button" id="butonIntroducereNote"
                    class="hidden text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Introducere
                    note</button>
                <button type="button" id="butonPrintarePvRezultate"
                    class="hidden text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Tipărire
                    PV</button>
                <button type="button" id="butonValidarePvRezultate"
                    class="hidden text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Validare
                    PV</button>
                <button type="button" id="butonEditarePvRezultate"
                    class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Editează
                    Rezultate</button>
                <div id='fields-nr-aut' class='hidden flex flex-row align-center'>
                    <input type="number" placeholder="Număr autorizație" id="inputNrAut"
                        class="mt-1 mr-2 block w-10 bg-gray-50 border border-gray-300 text-gray-900 rounded-md">
                    <button type="button" id="butonConfirmareNrAut"
                        class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Confirmare
                        număr autorizație</button>
                </div>

                @hasanyrole('admin|super_admin')
                    <button type="button" id="butonGenerareAut"
                        class="hidden text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">Generare
                        autorizație</button>
                @endhasanyrole
                @role('super_admin')
                    <button type="button" id="butonStergereInreg"
                        class="hidden text-white ml-auto bg-red-600 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 focus:outline-none dark:focus:ring-red-800">Șterge
                        înregistrare</button>

                    <button type="button" id="butonIntoarcereStatus"
                        class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Întoarce
                        status</button>
                @endrole
            </div>
            @if (isset($columns) && isset($dateTabel))
                <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3 sticky">Action</th>
                                @foreach ($columns as $column)
                                    <th scope="col" class="px-6 py-3">{{ $column }}</th>
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($dateTabel as $dataRow)
                                <tr
                                    style="{{ $dataRow->status === 'AVIZE GENERATE' ? 'background-color: #a0f0d0 !important;' : '' }}">


                                    <td class="px-6 py-4 sticky">
                                        <input type="radio" name="row-radio" class="row-radio"
                                            data-row-solicitant-nume-prenume = "{{ $dataRow->solicitant_prenume }}_{{ $dataRow->solicitant_nume }}"
                                            data-row-id="{{ $dataRow->oameni }}" data-status="{{ $dataRow->status }}"
                                            data-tip-pers="{{ $dataRow->unitate }}"
                                            data-nr-aut="{{ $dataRow->nr_aut }}"
                                            data-serie-aut="{{ $dataRow->serie_aut }}"
                                            data-tip-aut="{{ $dataRow->tip_comisie }}"
                                            data-id-solicit-pj="{{ $dataRow->id_solicitare_pj }}"
                                            @if ($dataRow->aut_anterioara && $dataRow->aut_anterioara) data-tip_comisie_aut_anterioara="{{ $dataRow->aut_anterioara->tip_comisie }}"
                                            @else
                                            data-tip_comisie_aut_anterioara="" @endif />
                                    </td>
                                    @foreach ($columns as $column)
                                        <td class="px-6 py-4" data-column="{{ $column }}">
                                            {{ $dataRow->$column }}</td>
                                    @endforeach
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $dateTabel->appends(request()->query())->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const radios = document.querySelectorAll('.row-radio');
        const btnTiparireComunicare = document.getElementById('butonTiparireComunicare');
        const btnValidareComunicare = document.getElementById('butonValidareComunicare');
        const btnEditareComunicare = document.getElementById('butonEditareComunicare');
        const btnTiparireProcesVerbal = document.getElementById('butonTiparireProcesVerbal');
        const btnValidareProcesVerbal = document.getElementById('butonValidareProcesVerbal');
        const fieldsNrAut = document.getElementById('fields-nr-aut');
        const btnConfirmareNrAut = document.getElementById('butonConfirmareNrAut');
        const inputNrAut = document.getElementById('inputNrAut');
        const btnGenerareAut = document?.getElementById('butonGenerareAut');
        const btnIntroducereNote = document.getElementById('butonIntroducereNote');
        const btnPrintarePvRezultate = document.getElementById('butonPrintarePvRezultate');
        const btnValidarePvRezultate = document.getElementById('butonValidarePvRezultate');
        const btnEditarePvRezultate = document.getElementById('butonEditarePvRezultate');
        const btnPrintareAutorizatiiEmise = document.getElementById('butonPrintareAutorizatiiEmise');
        const btnStergereInreg = document.getElementById('butonStergereInreg');
        const btnIntoarecereStatus = document.getElementById('butonIntoarcereStatus');





        let selectedRowId = null;
        let selectedRowStatus = null;
        let selectedRowTipPers = null;
        let selectedRowSerieAut = null;
        let selectedRowTipAut = null;
        let selectedRowNrAut = null;
        let selectedRowIdSolicitPj = null;
        let selectedRowTipComisieAutAnterioara = null;
        let solectedRowNumePrenume = null;

        // tip_comisie_aut_anterioara
        // get row with selectedRowId as data-row-id and update status
        const updateButtonVisibility = () => {
            const anyChecked = Array.from(radios).some(radio => radio.checked);
            if (anyChecked) {
                const radio = document.querySelector('.row-radio:checked');
                selectedRowId = radio.getAttribute('data-row-id');
                selectedRowStatus = radio.getAttribute('data-status');
                selectedRowTipPers = radio.getAttribute('data-tip-pers');
                selectedRowSerieAut = radio.getAttribute('data-serie-aut');
                selectedRowTipAut = radio.getAttribute('data-tip-aut');
                selectedRowNrAut = radio.getAttribute('data-nr-aut');
                selectedRowIdSolicitPj = radio.getAttribute('data-id-solicit-pj');
                selectedRowTipComisieAutAnterioara = radio.getAttribute('data-tip_comisie_aut_anterioara');
                selectedRowNumePrenume = radio.getAttribute('data-row-solicitant-nume-prenume');

                console.log('selected row status', selectedRowStatus);
                switch (selectedRowStatus) {
                    case 'IN LUCRU':
                        btnTiparireComunicare.classList.remove('hidden');
                        btnEditareComunicare.classList.remove('hidden');
                        btnValidareComunicare.classList.add('hidden');
                        btnTiparireProcesVerbal.classList.add('hidden');
                        btnValidareProcesVerbal.classList.add('hidden');
                        btnIntroducereNote.classList.add('hidden');
                        btnValidarePvRezultate.classList.add('hidden');
                        btnPrintarePvRezultate.classList.add('hidden');
                        btnEditarePvRezultate.classList.add('hidden');
                        fieldsNrAut.classList.add('hidden');
                        btnGenerareAut?.classList?.add('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus.classList.add('hidden');


                        break;
                    case 'COMUNICARE TIPARITA':
                        btnTiparireComunicare.classList.remove('hidden');
                        btnValidareComunicare.classList.remove('hidden');
                        btnEditareComunicare.classList.remove('hidden');
                        btnTiparireProcesVerbal.classList.add('hidden');
                        btnValidareProcesVerbal.classList.add('hidden');
                        btnIntroducereNote.classList.add('hidden');
                        btnValidarePvRezultate.classList.add('hidden');
                        btnPrintarePvRezultate.classList.add('hidden');
                        btnEditarePvRezultate.classList.add('hidden');
                        fieldsNrAut.classList.add('hidden');
                        btnGenerareAut?.classList?.add('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus.classList.remove('hidden');

                        break;
                    case 'COMUNICARE VALIDATA':
                        btnTiparireComunicare.classList.add('hidden');
                        btnValidareComunicare.classList.add('hidden');
                        btnEditareComunicare.classList.add('hidden');
                        btnTiparireProcesVerbal.classList.remove('hidden');
                        btnValidareProcesVerbal.classList.add('hidden');
                        btnIntroducereNote.classList.add('hidden');
                        btnValidarePvRezultate.classList.add('hidden');
                        btnPrintarePvRezultate.classList.add('hidden');
                        btnEditarePvRezultate.classList.add('hidden');
                        fieldsNrAut.classList.add('hidden');
                        btnGenerareAut?.classList?.add('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus.classList.remove('hidden');


                        break;
                    case 'PV TIPARIT':
                        btnTiparireComunicare.classList.add('hidden');
                        btnValidareComunicare.classList.add('hidden');
                        btnEditareComunicare.classList.add('hidden');
                        btnTiparireProcesVerbal.classList.remove('hidden');
                        btnValidareProcesVerbal.classList.remove('hidden');
                        btnIntroducereNote.classList.add('hidden');
                        btnValidarePvRezultate.classList.add('hidden');
                        btnPrintarePvRezultate.classList.add('hidden');
                        btnEditarePvRezultate.classList.add('hidden');
                        fieldsNrAut.classList.add('hidden');
                        btnGenerareAut?.classList?.add('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus.classList.remove('hidden');


                        break;
                    case 'PV VALIDAT':
                        btnTiparireComunicare.classList.add('hidden');
                        btnValidareComunicare.classList.add('hidden');
                        btnEditareComunicare.classList.add('hidden');
                        btnTiparireProcesVerbal.classList.add('hidden');
                        btnValidareProcesVerbal.classList.add('hidden');
                        btnValidarePvRezultate.classList.add('hidden');
                        btnPrintarePvRezultate.classList.add('hidden');
                        btnEditarePvRezultate.classList.add('hidden');
                        fieldsNrAut.classList.add('hidden');
                        btnGenerareAut?.classList?.add('hidden');

                        btnIntroducereNote.classList.remove('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus.classList.remove('hidden');

                        break;

                    case 'REZULTATE INTRODUSE':
                        btnTiparireComunicare.classList.add('hidden');
                        btnValidareComunicare.classList.add('hidden');
                        btnEditareComunicare.classList.add('hidden');
                        btnTiparireProcesVerbal.classList.add('hidden');
                        btnValidareProcesVerbal.classList.add('hidden');
                        btnIntroducereNote.classList.add('hidden');
                        fieldsNrAut.classList.add('hidden');
                        btnGenerareAut?.classList?.add('hidden');

                        btnPrintarePvRezultate.classList.remove('hidden');
                        btnValidarePvRezultate.classList.add('hidden');
                        btnEditarePvRezultate.classList.remove('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus.classList.remove('hidden');


                        break;

                    case 'PV REZULTATE PRINTAT':
                        btnTiparireComunicare.classList.add('hidden');
                        btnValidareComunicare.classList.add('hidden');
                        btnEditareComunicare.classList.add('hidden');
                        btnTiparireProcesVerbal.classList.add('hidden');
                        btnValidareProcesVerbal.classList.add('hidden');
                        btnIntroducereNote.classList.add('hidden');
                        fieldsNrAut.classList.add('hidden');
                        btnGenerareAut?.classList?.add('hidden');

                        btnPrintarePvRezultate.classList.remove('hidden');
                        btnValidarePvRezultate.classList.remove('hidden');
                        btnEditarePvRezultate.classList.remove('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus.classList.remove('hidden');

                        break;

                    case 'PV REZULTATE VALIDATE':
                        btnTiparireComunicare.classList.add('hidden');
                        btnValidareComunicare.classList.add('hidden');
                        btnEditareComunicare.classList.add('hidden');
                        btnTiparireProcesVerbal.classList.add('hidden');
                        btnValidareProcesVerbal.classList.add('hidden');
                        btnValidarePvRezultate.classList.add('hidden');
                        btnPrintarePvRezultate.classList.add('hidden');
                        btnEditarePvRezultate.classList.add('hidden');
                        btnIntroducereNote.classList.add('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus?.classList.remove('hidden');
                        console.log('selectedRowTipAut', selectedRowTipAut);
                        console.log('selectedRowId', selectedRowId);
                        if (selectedRowTipAut?.toLowerCase() === 'examinare' || selectedRowTipAut
                            ?.toLowerCase() === 'reexaminare' || selectedRowTipAut
                            ?.toLowerCase() === 'reprogramare') {
                            console.log('a intrat in if');
                            fieldsNrAut.classList.remove('hidden');
                            btnGenerareAut?.classList?.add('hidden');
                        } else {
                            fieldsNrAut.classList.add('hidden');
                            btnGenerareAut?.classList?.remove('hidden');
                        }

                        break;

                    case 'NR AUTORIZARE INTRODUS':
                        btnTiparireComunicare.classList.add('hidden');
                        btnValidareComunicare.classList.add('hidden');
                        btnEditareComunicare.classList.add('hidden');
                        btnTiparireProcesVerbal.classList.add('hidden');
                        btnValidareProcesVerbal.classList.add('hidden');
                        btnIntroducereNote.classList.add('hidden');
                        btnValidarePvRezultate.classList.add('hidden');
                        btnPrintarePvRezultate.classList.add('hidden');
                        btnEditarePvRezultate.classList.add('hidden');
                        fieldsNrAut.classList.add('hidden');
                        console.log('nr autorizare introdus case');
                        btnGenerareAut?.classList?.remove('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus.classList.remove('hidden');

                        break;

                    default:
                        btnTiparireComunicare.classList.add('hidden');
                        btnValidareComunicare.classList.add('hidden');
                        btnEditareComunicare.classList.add('hidden');
                        btnTiparireProcesVerbal.classList.add('hidden');
                        btnValidareProcesVerbal.classList.add('hidden');
                        btnIntroducereNote.classList.add('hidden');
                        btnValidarePvRezultate.classList.add('hidden');
                        btnPrintarePvRezultate.classList.add('hidden');
                        btnEditarePvRezultate.classList.add('hidden');
                        fieldsNrAut.classList.add('hidden');
                        btnGenerareAut?.classList?.add('hidden');
                        btnStergereInreg?.classList.remove('hidden');
                        btnIntoarecereStatus.classList.remove('hidden');

                }
                if (selectedRowStatus === 'NR AUTORIZARE INTRODUS' && selectedRowSerieAut &&
                    selectedRowNrAut) {
                    btnTiparireComunicare.classList.add('hidden');
                    btnValidareComunicare.classList.add('hidden');
                    btnEditareComunicare.classList.add('hidden');
                    btnTiparireProcesVerbal.classList.add('hidden');
                    btnValidareProcesVerbal.classList.add('hidden');
                    btnIntroducereNote.classList.add('hidden');
                    btnValidarePvRezultate.classList.add('hidden');
                    btnPrintarePvRezultate.classList.add('hidden');
                    btnEditarePvRezultate.classList.add('hidden');
                    fieldsNrAut.classList.add('hidden');
                    btnGenerareAut?.classList?.remove('hidden');
                    btnStergereInreg?.classList.remove('hidden');
                    btnIntoarecereStatus.classList.remove('hidden');
                }

            } else {
                btnTiparireComunicare.classList.add('hidden');
                btnValidareComunicare.classList.add('hidden');
                btnEditareComunicare.classList.add('hidden');
                btnTiparireProcesVerbal.classList.add('hidden');
                btnValidareProcesVerbal.classList.add('hidden');
                btnIntroducereNote.classList.add('hidden');
                btnValidarePvRezultate.classList.add('hidden');
                btnPrintarePvRezultate.classList.add('hidden');
                btnEditarePvRezultate.classList.add('hidden');
                fieldsNrAut.classList.add('hidden');
                btnGenerareAut?.classList?.add('hidden');
                btnStergereInreg?.classList.add('hidden');
                btnIntoarecereStatus.classList.add('hidden');

            }
        };

        radios.forEach(radio => {
            radio.addEventListener('change', function() {
                updateButtonVisibility();
            });
        });
        const updateRowStatus = (status) => {
            const checkedRadio = document.querySelector('.row-radio:checked');
            console.log('updating row status PF');
            if (checkedRadio) {
                console.log('checked radio', checkedRadio);
                // Get the parent row of the checked radio button
                const selectedRow = checkedRadio.closest('tr');

                const newStatus = status;

                // Update the data-status attribute of the radio button
                checkedRadio.setAttribute('data-status', newStatus);
                console.log('newStatus', newStatus);
                // Find the status column inside the same row (if there's a specific status column, adjust the selector accordingly)
                const statusColumn = selectedRow.querySelector('[data-column="status"]');
                console.log('status-col', statusColumn);
                if (statusColumn) {
                    statusColumn.textContent = newStatus; // Update the visible text
                }
            }
        }
        const updateRowStatusPJ = (status, id_solicitare_pj, isForSingleRecord = false) => {
            console.log('updating row status PJ');

            // Select all rows that have the matching data-id-solicit-pj attribute
            let inputs = [];
            if (isForSingleRecord) {
                inputs = document.querySelectorAll(`input[data-row-id="${id_solicitare_pj}"]`);
            } else {
                inputs = document.querySelectorAll(`input[data-id-solicit-pj="${id_solicitare_pj}"]`);
            }
            console.log('inputs', inputs);
            if (inputs.length === 0) {
                console.log(`No rows found with id_solicitare_pj: ${id_solicitare_pj}`);
                return;
            }

            // Loop over each row and update its status
            inputs.forEach(input => {
                console.log('current radio', input);
                // Get the parent row of the checked radio button
                const selectedRow = input.closest('tr');

                const newStatus = status;

                // Update the data-status attribute of the radio button
                input.setAttribute('data-status', newStatus);

                // Find the status column inside the same row (if there's a specific status column, adjust the selector accordingly)
                const statusColumn = selectedRow.querySelector('[data-column="status"]');
                console.log('status-col', statusColumn);
                if (statusColumn) {
                    statusColumn.textContent = newStatus; // Update the visible text
                }
            });

            console.log(`Updated status of rows with id_solicitare_pj: ${id_solicitare_pj} to ${status}`);
        };

        const deleteSelectedRow = () => {
            const checkedRadio = document.querySelector('.row-radio:checked');
            if (checkedRadio) {
                const selectedRow = checkedRadio.closest('tr');
                if (selectedRow) {
                    selectedRow.remove();
                    console.log('Deleted selected row');
                }
            }
        };

        const deleteSelectedRowPJ = (id_solicitare_pj) => {
            console.log('Deleting row(s) for PJ:', id_solicitare_pj);

            const inputs = document.querySelectorAll(`input[data-id-solicit-pj="${id_solicitare_pj}"]`);
            if (inputs.length === 0) {
                console.log(`No rows found with id_solicitare_pj: ${id_solicitare_pj}`);
                return;
            }

            inputs.forEach(input => {
                const selectedRow = input.closest('tr');
                if (selectedRow) {
                    selectedRow.remove();
                    console.log('Deleted row:', selectedRow);
                }
            });
        };


        btnTiparireComunicare.addEventListener('click', function() {
            if (selectedRowId) {
                fetch(`/${selectedRowTipPers?'generateComunicarePersJur':'generateComunicarePersFiz'}/${selectedRowId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Success:', data);
                        // Decode the base64 PDF content
                        const pdfContent = atob(data.pdf);
                        // Convert the decoded content to an array buffer
                        const arrayBuffer = new Uint8Array([...pdfContent].map(char => char
                            .charCodeAt(0))).buffer;
                        // Create a Blob from the array buffer
                        const pdfBlob = new Blob([arrayBuffer], {
                            type: 'application/pdf'
                        });
                        // Create a URL for the Blob
                        const pdfUrl = URL.createObjectURL(pdfBlob);
                        // Create a link element to trigger the download
                        const link = document.createElement('a');
                        link.href = pdfUrl;
                        link.download = 'comunicare.pdf';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        console.log('data', data);
                        // Show the validation button if the status is 'COMUNICARE TIPARITA'
                        if (data.status === 'COMUNICARE TIPARITA') {
                            // btnValidareComunicare.classList.remove('hidden');
                            console.log('selectedRowTipPers', selectedRowTipPers);
                            selectedRowTipPers ? updateRowStatusPJ(
                                    'COMUNICARE TIPARITA', selectedRowIdSolicitPj) :
                                updateRowStatus('COMUNICARE TIPARITA');
                            updateButtonVisibility();


                        } else {
                            // btnValidareComunicare.classList.add('hidden');
                            updateButtonVisibility();

                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            }
        });

        btnValidareComunicare.addEventListener('click', function() {
            if (selectedRowId) {
                fetch(`/validareComunicare${selectedRowTipPers?`Pj/${selectedRowIdSolicitPj}`:`Pf/${selectedRowId}`}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            oameni: selectedRowId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Validation Success:', data);
                        if (data.requestStatus === 200) {
                            alert('Comunicare validată cu succes!');

                            selectedRowTipPers ? updateRowStatusPJ(
                                    data.status, selectedRowIdSolicitPj) :
                                updateRowStatus(data.status);
                            updateButtonVisibility();

                        } else {
                            updateButtonVisibility();

                        }
                    })
                    .catch(error => {
                        console.error('Validation Error:', error);
                    });
            }
        });

        btnEditareComunicare.addEventListener('click', function() {
            if (selectedRowId !== null) {
                // Redirecționează către ruta de editare cu id-ul permisului selectat
                window.location.href =
                    `/solicitari/edit${selectedRowTipPers?`Pj/${selectedRowId}`:`Pf/${selectedRowId}`}`;
            }
        });

        btnTiparireProcesVerbal.addEventListener('click',
            function() { //aici selectez in functie de IDpj sau IDpf ce pdf generez
                if (selectedRowId) {
                    fetch(`/generatePv${selectedRowTipPers?`Pj/${selectedRowIdSolicitPj}`:`Pf/${selectedRowId}`}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('Success:', data);
                            // Decode the base64 PDF content
                            const pdfContent = atob(data.pdf);
                            // Convert the decoded content to an array buffer
                            const arrayBuffer = new Uint8Array([...pdfContent].map(char => char
                                .charCodeAt(0))).buffer;
                            // Create a Blob from the array buffer
                            const pdfBlob = new Blob([arrayBuffer], {
                                type: 'application/pdf'
                            });
                            // Create a URL for the Blob
                            const pdfUrl = URL.createObjectURL(pdfBlob);
                            // Create a link element to trigger the download
                            const link = document.createElement('a');
                            link.href = pdfUrl;
                            link.download = 'proces_verbal.pdf';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            if (data.status === 200) {
                                console.log('generare cu succes')
                                // btnValidareProcesVerbal.classList.remove('hidden');
                                selectedRowTipPers ? updateRowStatusPJ(
                                        'PV TIPARIT', selectedRowIdSolicitPj) :
                                    updateRowStatus('PV TIPARIT');
                                updateButtonVisibility();

                            } else {
                                // btnValidareProcesVerbal.classList.add('hidden');
                                updateButtonVisibility();

                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                        });
                }
            });

        btnValidareProcesVerbal.addEventListener('click', function() {
            if (selectedRowId) {
                fetch(`/validareMachetaPv${selectedRowTipPers?`Pj/${selectedRowIdSolicitPj}`:`Pf/${selectedRowId}`}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            oameni: selectedRowId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Validation Success:', data);
                        if (data.status === 200) {
                            alert('Proces verbal validat cu succes!');

                            selectedRowTipPers ? updateRowStatusPJ(
                                    'PV VALIDAT', selectedRowIdSolicitPj) :
                                updateRowStatus('PV VALIDAT');
                            updateButtonVisibility();

                            // Hide the buttons after successful validation
                            // btnTiparireComunicare.classList.add('hidden');
                            // btnValidareComunicare.classList.add('hidden');
                            // btnTiparireProcesVerbal.classList.add('hidden');
                            // btnValidareProcesVerbal.classList.add('hidden');
                            // fieldsNrAut.classList.remove('hidden');
                            // btnGenerareAut.classList.add('hidden');


                        } else {
                            updateButtonVisibility();

                        }
                    })
                    .catch(error => {
                        console.error('Validation Error:', error);
                    });
            }
        });

        btnIntroducereNote.addEventListener('click', function() {
            if (selectedRowId) {
                window.location.href =
                    `/rezultateExamen${selectedRowTipPers?`Pj/${selectedRowIdSolicitPj}`:`Pf/${selectedRowId}`}`;
            }
        });

        btnPrintarePvRezultate.addEventListener('click', function() {
            if (selectedRowId) {
                fetch(`/generateRezultatePv${selectedRowTipPers?`Pj/${selectedRowIdSolicitPj}`:`Pf/${selectedRowId}`}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Success:', data);
                        // Decode the base64 PDF content
                        const pdfContent = atob(data.pdf);
                        // Convert the decoded content to an array buffer
                        const arrayBuffer = new Uint8Array([...pdfContent].map(char => char
                            .charCodeAt(0))).buffer;
                        // Create a Blob from the array buffer
                        const pdfBlob = new Blob([arrayBuffer], {
                            type: 'application/pdf'
                        });
                        // Create a URL for the Blob
                        const pdfUrl = URL.createObjectURL(pdfBlob);
                        // Create a link element to trigger the download
                        const link = document.createElement('a');
                        link.href = pdfUrl;
                        link.download = 'rezultate_proces_verbal.pdf';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        if (data.status === 200) {
                            console.log('generare cu succes')
                            // btnValidareProcesVerbal.classList.remove('hidden');
                            selectedRowTipPers ? updateRowStatusPJ(
                                    'PV REZULTATE PRINTAT', selectedRowIdSolicitPj) :
                                updateRowStatus('PV REZULTATE PRINTAT');
                            updateButtonVisibility();

                        } else {
                            // btnValidareProcesVerbal.classList.add('hidden');
                            updateButtonVisibility();

                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            }
        });

        btnEditarePvRezultate.addEventListener('click', function() {
            if (selectedRowId !== null) {
                // Redirecționează către ruta de editare cu id-ul permisului selectat
                window.location.href =
                    `/rezultateExamen/edit${selectedRowTipPers?`Pj/${selectedRowId}`:`Pf/${selectedRowId}`}`;
            }
        });

        btnValidarePvRezultate.addEventListener('click', function() {
            if (selectedRowId) {
                fetch(`/validareRezultatePv${selectedRowTipPers?`Pj/${selectedRowIdSolicitPj}`:`Pf/${selectedRowId}`}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            oameni: selectedRowId,
                            tipComisieAutAnterioara: selectedRowTipComisieAutAnterioara
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Validation Success:', data);
                        if (data.requestStatus === 200) {
                            alert('Proces verbal validat cu succes!');
                            console.log('Received status', status);
                            selectedRowTipPers ? updateRowStatusPJ(
                                    data.status, selectedRowIdSolicitPj) :
                                updateRowStatus(data.status);
                            updateButtonVisibility();


                        } else {
                            updateButtonVisibility();

                        }
                    })
                    .catch(error => {
                        console.error('Validation Error:', error);
                    });
            }
        });

        btnConfirmareNrAut.addEventListener('click', function() {
            if (selectedRowId && inputNrAut.value) {
                fetch(`/introducereNr_aut`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            oameni: selectedRowId,
                            nr_aut: inputNrAut.value
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Validation Success:', data);
                        if (data.status === 200) {
                            alert('Număr autorizație introdus cu succes!');

                            updateRowStatus('NR AUTORIZARE INTRODUS');
                            updateButtonVisibility();
                            inputNrAut.textContent = '';
                        } else if (data.status === 409) {
                            alert(data.message); // Afișează mesajul de duplicat
                        }
                    })
                    .catch(error => {
                        console.error('Validation Error:', error);
                    });
            }
        });

        btnGenerareAut && btnGenerareAut.addEventListener('click', function() {
            if (selectedRowId) {
                fetch(`/generateAutorizatie/${selectedRowId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            oameni: selectedRowId,
                            nr_aut: inputNrAut.value
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Success:', data);
                        for (const pdf of data.pdfs) {


                            // Decode the base64 PDF content
                            const pdfContent = atob(pdf);
                            // Convert the decoded content to an array buffer
                            const arrayBuffer = new Uint8Array([...pdfContent].map(char => char
                                .charCodeAt(0))).buffer;
                            // Create a Blob from the array buffer
                            const pdfBlob = new Blob([arrayBuffer], {
                                type: 'application/pdf'
                            });
                            // Create a URL for the Blob
                            const pdfUrl = URL.createObjectURL(pdfBlob);
                            // Create a link element to trigger the download
                            const link = document.createElement('a');
                            link.href = pdfUrl;
                            link.download = `autorizatie_${selectedRowNumePrenume}.pdf`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        }
                        if (data.status === 200) {
                            console.log('Autorizatie generata cu succes');
                            // btnGenerareAut.classList.add('hidden');
                            selectedRowTipPers ? updateRowStatusPJ(
                                    'AVIZE GENERATE', selectedRowId, true) :
                                updateRowStatus('AVIZE GENERATE');
                            updateButtonVisibility();
                        } else {
                            // btnGenerareAut.classList.remove('hidden');
                            alert(data?.message ||
                                'Nu s-a putut trece la pasul urmator. Probabil nu ati emis lista.'
                            );
                            updateButtonVisibility();


                        }
                    })
                    .catch(error => {
                        alert('Nu s-a putut trece la pasul urmator.');

                        console.error('Validation Error:', error);
                    });
            }
        });

        btnPrintareAutorizatiiEmise.addEventListener('click',
            function() { //aici selectez in functie de IDpj sau IDpf ce pdf generez
                fetch(`/generateAutorizatiiEmise`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            nr_ISF: document.getElementById('inputNrIesire').value,
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Success:', data);
                        // Decode the base64 PDF content
                        const pdfContent = atob(data.pdf);
                        // Convert the decoded content to an array buffer
                        const arrayBuffer = new Uint8Array([...pdfContent].map(char => char
                            .charCodeAt(0))).buffer;
                        // Create a Blob from the array buffer
                        const pdfBlob = new Blob([arrayBuffer], {
                            type: 'application/pdf'
                        });
                        // Create a URL for the Blob
                        const pdfUrl = URL.createObjectURL(pdfBlob);
                        // Create a link element to trigger the download
                        const link = document.createElement('a');
                        link.href = pdfUrl;
                        link.download = 'autorizatii_emise.pdf';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        if (data.status === 200) {
                            console.log('generare cu succes')
                            // btnValidareProcesVerbal.classList.remove('hidden');
                            // selectedRowTipPers ? updateRowStatusPJ(
                            //         'PV TIPARIT', selectedRowIdSolicitPj) :
                            //     updateRowStatus('PV TIPARIT');
                            // updateButtonVisibility();

                        } else {
                            // btnValidareProcesVerbal.classList.add('hidden');
                            updateButtonVisibility();

                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            });

        btnStergereInreg?.addEventListener('click',
            function() {
                if (!confirm("Confirmi ștergerea înregistrării?")) {
                    return;
                }
                fetch(`/stergereInreg${selectedRowTipPers?`Pj/${selectedRowIdSolicitPj}`:`Pf/${selectedRowId}`}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {

                        if (data.status === 200) {
                            console.log('inreg stearsa cu succes');

                            selectedRowTipPers ? deleteSelectedRowPJ(selectedRowIdSolicitPj) :
                                deleteSelectedRow();


                            alert('Înregistrare ștearsă cu succes');

                        } else {

                            alert('Înregistrarea nu a fost ștearsă');


                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            });

        btnIntoarecereStatus.addEventListener('click',
            function() {
                if (!confirm("Întorci statusul?")) {
                    return;
                }
                fetch(`/intoarcereStatus${selectedRowTipPers?`Pj/${selectedRowId}`:`Pf/${selectedRowId}`}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {

                        if (data.status === 200) {
                            console.log('Status întors cu succes.');
                            alert('Status întors cu succes.');

                        } else {

                            alert('Status-ul nu a fost întors. A apărut o eroare.');


                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            });

        radios.forEach(radio => {
            const status = radio.getAttribute('data-status');
            if (status === 'COMUNICARE VALIDATA') {
                radio.checked = true;
                updateButtonVisibility();
            }
        });

        // Initial update on page load
        updateButtonVisibility();
    });
</script>
