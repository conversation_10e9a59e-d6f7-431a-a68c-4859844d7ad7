<x-app-layout>
    <div>
        <form class="mt-10 max-w-xl mx-auto flex flex-col" action="{{ route('unitatiferoviare.store') }}" method="POST">
            @csrf
            <div class="mb-5">
                <label for="denumire"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Denumire</label>
                <input type="text" id="denumire" name="denumire"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    required />
                @error('denumire')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
            <div class="mb-5 flex flex-row gap-5">
                <div class="flex-1">
                    <label for="atributFiscal"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Atribut
                        fiscal</label>
                    <input type="text" value='RO' id="atributFiscal" name='atributFiscal'
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                    @error('atributFiscal')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
                <div class="flex-1">
                    <label for="cif" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Cod de
                        identificare fiscala</label>
                    <input type="text" id="cif" name="cif"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                    @error('cif')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="mb-5 flex flex-row gap-5">
                <div class="flex-1">
                    <label for="localitate"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Localitate</label>
                    <input type="text" id="localitate" name="localitate"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                    @error('localitate')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
                <div class="flex-1">
                    <label for="tara"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Țară</label>
                    <input type="text" value="România" id="tara" name="tara"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                    @error('tara')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="mb-5">
                <label for="adresa"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Adresă</label>
                <input type="text" id="adresa" name="adresa"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    required />
                @error('adresa')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
            <div class="mb-5 flex flex-row gap-5">
                <div class="flex-1">
                    <label for="telefon"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Telefon</label>
                    <input type="tel" id="telefon" name="telefon"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                    @error('telefon')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
                <div class="flex-1">
                    <label for="fax"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Fax</label>
                    <input type="text" id="fax" name="fax"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required />
                    @error('fax')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <button type="submit"
                class="text-white mt-10 mb-5 bg-green-500 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-green-600 dark:hover:bg-green-600 dark:focus:ring-green-800">Validare</button>
            <button type="button" onclick="window.history.back()"
                class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-red-600 dark:hover:bg-red-600 dark:focus:ring-red-800">Anulare</button>
        </form>
    </div>
</x-app-layout>
