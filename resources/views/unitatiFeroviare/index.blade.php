@props(['menus', 'dateTabel', 'columns'])

<x-app-layout>
    <x-slot name="header">
        <nav
            class="flex justify-center items-center flex-wrap gap-1 bg-gray-200 dark:bg-gray-800 p-4 font-semibold text-xl dark:text-white text-black dark:text-gray-200 leading-tight">
            @foreach ($menus as $menu)
                <x-custom-dropdown align="left" width="48" contentClasses="py-1 bg-white dark:bg-gray-800"
                    :menuObj="$menu">
                </x-custom-dropdown>
            @endforeach
        </nav>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (isset($columns) && isset($dateTabel))
                <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3 sticky">Action</th>
                                @foreach ($columns as $column)
                                    <th scope="col" class="px-6 py-3">{{ $column }}</th>
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($dateTabel as $dataRow)
                                <tr
                                    class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <td class="px-6 py-4 sticky">
                                        <input type="radio" name="row-radio" class="row-radio"
                                            data-row-id="{{ $dataRow->oameni }}" data-status="{{ $dataRow->status }}"
                                            data-tip-pers="{{ $dataRow->unitate }}" data-nr-aut="{{ $dataRow->nr_aut }}"
                                            data-serie-aut="{{ $dataRow->serie_aut }}"
                                            data-tip-aut="{{ $dataRow->tip_comisie }}"
                                            data-id-solicit-pj="{{ $dataRow->id_solicitare_pj }}">
                                    </td>
                                    @foreach ($columns as $column)
                                        <td class="px-6 py-4" data-column="{{ $column }}">
                                            {{ $dataRow->$column }}</td>
                                    @endforeach
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $dateTabel->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>