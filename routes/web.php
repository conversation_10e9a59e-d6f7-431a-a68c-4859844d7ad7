<?php

use App\Http\Controllers\autorizatiiEmiseController;
use App\Http\Controllers\ComunicareMetrorexController;
use App\Http\Controllers\facturareController;
use App\Http\Controllers\machetaPvController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UnitatiFeroviareController;


use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MainController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\solicitariController;
use App\Http\Controllers\ComunicareController;
use App\Http\Controllers\permisController;
use App\Http\Controllers\autorizatieController;
use App\Http\Controllers\autorizatieMetrorexController;
use App\Http\Controllers\RezultateExamenController;
use App\Http\Controllers\RezultatePvController;
use App\Http\Controllers\PermisMetrorexController;
use App\Http\Controllers\AutorizatiiSpecialeController;


Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    $menus = config('customVariables.menus');
    return view('dashboard', compact('menus'));
})->middleware(['auth', 'verified'])->name('dashboard');

// Route::middleware(['role:admin'])->group(function () {
Route::get('/unitatiferoviare', [UnitatiFeroviareController::class, 'index'])->name('unitatiferoviare');
Route::get('/unitatiferoviare/create', [UnitatiFeroviareController::class, 'create'])->name('unitatiferoviare.create');
Route::post('/unitatiferoviare/getPaginated', [UnitatiFeroviareController::class, 'getPaginated'])->name('unitatiferoviare.getPaginated');
Route::post('/unitatiferoviare/store', [UnitatiFeroviareController::class, 'store'])->name('unitatiferoviare.store');
// });


Route::middleware(['auth'])->group(function () {
    Route::get('/utilizatori', [UserController::class, 'index'])->name('users.index');
    Route::put('/utilizatori', [UserController::class, 'update'])->name('users.update');
    Route::get('/dashboard/users/create', 'UserController@create')->name('users.create');
    Route::post('/dashboard/users', 'UserController@store')->name('users.store');
    Route::get('/dashboard/users/{user}/edit', 'UserController@edit')->name('users.edit');
    Route::delete('/dashboard/users/{user}', 'UserController@destroy')->name('users.destroy');
});

// Route::get('/dashboards/users', function () {
//     return view('admin.usermanagement');
// })->name('users.index');

// REZULTATE EXAMEN
Route::get('/rezultateExamenPf/{id_solicitare}', [RezultateExamenController::class, 'createPf'])->name('rezultate_examen.createPf');
Route::get('/rezultateExamenPj/{id_solicitare_pj}', [RezultateExamenController::class, 'createPj'])->name('rezultate_examen.createPj');
Route::post('/storeRezultateExamen', [RezultateExamenController::class, 'store']);
Route::get('/rezultateExamen/editPf/{oameni}', [RezultateExamenController::class, 'editPf'])->name('rezultate_examen.edit');
Route::get('/rezultateExamen/editPj/{oameni}', [RezultateExamenController::class, 'editPj'])->name('rezultate_examen.edit');
Route::post('/rezultateExamen/update', [RezultateExamenController::class, 'update'])->name('rezultate_examen.update');
// Route::post('/rezultateExamen/updatePj/{id_solicitare_pj}', [RezultateExamenController::class, 'editPj'])->name('rezultate_examen.edit');


Route::get('/generateRezultatePvPf/{id_solicitare}', [RezultatePvController::class, 'generatePDFpf']);
Route::get('/generateRezultatePvPj/{id_solicitare_pj}', [RezultatePvController::class, 'generatePDFpj']);
Route::post('/validareRezultatePvPf/{id_solicitare}', [RezultatePvController::class, 'validareRezultatPf']);
Route::post('/validareRezultatePvPj/{id_solicitare_pj}', [RezultatePvController::class, 'validareRezultatPj']);


Route::post('/generateComunicarePersFiz/{oameni}', [ComunicareController::class, 'generatePersFizicaPDF']);
Route::post('/generateComunicarePersJur/{oameni}', [ComunicareController::class, 'generatePersJuridicaPDF']);

Route::middleware(['role:super_admin'])->group(function () {
    Route::get('/permisMetrorex/create', [PermisMetrorexController::class, 'create'])->name('permisMetrorex.create');
    Route::get('/permisMetrorex/vizualizare', [PermisMetrorexController::class, 'index'])->name('permisMetrorex.vizualizare');
    Route::get('/permisMetrorex/edit/{id}', [PermisMetrorexController::class, 'edit'])->name('permisMetrorex.edit');
    Route::post('/generateAutorizatieMetrorex/{id}', [autorizatieMetrorexController::class, 'generatePDF']);
    Route::get('/generateComunicareMetrorexPf/{id}', [ComunicareMetrorexController::class, 'generatePersFizicaPDF']);
    Route::post('/solicitariMetrorex/store', [PermisMetrorexController::class, 'store'])->name('store');
    Route::get('/permisMetrorex/getLastId', [PermisMetrorexController::class, 'getLastId']);
});


Route::post('/validareComunicarePf/{oameni}', [ComunicareController::class, 'validareComunicarePf']);
Route::post('/validareComunicarePj/{id_solicitare_pj}', [ComunicareController::class, 'validareComunicarePj']);

Route::post('/validareMachetaPvPf/{oameni}', [machetaPvController::class, 'validareMachetaPvPf']);
Route::post('/validareMachetaPvPj/{id_solicitare_pj}', [machetaPvController::class, 'validareMachetaPvPj']);

Route::post('/generateAutorizatiiEmise', [autorizatiiEmiseController::class, 'generateAutorizatiiEmise']);
Route::post('/generatePvPf/{oameni}', [machetaPvController::class, 'generatePDFpf']);
Route::post('/generatePvPj/{id_solicitare_pj}', [machetaPvController::class, 'generatePDFpj']);

Route::post('/generatePermis/{id}', [permisController::class, 'generatePDF']);
Route::get('/generateFactura/{id}', [facturareController::class, 'generatePDF']);

Route::post('/generateAutorizatie/{id}', [autorizatieController::class, 'generatePDF']);
Route::middleware(['role:super_admin'])->group(function () {
    Route::post('/stergereInregPf/{oameni}', [solicitariController::class, 'stergereInregPf']);
    Route::post('/stergereInregPj/{id_solicitare_pj}', [solicitariController::class, 'stergereInregPj']);
    Route::post('/intoarcereStatusPf/{oameni}', [solicitariController::class, 'intoarcereStatusPf']);
    Route::post('/intoarcereStatusPj/{oameni}', [solicitariController::class, 'intoarcereStatusPj']);
});

// Route::get('/generateComunicareMetrorexPj/{id}', [ComunicareMetrorexController::class, 'generatePersJuridicaPDF']);
Route::post('/introducereNr_aut', [autorizatieController::class, 'introducereNr_aut']);
Route::get('/solicitari', [solicitariController::class, 'index'])->name('solicitari.index');
Route::get('/solicitariGrouped', [solicitariController::class, 'indexGrouped'])->name('solicitariGrouped.index');
Route::get('/totDespreOPersoana', [solicitariController::class, 'indexTotDespreOPersoana'])->name('totDespreOPersoana.indexTotDespreOPersoana');
Route::get('/validateCnpAndGetAutorizatii/{cnp}/{tipSolicitare}', [solicitariController::class, 'validateCnpAndGetAutorizatii'])->name('validateCnpAndGetAutorizatii');


Route::get('/solicitari/editPf/{oameni}', [solicitariController::class, 'edit'])->name('solicitari.edit');
Route::get('/solicitari/editPj/{oameni}', [solicitariController::class, 'edit'])->name('solicitari.edit');

Route::post('/solicitari/updatePf/{oameni}', [solicitariController::class, 'updatePf'])->name('solicitari.updatePf');
Route::post('/solicitari/updatePj/{id_solicitare_pj}', [solicitariController::class, 'updatePj'])->name('solicitari.updatePj');

Route::get('/solicitari/create', [solicitariController::class, 'create'])->name('solicitari.create');
Route::post('/solicitari/getTipAutorizatii', [solicitariController::class, 'getTipAutorizatii'])->name('solicitari.getTipAutorizatii');
Route::post('/solicitari/storePersFizica', [solicitariController::class, 'storePersFizica'])->name('solicitari.storePersFizica');
Route::post('/solicitari/storePersJuridica', [solicitariController::class, 'storePersJuridica'])->name('solicitari.storePersJuridica');







Route::get('/personal', [solicitariController::class, 'index'])->name('personal.index');












Route::get('/permise', [permisController::class, 'index'])->name('permise.index');
Route::get('/permise/create', [permisController::class, 'create'])->name('permise.create');
Route::post('/permise/check', [permisController::class, 'check'])->name('permise.check');
Route::post('/permise/store', [permisController::class, 'store'])->name('permise.store');
Route::get('/permise/edit/{id}', [permisController::class, 'edit'])->name('permise.edit');
Route::get('/permise/search', [permisController::class, 'search'])->name('permise.search');
Route::put('/permise/update/{id}', [permisController::class, 'update'])->name('permise.update');
Route::get('/permise/vizualizare', [permisController::class, 'index'])->name('permise.vizualizare');
Route::get('/permise/modificarePermis', [permisController::class, 'modificarePermis'])->name('permise.modificarePermis');
Route::get('/permise/acordareNumarPermis', [permisController::class, 'acordareNumarPermis'])->name('permise.acordareNumarPermis');
Route::get('/permise/permisNeacordat', [permisController::class, 'permisNeacordat'])->name('permise.permisNeacordat');
Route::get('/permise/permisDublat', [permisController::class, 'permisDublat'])->name('permise.permisDublat');
Route::get('/permise/cardNeacordat', [permisController::class, 'cardNeacordat'])->name('permise.cardNeacordat');
Route::get('/permise/cardDublat', [permisController::class, 'cardDublat'])->name('permise.cardDublat');// routes/web.php
Route::post('/permise/validare/{id}', [permisController::class, 'validare'])->name('permise.validare');



Route::get('/autorizatiiSpeciale', [AutorizatiiSpecialeController::class, 'autorizatiiSpeciale']);
Route::post('/autorizatiiSpeciale/store', [AutorizatiiSpecialeController::class, 'store'])->name('store');
Route::get('/autorizatiiSpeciale/pdf/{id}', [AutorizatiiSpecialeController::class, 'genereazaPdf']);
Route::get('/autorizatiiSpeciale/vizualizare', [AutorizatiiSpecialeController::class, 'index'])->name('autorizatiiSpeciale.vizualizare');




Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/main', [MainController::class, 'index'])->name('main');
});

require __DIR__ . '/auth.php';
